import { Pool } from "pg";

// Use the full DATABASE_URL if available, otherwise fallback to Supabase
const databaseUrl = process.env.DATABASE_URL || process.env.SUPABASE_DATABASE_URL;

console.log('databaseUrl%s',databaseUrl)
if (!databaseUrl) {
  throw new Error('DATABASE_URL or SUPABASE_DATABASE_URL environment variable is required');
}

export default {
  database: new Pool({
    connectionString: databaseUrl,
    ssl: {
      rejectUnauthorized: false, // Supabase requires SSL
    },
    max: 10,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  }),
  emailAndPassword: {
    enabled: false,
    requireEmailVerification: false,
  },
  socialProviders: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
      accessType: "offline", // Always get refresh token
      prompt: "select_account", // Always ask to select account
    },
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  user: {
    additionalFields: {
      name: {
        type: "string",
        required: false,
      },
    },
  },
  secret: process.env.BETTER_AUTH_SECRET || "fallback-secret-key-for-development",
  baseURL: process.env.BETTER_AUTH_URL || "http://localhost:3000",
};
