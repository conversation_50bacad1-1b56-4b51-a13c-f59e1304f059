-- CreateTable
CREATE TABLE "whiteboard" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "content" JSONB DEFAULT '{}',
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(6) NOT NULL,
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "allowComments" BOOLEAN NOT NULL DEFAULT true,
    "viewMode" TEXT NOT NULL DEFAULT 'edit',
    "backgroundColor" TEXT NOT NULL DEFAULT '#ffffff',
    "gridMode" BOOLEAN NOT NULL DEFAULT false,
    "snapToGrid" BOOLEAN NOT NULL DEFAULT false,
    "theme" TEXT NOT NULL DEFAULT 'light',
    "viewportX" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "viewportY" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "viewportZoom" DOUBLE PRECISION NOT NULL DEFAULT 1,

    CONSTRAINT "whiteboard_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "whiteboardNode" (
    "id" TEXT NOT NULL,
    "whiteboardId" TEXT NOT NULL,
    "nodeId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "positionX" DOUBLE PRECISION NOT NULL,
    "positionY" DOUBLE PRECISION NOT NULL,
    "width" DOUBLE PRECISION,
    "height" DOUBLE PRECISION,
    "data" JSONB NOT NULL DEFAULT '{}',
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "whiteboardNode_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "whiteboardEdge" (
    "id" TEXT NOT NULL,
    "whiteboardId" TEXT NOT NULL,
    "edgeId" TEXT NOT NULL,
    "source" TEXT NOT NULL,
    "target" TEXT NOT NULL,
    "type" TEXT NOT NULL DEFAULT 'default',
    "data" JSONB DEFAULT '{}',
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "whiteboardEdge_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "countdownTimer" (
    "id" TEXT NOT NULL,
    "whiteboardId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "endDate" TIMESTAMP(6) NOT NULL,
    "positionX" DOUBLE PRECISION NOT NULL,
    "positionY" DOUBLE PRECISION NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "color" TEXT NOT NULL DEFAULT '#000000',
    "fontSize" INTEGER NOT NULL DEFAULT 16,
    "backgroundColor" TEXT,
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "countdownTimer_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "goal" (
    "id" TEXT NOT NULL,
    "whiteboardId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "dueDate" TIMESTAMP(6),
    "priority" TEXT NOT NULL DEFAULT 'medium',
    "status" TEXT NOT NULL DEFAULT 'not-started',
    "positionX" DOUBLE PRECISION NOT NULL,
    "positionY" DOUBLE PRECISION NOT NULL,
    "tags" TEXT NOT NULL DEFAULT '',
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "goal_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "hillChart" (
    "id" TEXT NOT NULL,
    "whiteboardId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "positionX" DOUBLE PRECISION NOT NULL,
    "positionY" DOUBLE PRECISION NOT NULL,
    "width" DOUBLE PRECISION NOT NULL DEFAULT 400,
    "height" DOUBLE PRECISION NOT NULL DEFAULT 200,
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "hillChart_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "hillChartItem" (
    "id" TEXT NOT NULL,
    "hillChartId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "position" DOUBLE PRECISION NOT NULL,
    "color" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "hillChartItem_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "whiteboard_userId_idx" ON "whiteboard"("userId");

-- CreateIndex
CREATE INDEX "whiteboard_createdAt_idx" ON "whiteboard"("createdAt");

-- CreateIndex
CREATE INDEX "whiteboard_updatedAt_idx" ON "whiteboard"("updatedAt");

-- CreateIndex
CREATE INDEX "whiteboardNode_whiteboardId_idx" ON "whiteboardNode"("whiteboardId");

-- CreateIndex
CREATE UNIQUE INDEX "whiteboardNode_whiteboardId_nodeId_key" ON "whiteboardNode"("whiteboardId", "nodeId");

-- CreateIndex
CREATE INDEX "whiteboardEdge_whiteboardId_idx" ON "whiteboardEdge"("whiteboardId");

-- CreateIndex
CREATE UNIQUE INDEX "whiteboardEdge_whiteboardId_edgeId_key" ON "whiteboardEdge"("whiteboardId", "edgeId");

-- CreateIndex
CREATE INDEX "countdownTimer_whiteboardId_idx" ON "countdownTimer"("whiteboardId");

-- CreateIndex
CREATE INDEX "goal_whiteboardId_idx" ON "goal"("whiteboardId");

-- CreateIndex
CREATE INDEX "hillChart_whiteboardId_idx" ON "hillChart"("whiteboardId");

-- CreateIndex
CREATE INDEX "hillChartItem_hillChartId_idx" ON "hillChartItem"("hillChartId");

-- AddForeignKey
ALTER TABLE "whiteboard" ADD CONSTRAINT "whiteboard_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "whiteboardNode" ADD CONSTRAINT "whiteboardNode_whiteboardId_fkey" FOREIGN KEY ("whiteboardId") REFERENCES "whiteboard"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "whiteboardEdge" ADD CONSTRAINT "whiteboardEdge_whiteboardId_fkey" FOREIGN KEY ("whiteboardId") REFERENCES "whiteboard"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "countdownTimer" ADD CONSTRAINT "countdownTimer_whiteboardId_fkey" FOREIGN KEY ("whiteboardId") REFERENCES "whiteboard"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "goal" ADD CONSTRAINT "goal_whiteboardId_fkey" FOREIGN KEY ("whiteboardId") REFERENCES "whiteboard"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "hillChart" ADD CONSTRAINT "hillChart_whiteboardId_fkey" FOREIGN KEY ("whiteboardId") REFERENCES "whiteboard"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "hillChartItem" ADD CONSTRAINT "hillChartItem_hillChartId_fkey" FOREIGN KEY ("hillChartId") REFERENCES "hillChart"("id") ON DELETE CASCADE ON UPDATE CASCADE;
