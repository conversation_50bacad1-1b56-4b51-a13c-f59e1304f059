-- CreateEnum
CREATE TYPE "public"."WhiteboardRole" AS ENUM ('OWNER', 'EDITOR', 'VIEWER', 'COMMENTER');

-- CreateEnum
CREATE TYPE "public"."WhiteboardPermission" AS ENUM ('VIEW', 'EDIT', 'COMMENT', '<PERSON><PERSON><PERSON>_SETTINGS', 'MANAGE_ACCESS', 'DELETE', 'SHARE');

-- AlterTable
ALTER TABLE "public"."whiteboard" ADD COLUMN     "allowAnonymousView" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "publicAccessLevel" TEXT NOT NULL DEFAULT 'none',
ADD COLUMN     "requiresAuth" BOOLEAN NOT NULL DEFAULT true;

-- CreateTable
CREATE TABLE "public"."whiteboardUserAccess" (
    "id" TEXT NOT NULL,
    "whiteboardId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "role" "public"."WhiteboardRole" NOT NULL,
    "permissions" TEXT[],
    "grantedBy" TEXT,
    "grantedAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" TIMESTAMP(6),
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "whiteboardUserAccess_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."whiteboardShareLink" (
    "id" TEXT NOT NULL,
    "whiteboardId" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "role" "public"."WhiteboardRole" NOT NULL DEFAULT 'VIEWER',
    "permissions" TEXT[],
    "expiresAt" TIMESTAMP(6),
    "maxUses" INTEGER,
    "usedCount" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "whiteboardShareLink_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."whiteboardInvitation" (
    "id" TEXT NOT NULL,
    "whiteboardId" TEXT NOT NULL,
    "inviterUserId" TEXT NOT NULL,
    "inviteeUserId" TEXT,
    "inviteeEmail" TEXT,
    "role" "public"."WhiteboardRole" NOT NULL,
    "permissions" TEXT[],
    "status" TEXT NOT NULL DEFAULT 'pending',
    "message" TEXT,
    "expiresAt" TIMESTAMP(6) NOT NULL,
    "acceptedAt" TIMESTAMP(6),
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "whiteboardInvitation_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "whiteboardUserAccess_whiteboardId_idx" ON "public"."whiteboardUserAccess"("whiteboardId");

-- CreateIndex
CREATE INDEX "whiteboardUserAccess_userId_idx" ON "public"."whiteboardUserAccess"("userId");

-- CreateIndex
CREATE INDEX "whiteboardUserAccess_role_idx" ON "public"."whiteboardUserAccess"("role");

-- CreateIndex
CREATE UNIQUE INDEX "whiteboardUserAccess_whiteboardId_userId_key" ON "public"."whiteboardUserAccess"("whiteboardId", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "whiteboardShareLink_token_key" ON "public"."whiteboardShareLink"("token");

-- CreateIndex
CREATE INDEX "whiteboardShareLink_whiteboardId_idx" ON "public"."whiteboardShareLink"("whiteboardId");

-- CreateIndex
CREATE INDEX "whiteboardShareLink_token_idx" ON "public"."whiteboardShareLink"("token");

-- CreateIndex
CREATE INDEX "whiteboardShareLink_expiresAt_idx" ON "public"."whiteboardShareLink"("expiresAt");

-- CreateIndex
CREATE INDEX "whiteboardInvitation_whiteboardId_idx" ON "public"."whiteboardInvitation"("whiteboardId");

-- CreateIndex
CREATE INDEX "whiteboardInvitation_inviterUserId_idx" ON "public"."whiteboardInvitation"("inviterUserId");

-- CreateIndex
CREATE INDEX "whiteboardInvitation_inviteeUserId_idx" ON "public"."whiteboardInvitation"("inviteeUserId");

-- CreateIndex
CREATE INDEX "whiteboardInvitation_inviteeEmail_idx" ON "public"."whiteboardInvitation"("inviteeEmail");

-- CreateIndex
CREATE INDEX "whiteboardInvitation_status_idx" ON "public"."whiteboardInvitation"("status");

-- CreateIndex
CREATE INDEX "whiteboardInvitation_expiresAt_idx" ON "public"."whiteboardInvitation"("expiresAt");

-- CreateIndex
CREATE INDEX "whiteboard_isPublic_idx" ON "public"."whiteboard"("isPublic");

-- CreateIndex
CREATE INDEX "whiteboard_publicAccessLevel_idx" ON "public"."whiteboard"("publicAccessLevel");

-- AddForeignKey
ALTER TABLE "public"."whiteboardUserAccess" ADD CONSTRAINT "whiteboardUserAccess_whiteboardId_fkey" FOREIGN KEY ("whiteboardId") REFERENCES "public"."whiteboard"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."whiteboardUserAccess" ADD CONSTRAINT "whiteboardUserAccess_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."whiteboardShareLink" ADD CONSTRAINT "whiteboardShareLink_whiteboardId_fkey" FOREIGN KEY ("whiteboardId") REFERENCES "public"."whiteboard"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."whiteboardInvitation" ADD CONSTRAINT "whiteboardInvitation_whiteboardId_fkey" FOREIGN KEY ("whiteboardId") REFERENCES "public"."whiteboard"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."whiteboardInvitation" ADD CONSTRAINT "whiteboardInvitation_inviterUserId_fkey" FOREIGN KEY ("inviterUserId") REFERENCES "public"."user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."whiteboardInvitation" ADD CONSTRAINT "whiteboardInvitation_inviteeUserId_fkey" FOREIGN KEY ("inviteeUserId") REFERENCES "public"."user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
