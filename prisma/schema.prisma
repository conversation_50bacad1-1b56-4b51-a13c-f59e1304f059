generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = "******************************************************************************************************/postgres"
  // env("SUPABASE_DATABASE_URL")
}

model account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime? @db.Timestamp(6)
  refreshTokenExpiresAt DateTime? @db.Timestamp(6)
  scope                 String?
  password              String?
  createdAt             DateTime  @db.Timestamp(6)
  updatedAt             DateTime  @db.Timestamp(6)
  user                  user      @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model session {
  id        String   @id
  expiresAt DateTime @db.Timestamp(6)
  token     String   @unique
  createdAt DateTime @db.Timestamp(6)
  updatedAt DateTime @db.Timestamp(6)
  ipAddress String?
  userAgent String?
  userId    String
  user      user     @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model user {
  id            String       @id
  name          String?
  email         String       @unique
  emailVerified Boolean
  image         String?
  createdAt     DateTime     @db.Timestamp(6)
  updatedAt     DateTime     @db.Timestamp(6)
  account       account[]
  session       session[]
  whiteboards   whiteboard[]

  // RBAC Relations
  whiteboardAccess    whiteboardUserAccess[]
  sentInvitations     whiteboardInvitation[] @relation("InvitationSender")
  receivedInvitations whiteboardInvitation[] @relation("InvitationReceiver")
}

model whiteboard {
  id                String              @id @default(cuid())
  title             String
  content           Json?               @default("{}")
  userId            String
  createdAt         DateTime            @default(now()) @db.Timestamp(6)
  updatedAt         DateTime            @updatedAt @db.Timestamp(6)

  // Settings
  isPublic          Boolean             @default(false)
  allowComments     Boolean             @default(true)
  viewMode          String              @default("edit")
  backgroundColor   String              @default("#ffffff")
  gridMode          Boolean             @default(false)
  snapToGrid        Boolean             @default(false)
  theme             String              @default("light")

  // Access Control
  publicAccessLevel String              @default("none") // none, view, comment, edit
  requiresAuth      Boolean             @default(true)
  allowAnonymousView Boolean            @default(false)

  // Viewport
  viewportX         Float               @default(0)
  viewportY         Float               @default(0)
  viewportZoom      Float               @default(1)

  // Relations
  user              user                @relation(fields: [userId], references: [id], onDelete: Cascade)
  nodes             whiteboardNode[]
  edges             whiteboardEdge[]
  countdownTimers   countdownTimer[]
  goals             goal[]
  hillCharts        hillChart[]

  // RBAC Relations
  userAccess        whiteboardUserAccess[]
  shareLinks        whiteboardShareLink[]
  invitations       whiteboardInvitation[]

  @@index([userId])
  @@index([createdAt])
  @@index([updatedAt])
  @@index([isPublic])
  @@index([publicAccessLevel])
}

model whiteboardNode {
  id            String      @id @default(cuid())
  whiteboardId  String
  nodeId        String
  type          String
  positionX     Float
  positionY     Float
  width         Float?
  height        Float?
  data          Json        @default("{}")
  createdAt     DateTime    @default(now()) @db.Timestamp(6)
  updatedAt     DateTime    @updatedAt @db.Timestamp(6)

  whiteboard    whiteboard  @relation(fields: [whiteboardId], references: [id], onDelete: Cascade)

  @@unique([whiteboardId, nodeId])
  @@index([whiteboardId])
}

model whiteboardEdge {
  id            String      @id @default(cuid())
  whiteboardId  String
  edgeId        String
  source        String
  target        String
  type          String      @default("default")
  data          Json?       @default("{}")
  createdAt     DateTime    @default(now()) @db.Timestamp(6)
  updatedAt     DateTime    @updatedAt @db.Timestamp(6)

  whiteboard    whiteboard  @relation(fields: [whiteboardId], references: [id], onDelete: Cascade)

  @@unique([whiteboardId, edgeId])
  @@index([whiteboardId])
}

model countdownTimer {
  id            String      @id @default(cuid())
  whiteboardId  String
  title         String
  endDate       DateTime    @db.Timestamp(6)
  positionX     Float
  positionY     Float
  isActive      Boolean     @default(true)
  color         String      @default("#000000")
  fontSize      Int         @default(16)
  backgroundColor String?
  createdAt     DateTime    @default(now()) @db.Timestamp(6)
  updatedAt     DateTime    @updatedAt @db.Timestamp(6)

  whiteboard    whiteboard  @relation(fields: [whiteboardId], references: [id], onDelete: Cascade)

  @@index([whiteboardId])
}

model goal {
  id            String      @id @default(cuid())
  whiteboardId  String
  title         String
  description   String?
  dueDate       DateTime?   @db.Timestamp(6)
  priority      String      @default("medium")
  status        String      @default("not-started")
  positionX     Float
  positionY     Float
  tags          String      @default("")
  createdAt     DateTime    @default(now()) @db.Timestamp(6)
  updatedAt     DateTime    @updatedAt @db.Timestamp(6)

  whiteboard    whiteboard  @relation(fields: [whiteboardId], references: [id], onDelete: Cascade)

  @@index([whiteboardId])
}

model hillChart {
  id            String          @id @default(cuid())
  whiteboardId  String
  title         String
  positionX     Float
  positionY     Float
  width         Float           @default(400)
  height        Float           @default(200)
  createdAt     DateTime        @default(now()) @db.Timestamp(6)
  updatedAt     DateTime        @updatedAt @db.Timestamp(6)

  whiteboard    whiteboard      @relation(fields: [whiteboardId], references: [id], onDelete: Cascade)
  items         hillChartItem[]

  @@index([whiteboardId])
}

model hillChartItem {
  id            String      @id @default(cuid())
  hillChartId   String
  name          String
  position      Float
  color         String
  description   String?
  createdAt     DateTime    @default(now()) @db.Timestamp(6)
  updatedAt     DateTime    @updatedAt @db.Timestamp(6)

  hillChart     hillChart   @relation(fields: [hillChartId], references: [id], onDelete: Cascade)

  @@index([hillChartId])
}

model verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime  @db.Timestamp(6)
  createdAt  DateTime? @db.Timestamp(6)
  updatedAt  DateTime? @db.Timestamp(6)
}

// RBAC Models

enum WhiteboardRole {
  OWNER
  EDITOR
  VIEWER
  COMMENTER
}

enum WhiteboardPermission {
  VIEW
  EDIT
  COMMENT
  MANAGE_SETTINGS
  MANAGE_ACCESS
  DELETE
  SHARE
}

model whiteboardUserAccess {
  id            String          @id @default(cuid())
  whiteboardId  String
  userId        String
  role          WhiteboardRole
  permissions   String[]        // JSON array of permissions
  grantedBy     String?         // User ID who granted access
  grantedAt     DateTime        @default(now()) @db.Timestamp(6)
  expiresAt     DateTime?       @db.Timestamp(6)
  createdAt     DateTime        @default(now()) @db.Timestamp(6)
  updatedAt     DateTime        @updatedAt @db.Timestamp(6)

  whiteboard    whiteboard      @relation(fields: [whiteboardId], references: [id], onDelete: Cascade)
  user          user            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([whiteboardId, userId])
  @@index([whiteboardId])
  @@index([userId])
  @@index([role])
}
model whiteboardShareLink {
  id            String          @id @default(cuid())
  whiteboardId  String
  token         String          @unique
  role          WhiteboardRole  @default(VIEWER)
  permissions   String[]        // JSON array of permissions
  expiresAt     DateTime?       @db.Timestamp(6)
  maxUses       Int?            // Maximum number of uses
  usedCount     Int             @default(0)
  isActive      Boolean         @default(true)
  createdBy     String
  createdAt     DateTime        @default(now()) @db.Timestamp(6)
  updatedAt     DateTime        @updatedAt @db.Timestamp(6)

  whiteboard    whiteboard      @relation(fields: [whiteboardId], references: [id], onDelete: Cascade)

  @@index([whiteboardId])
  @@index([token])
  @@index([expiresAt])
}

model whiteboardInvitation {
  id            String          @id @default(cuid())
  whiteboardId  String
  inviterUserId String
  inviteeUserId String?         // null for email invitations
  inviteeEmail  String?         // for inviting non-users
  role          WhiteboardRole
  permissions   String[]        // JSON array of permissions
  status        String          @default("pending") // pending, accepted, declined, expired
  message       String?         // Optional invitation message
  expiresAt     DateTime        @db.Timestamp(6)
  acceptedAt    DateTime?       @db.Timestamp(6)
  createdAt     DateTime        @default(now()) @db.Timestamp(6)
  updatedAt     DateTime        @updatedAt @db.Timestamp(6)

  whiteboard    whiteboard      @relation(fields: [whiteboardId], references: [id], onDelete: Cascade)
  inviter       user            @relation("InvitationSender", fields: [inviterUserId], references: [id], onDelete: Cascade)
  invitee       user?           @relation("InvitationReceiver", fields: [inviteeUserId], references: [id], onDelete: Cascade)

  @@index([whiteboardId])
  @@index([inviterUserId])
  @@index([inviteeUserId])
  @@index([inviteeEmail])
  @@index([status])
  @@index([expiresAt])
}
