import { PrismaClient } from '../src/generated/prisma';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

//   // Create sample users
//   const user1 = await prisma.user.upsert({
//     where: { email: '<EMAIL>' },
//     update: {},
//     create: {
//       name: '<PERSON>',
//       email: '<EMAIL>',
//     },
//   });

//   const user2 = await prisma.user.upsert({
//     where: { email: '<EMAIL>' },
//     update: {},
//     create: {
//       name: '<PERSON>',
//       email: '<EMAIL>',
//       avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
//     },
//   });

//   console.log('✅ Created users:', { user1: user1.name, user2: user2.name });

//   // Create sample whiteboards
//   const whiteboard1 = await prisma.whiteboard.create({
//     data: {
//       userId: user1.id,
//       isPublic: true,
//       allowComments: true,
//       viewMode: 'edit',
//       backgroundColor: '#ffffff',
//       gridMode: true,
//       snapToGrid: false,
//       theme: 'light',
//       viewportX: 0,
//       viewportY: 0,
//       viewportZoom: 1,
//     },
//   });

//   const whiteboard2 = await prisma.whiteboard.create({
//     data: {
//       userId: user2.id,
//       isPublic: false,
//       allowComments: true,
//       viewMode: 'edit',
//       backgroundColor: '#f8fafc',
//       gridMode: false,
//       snapToGrid: true,
//       theme: 'light',
//       viewportX: -100,
//       viewportY: -50,
//       viewportZoom: 0.8,
//     },
//   });

//   const whiteboard3 = await prisma.whiteboard.create({
//     data: {
//       name: 'Design System Workshop',
//       userId: user1.id,
//       isPublic: true,
//       allowComments: true,
//       viewMode: 'edit',
//       backgroundColor: '#1a1a1a',
//       gridMode: true,
//       snapToGrid: true,
//       theme: 'dark',
//       viewportX: 50,
//       viewportY: 25,
//       viewportZoom: 1.2,
//     },
//   });

//   console.log('✅ Created whiteboards:', {
//     whiteboard1: whiteboard1.name,
//     whiteboard2: whiteboard2.name,
//     whiteboard3: whiteboard3.name,
//   });

//   // Create sample nodes for whiteboard1
//   await prisma.whiteboardNode.createMany({
//     data: [
//       {
//         whiteboardId: whiteboard1.id,
//         nodeId: 'text-node-1',
//         type: 'textNode',
//         positionX: 100,
//         positionY: 100,
//         width: 200,
//         height: 80,
//         data: {
//           label: 'Welcome to the Product Planning Board!',
//           fontSize: 16,
//           color: '#333333',
//           backgroundColor: '#ffffff',
//         },
//       },
//       {
//         whiteboardId: whiteboard1.id,
//         nodeId: 'rectangle-node-1',
//         type: 'rectangleNode',
//         positionX: 400,
//         positionY: 150,
//         width: 180,
//         height: 120,
//         data: {
//           label: 'Feature Ideas',
//           color: '#ffffff',
//           backgroundColor: '#3b82f6',
//         },
//       },
//       {
//         whiteboardId: whiteboard1.id,
//         nodeId: 'circle-node-1',
//         type: 'circleNode',
//         positionX: 650,
//         positionY: 200,
//         width: 100,
//         height: 100,
//         data: {
//           label: 'Priority',
//           color: '#ffffff',
//           backgroundColor: '#ef4444',
//         },
//       },
//     ],
//   });

//   // Create sample edges for whiteboard1
//   await prisma.whiteboardEdge.createMany({
//     data: [
//       {
//         whiteboardId: whiteboard1.id,
//         edgeId: 'edge-1',
//         source: 'text-node-1',
//         target: 'rectangle-node-1',
//         type: 'smoothstep',
//         data: {
//           label: 'leads to',
//           color: '#6b7280',
//         },
//       },
//       {
//         whiteboardId: whiteboard1.id,
//         edgeId: 'edge-2',
//         source: 'rectangle-node-1',
//         target: 'circle-node-1',
//         type: 'straight',
//         data: {
//           label: 'prioritize',
//           color: '#ef4444',
//         },
//       },
//     ],
//   });

//   // Create sample countdown timers
//   const futureDate1 = new Date();
//   futureDate1.setDate(futureDate1.getDate() + 7); // 1 week from now

//   const futureDate2 = new Date();
//   futureDate2.setDate(futureDate2.getDate() + 30); // 1 month from now

//   await prisma.countdownTimer.createMany({
//     data: [
//       {
//         whiteboardId: whiteboard1.id,
//         title: 'Sprint End',
//         endDate: futureDate1,
//         positionX: 800,
//         positionY: 100,
//         isActive: true,
//         color: '#ef4444',
//         fontSize: 18,
//         backgroundColor: '#fef2f2',
//       },
//       {
//         whiteboardId: whiteboard2.id,
//         title: 'Product Launch',
//         endDate: futureDate2,
//         positionX: 200,
//         positionY: 300,
//         isActive: true,
//         color: '#059669',
//         fontSize: 20,
//         backgroundColor: '#f0fdf4',
//       },
//     ],
//   });

//   // Create sample goals
//   const goalDueDate1 = new Date();
//   goalDueDate1.setDate(goalDueDate1.getDate() + 14); // 2 weeks from now

//   const goalDueDate2 = new Date();
//   goalDueDate2.setDate(goalDueDate2.getDate() + 21); // 3 weeks from now

//   await prisma.goal.createMany({
//     data: [
//       {
//         whiteboardId: whiteboard1.id,
//         title: 'Complete User Research',
//         description: 'Conduct interviews with 10 users to understand their pain points and needs.',
//         dueDate: goalDueDate1,
//         priority: 'high',
//         status: 'in-progress',
//         positionX: 100,
//         positionY: 400,
//         tags: 'research,users,interviews',
//       },
//       {
//         whiteboardId: whiteboard1.id,
//         title: 'Design Wireframes',
//         description: 'Create low-fidelity wireframes for the main user flows.',
//         dueDate: goalDueDate2,
//         priority: 'medium',
//         status: 'not-started',
//         positionX: 350,
//         positionY: 450,
//         tags: 'design,wireframes,ux',
//       },
//       {
//         whiteboardId: whiteboard2.id,
//         title: 'Team Retrospective Meeting',
//         description: 'Facilitate a retrospective meeting to discuss what went well and what can be improved.',
//         priority: 'medium',
//         status: 'completed',
//         positionX: 500,
//         positionY: 200,
//         tags: 'meeting,retrospective,team',
//       },
//     ],
//   });

//   // Create sample hill charts
//   const hillChart1 = await prisma.hillChart.create({
//     data: {
//       whiteboardId: whiteboard1.id,
//       title: 'Product Development Progress',
//       positionX: 100,
//       positionY: 600,
//       width: 500,
//       height: 250,
//     },
//   });

//   const hillChart2 = await prisma.hillChart.create({
//     data: {
//       whiteboardId: whiteboard2.id,
//       title: 'Team Skills Development',
//       positionX: 300,
//       positionY: 400,
//       width: 400,
//       height: 200,
//     },
//   });

//   // Create sample hill chart items
//   await prisma.hillChartItem.createMany({
//     data: [
//       {
//         hillChartId: hillChart1.id,
//         name: 'User Authentication',
//         position: 85,
//         color: '#10b981',
//         description: 'Login and registration system',
//       },
//       {
//         hillChartId: hillChart1.id,
//         name: 'Dashboard UI',
//         position: 60,
//         color: '#3b82f6',
//         description: 'Main dashboard interface',
//       },
//       {
//         hillChartId: hillChart1.id,
//         name: 'API Integration',
//         position: 25,
//         color: '#f59e0b',
//         description: 'Third-party API connections',
//       },
//       {
//         hillChartId: hillChart1.id,
//         name: 'Testing Suite',
//         position: 15,
//         color: '#ef4444',
//         description: 'Automated testing implementation',
//       },
//       {
//         hillChartId: hillChart2.id,
//         name: 'React Proficiency',
//         position: 75,
//         color: '#8b5cf6',
//         description: 'Team React.js skills',
//       },
//       {
//         hillChartId: hillChart2.id,
//         name: 'TypeScript Adoption',
//         position: 45,
//         color: '#06b6d4',
//         description: 'TypeScript usage across projects',
//       },
//       {
//         hillChartId: hillChart2.id,
//         name: 'Testing Best Practices',
//         position: 30,
//         color: '#84cc16',
//         description: 'Unit and integration testing',
//       },
//     ],
//   });

//   // Create sample user sessions
//   await prisma.userSession.createMany({
//     data: [
//       {
//         userId: user1.id,
//         sessionId: 'session-1-active',
//         isActive: true,
//         lastSeen: new Date(),
//       },
//       {
//         userId: user2.id,
//         sessionId: 'session-2-active',
//         isActive: true,
//         lastSeen: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
//       },
//     ],
//   });

//   console.log('✅ Created sample data:');
//   console.log('  - Countdown timers: 2');
//   console.log('  - Goals: 3');
//   console.log('  - Hill charts: 2');
//   console.log('  - Hill chart items: 7');
//   console.log('  - Whiteboard nodes: 3');
//   console.log('  - Whiteboard edges: 2');
//   console.log('  - User sessions: 2');

//   console.log('🎉 Database seed completed successfully!');
// }

// main()
//   .then(async () => {
//     await prisma.$disconnect();
//   })
//   .catch(async (e) => {
//     console.error('❌ Error during database seed:', e);
//     await prisma.$disconnect();
//     process.exit(1);
//   });

}