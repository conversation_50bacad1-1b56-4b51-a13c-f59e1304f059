{"seo": {"score": 94, "passed": 15, "warnings": 1, "errors": 0}, "performance": {"score": 100, "passed": 12, "warnings": 0, "recommendations": 0}, "timestamp": "2025-07-05T12:27:22.418Z", "recommendations": [{"priority": "MEDIUM", "category": "SEO", "title": "Address SEO Warnings", "description": "1 SEO warnings should be resolved", "action": "Review and implement missing SEO features"}, {"priority": "LOW", "category": "Monitoring", "title": "Set up Regular Monitoring", "description": "Implement automated SEO monitoring for production", "action": "Schedule npm run seo:monitor to run daily"}, {"priority": "MEDIUM", "category": "Content", "title": "Content Optimization", "description": "Regularly update and optimize content for target keywords", "action": "Review and update page content monthly"}]}