<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#grad)"/>
  <text x="50%" y="40%" dominant-baseline="middle" text-anchor="middle" 
        font-family="Arial, sans-serif" font-size="34.13333333333333" 
        font-weight="bold" fill="white">
    Anchorboard App
  </text>
  <text x="50%" y="55%" dominant-baseline="middle" text-anchor="middle" 
        font-family="Arial, sans-serif" font-size="20.48" 
        fill="rgba(255,255,255,0.9)">
    AI-Powered Collaboration
  </text>
  <text x="50%" y="70%" dominant-baseline="middle" text-anchor="middle" 
        font-family="Arial, sans-serif" font-size="14.628571428571428" 
        fill="rgba(255,255,255,0.7)">
    Company logo
  </text>
</svg>