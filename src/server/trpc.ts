import { initTR<PERSON>, TRPCError } from '@trpc/server';
import type { CreateNextContextOptions } from '@trpc/server/adapters/next';
import type { FetchCreateContextFnOptions } from '@trpc/server/adapters/fetch';
import superjson from 'superjson';
import { ZodError } from 'zod';
import { prisma } from '@/lib/prisma';
import { auth } from '@/lib/auth';
import { logAuthError, logDebug } from '@/lib/logger';
import type { UserAccess } from '@/lib/rbac';

/**
 * 1. CONTEXT
 *
 * This section defines the "contexts" that are available in the backend API.
 *
 * These allow you to access things when processing a request, like the database, the session, etc.
 */

interface CreateContextOptions {
  session: { user: { id: string; email: string; name?: string | null } } | null;
  userAccess?: UserAccess;
}

/**
 * This helper generates the "internals" for a tRPC context. If you need to use it, you can export
 * it from here.
 *
 * Examples of things you may need it for:
 * - testing, so we don't have to mock Next.js' req/res
 * - tRPC's `createSSGHelpers`, where we don't have req/res
 *
 * @see https://create.t3.gg/en/usage/trpc#-serverapitrpcts
 */
const createInnerTRPCContext = (opts: CreateContextOptions) => {
  return {
    session: opts.session,
    userAccess: opts.userAccess,
    prisma,
  };
};

/**
 * This is the actual context you will use in your router. It will be used to process every request
 * that goes through your tRPC endpoint.
 *
 * @see https://trpc.io/docs/context
 */
export const createTRPCContext = async (opts: CreateNextContextOptions) => {
  const { req } = opts;

  // Get the session from the request using BetterAuth API directly
  const getSession = async () => {
    try {
      logDebug('tRPC Context - Getting session using BetterAuth API');

      // Use BetterAuth's API to get session directly
      // Check if auth.api.getSession is available (not during build time)
      if (!auth.api || typeof (auth.api as any).getSession !== 'function') {
        logDebug('tRPC Context - BetterAuth API not available (likely during build)');
        return null;
      }

      const sessionData = await (auth.api as any).getSession({ headers: req.headers });

      logDebug('tRPC Context - BetterAuth session result', {
        hasSession: !!sessionData?.session,
        hasUser: !!sessionData?.user
      });

      if (sessionData?.session && sessionData?.user) {
        logDebug('tRPC Context - Valid session found for user', {
          userEmail: sessionData.user.email
        });

        return {
          user: {
            id: sessionData.user.id,
            email: sessionData.user.email,
            name: sessionData.user.name || null,
          },
        };
      }

      logDebug('tRPC Context - No valid session found');
      return null;
    } catch (error) {
      logAuthError('tRPC Context - Error getting session', error);
      return null;
    }
  };

  const session = await getSession();

  return createInnerTRPCContext({
    session,
  });
};

/**
 * Context for App Router (fetch adapter)
 */
export const createTRPCContextAppRouter = async (opts: FetchCreateContextFnOptions) => {
  logDebug('tRPC Context - createTRPCContextAppRouter called');
  const { req } = opts;

  // Get the session from the request using BetterAuth API directly
  const getSession = async () => {
    try {
      logDebug('tRPC Context - Getting session using BetterAuth API');

      // Check if auth.api.getSession is available (not during build time)
      if (!auth.api || typeof (auth.api as any).getSession !== 'function') {
        logDebug('tRPC Context - BetterAuth API not available (likely during build)');
        return null;
      }

      // Convert Headers object to plain object for BetterAuth
      const headers: Record<string, string> = {};
      req.headers.forEach((value, key) => {
        headers[key] = value;
      });

      // Use BetterAuth's API to get session directly
      const sessionData = await (auth.api as any).getSession({ headers });

      logDebug('tRPC Context - BetterAuth session result', {
        hasSession: !!sessionData?.session,
        hasUser: !!sessionData?.user
      });

      if (sessionData?.session && sessionData?.user) {
        logDebug('tRPC Context - Valid session found for user', {
          userEmail: sessionData.user.email
        });

        return {
          user: {
            id: sessionData.user.id,
            email: sessionData.user.email,
            name: sessionData.user.name || null,
          },
        };
      }

      logDebug('tRPC Context - No valid session found');
      return null;
    } catch (error) {
      logAuthError('tRPC Context - Error getting session', error);
      return null;
    }
  };

  const session = await getSession();

  return createInnerTRPCContext({
    session,
  });
};

/**
 * 2. INITIALIZATION
 *
 * This is where the tRPC API is initialized, connecting the context and transformer. We also parse
 * ZodErrors so that you get typesafety on the frontend if your procedure fails due to validation
 * errors on the backend.
 */
const t = initTRPC.context<Awaited<ReturnType<typeof createTRPCContext>>>().create({
  transformer: superjson,
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    };
  },
});

/**
 * 3. ROUTER & PROCEDURE HELPERS
 *
 * These are the pieces you use to build your tRPC API. You should import these a lot in the
 * "/src/server/api/routers" directory.
 */

/**
 * This is how you create new routers and sub-routers in your tRPC API.
 *
 * @see https://trpc.io/docs/router
 */
export const createTRPCRouter = t.router;

/**
 * Public (unauthenticated) procedure
 *
 * This is the base piece you use to build new queries and mutations on your tRPC API. It does not
 * guarantee that a user querying is authorized, but you can still access user session data if they
 * are logged in.
 */
export const publicProcedure = t.procedure;

/**
 * Protected (authenticated) procedure
 *
 * If you want a query or mutation to ONLY be accessible to logged in users, use this. It verifies
 * the session is valid and guarantees `ctx.session.user` is not null.
 *
 * @see https://trpc.io/docs/procedures
 */
export const protectedProcedure = t.procedure.use(({ ctx, next }) => {
  if (!ctx.session || !ctx.session.user) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  return next({
    ctx: {
      // infers the `session` as non-nullable
      session: { ...ctx.session, user: ctx.session.user },
    },
  });
});

export type Context = Awaited<ReturnType<typeof createTRPCContext>>;
