import { TRPCError } from '@trpc/server';
import { WhiteboardPermission, WhiteboardRole } from '@/types';
import { 
  determineUserAccess, 
  hasPermission, 
  canPerformAction,
  type UserAccess,
  type WhiteboardAccessContext,
  type AccessRequest
} from '@/lib/rbac';
import type { Context } from '../trpc';

// ============================================================================
// ACCESS RESOLUTION
// ============================================================================

/**
 * Get user access for a whiteboard
 */
export async function getUserWhiteboardAccess(
  ctx: Context,
  whiteboardId: string,
  shareLinkToken?: string
): Promise<UserAccess> {
  const userId = ctx.session?.user?.id;

  // Get whiteboard with access information
  const whiteboard = await ctx.prisma.whiteboard.findUnique({
    where: { id: whiteboardId },
    include: {
      userAccess: {
        where: { userId: userId || '' },
        take: 1,
      },
      shareLinks: shareLinkToken ? {
        where: { 
          token: shareLinkToken,
          isActive: true,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } },
          ],
        },
        take: 1,
      } : undefined,
    },
  });

  if (!whiteboard) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: 'Whiteboard not found',
    });
  }

  const context: WhiteboardAccessContext = {
    whiteboardId: whiteboard.id,
    ownerId: whiteboard.userId,
    isPublic: whiteboard.isPublic,
    publicAccessLevel: whiteboard.publicAccessLevel,
    requiresAuth: whiteboard.requiresAuth,
    allowAnonymousView: whiteboard.allowAnonymousView,
  };

  // Get user's explicit access if any
  const userAccess = whiteboard.userAccess[0];
  const shareLink = whiteboard.shareLinks?.[0];

  const request: AccessRequest = {
    whiteboardId: whiteboard.id,
    userId,
    userRole: userAccess?.role as WhiteboardRole | undefined,
    customPermissions: userAccess?.permissions?.map(p => p as WhiteboardPermission),
    shareLinkToken: shareLink?.token,
    shareLinkRole: shareLink?.role as WhiteboardRole | undefined,
    shareLinkPermissions: shareLink?.permissions?.map(p => p as WhiteboardPermission),
  };

  return determineUserAccess(context, request);
}

// ============================================================================
// PERMISSION CHECKERS (Replace middleware)
// ============================================================================

/**
 * Create a permission checker function
 */
export function createPermissionChecker(permission: WhiteboardPermission) {
  return async (ctx: Context, input: any): Promise<UserAccess> => {
    const whiteboardId = input.whiteboardId || input.id;
    const shareLinkToken = input.token;

    if (!whiteboardId) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Whiteboard ID is required',
      });
    }

    const userAccess = await getUserWhiteboardAccess(ctx, whiteboardId, shareLinkToken);

    if (!hasPermission(userAccess, permission)) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: `Insufficient permissions. Required: ${permission}`,
      });
    }

    return userAccess;
  };
}

/**
 * Check public access
 */
export async function checkPublicAccess(ctx: Context, input: any): Promise<UserAccess> {
  const whiteboardId = input.whiteboardId || input.id;
  const shareLinkToken = input.token;

  if (!whiteboardId) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: 'Whiteboard ID is required',
    });
  }

  const userAccess = await getUserWhiteboardAccess(ctx, whiteboardId, shareLinkToken);

  if (!hasPermission(userAccess, WhiteboardPermission.VIEW)) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'This whiteboard is not publicly accessible',
    });
  }

  return userAccess;
}

// ============================================================================
// SPECIFIC PERMISSION CHECKERS
// ============================================================================

export const checkViewAccess = createPermissionChecker(WhiteboardPermission.VIEW);
export const checkEditAccess = createPermissionChecker(WhiteboardPermission.EDIT);
export const checkShareAccess = createPermissionChecker(WhiteboardPermission.SHARE);
export const checkManageAccess = createPermissionChecker(WhiteboardPermission.MANAGE_ACCESS);

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get whiteboard access information for sharing
 */
export async function getWhiteboardAccessInfo(
  ctx: Context,
  whiteboardId: string
) {
  const whiteboard = await ctx.prisma.whiteboard.findUnique({
    where: { id: whiteboardId },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      userAccess: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'asc',
        },
      },
      shareLinks: {
        where: {
          isActive: true,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } },
          ],
        },
        orderBy: {
          createdAt: 'desc',
        },
      },
      invitations: {
        where: {
          status: 'pending',
          expiresAt: { gt: new Date() },
        },
        include: {
          inviter: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          invitee: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      },
    },
  });

  if (!whiteboard) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: 'Whiteboard not found',
    });
  }

  return whiteboard;
} 