import { TRPCError } from '@trpc/server';
import { WhiteboardPermission, WhiteboardRole } from '@/types';
import { 
  determineUserAccess, 
  hasPermission, 
  canPerformAction,
  type UserAccess,
  type WhiteboardAccessContext,
  type AccessRequest
} from '@/lib/rbac';
import type { Context } from '../trpc';

// ============================================================================
// ACCESS RESOLUTION
// ============================================================================

/**
 * Get user access for a whiteboard
 */
export async function getUserWhiteboardAccess(
  ctx: Context,
  whiteboardId: string,
  shareLinkToken?: string
): Promise<UserAccess> {
  const userId = ctx.session?.user?.id;

  // Get whiteboard with access information
  const whiteboard = await ctx.prisma.whiteboard.findUnique({
    where: { id: whiteboardId },
    include: {
      userAccess: {
        where: { userId: userId || '' },
        take: 1,
      },
      shareLinks: shareLinkToken ? {
        where: { 
          token: shareLinkToken,
          isActive: true,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } },
          ],
        },
        take: 1,
      } : undefined,
    },
  });

  if (!whiteboard) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: 'Whiteboard not found',
    });
  }

  const context: WhiteboardAccessContext = {
    whiteboardId: whiteboard.id,
    ownerId: whiteboard.userId,
    isPublic: whiteboard.isPublic,
    publicAccessLevel: whiteboard.publicAccessLevel,
    requiresAuth: whiteboard.requiresAuth,
    allowAnonymousView: whiteboard.allowAnonymousView,
  };

  // Get user's explicit access if any
  const userAccess = whiteboard.userAccess[0];
  const shareLink = whiteboard.shareLinks?.[0];

  const request: AccessRequest = {
    whiteboardId: whiteboard.id,
    userId,
    userRole: userAccess?.role as WhiteboardRole | undefined,
    customPermissions: userAccess?.permissions?.map(p => p as WhiteboardPermission),
    shareLinkToken: shareLink?.token,
    shareLinkRole: shareLink?.role as WhiteboardRole | undefined,
    shareLinkPermissions: shareLink?.permissions?.map(p => p as WhiteboardPermission),
  };

  return determineUserAccess(context, request);
}

// ============================================================================
// MIDDLEWARE FACTORY
// ============================================================================

/**
 * Create middleware that adds userAccess to context
 */
export function withUserAccess() {
  return async (opts: { 
    ctx: Context; 
    input: { whiteboardId?: string; id?: string; token?: string } 
  }) => {
    const { ctx, input } = opts;
    const whiteboardId = input.whiteboardId || input.id;
    const shareLinkToken = input.token;

    if (!whiteboardId) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Whiteboard ID is required',
      });
    }

    const userAccess = await getUserWhiteboardAccess(ctx, whiteboardId, shareLinkToken);

    return {
      ctx: {
        ...ctx,
        userAccess,
      },
    };
  };
}

/**
 * Create middleware that requires a specific permission
 */
export function requirePermission(permission: WhiteboardPermission) {
  return async (opts: { 
    ctx: Context; 
    input: { whiteboardId?: string; id?: string; token?: string } 
  }) => {
    const { ctx, input } = opts;
    const whiteboardId = input.whiteboardId || input.id;
    const shareLinkToken = input.token;

    if (!whiteboardId) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Whiteboard ID is required',
      });
    }

    const userAccess = await getUserWhiteboardAccess(ctx, whiteboardId, shareLinkToken);

    if (!hasPermission(userAccess, permission)) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: `Insufficient permissions. Required: ${permission}`,
      });
    }

    return {
      ctx: {
        ...ctx,
        userAccess,
      },
    };
  };
}

/**
 * Create middleware that requires a specific action
 */
export function requireAction(action: 'view' | 'edit' | 'comment' | 'manage' | 'share' | 'delete') {
  return async (opts: { 
    ctx: Context; 
    input: { whiteboardId?: string; id?: string; token?: string } 
  }) => {
    const { ctx, input } = opts;
    const whiteboardId = input.whiteboardId || input.id;
    const shareLinkToken = input.token;

    if (!whiteboardId) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Whiteboard ID is required',
      });
    }

    const userAccess = await getUserWhiteboardAccess(ctx, whiteboardId, shareLinkToken);

    if (!canPerformAction(userAccess, action)) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: `Insufficient permissions to ${action} this whiteboard`,
      });
    }

    return {
      ctx: {
        ...ctx,
        userAccess,
      },
    };
  };
}

/**
 * Create middleware that requires ownership or specific roles
 */
export function requireOwnershipOrRole(allowedRoles: WhiteboardRole[] = []) {
  return async (opts: { 
    ctx: Context; 
    input: { whiteboardId?: string; id?: string; token?: string } 
  }) => {
    const { ctx, input } = opts;
    const whiteboardId = input.whiteboardId || input.id;
    const shareLinkToken = input.token;

    if (!whiteboardId) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Whiteboard ID is required',
      });
    }

    const userAccess = await getUserWhiteboardAccess(ctx, whiteboardId, shareLinkToken);

    // Check if user is owner
    if (userAccess.isOwner) {
      return {
        ctx: {
          ...ctx,
          userAccess,
        },
      };
    }

    // Check if user has allowed role
    if (userAccess.role && allowedRoles.includes(userAccess.role)) {
      return {
        ctx: {
          ...ctx,
          userAccess,
        },
      };
    }

    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Insufficient permissions. Owner or specific role required.',
    });
  };
}

/**
 * Create middleware for public whiteboard access
 */
export function allowPublicAccess() {
  return async (opts: { 
    ctx: Context; 
    input: { whiteboardId?: string; id?: string; token?: string } 
  }) => {
    const { ctx, input } = opts;
    const whiteboardId = input.whiteboardId || input.id;
    const shareLinkToken = input.token;

    if (!whiteboardId) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Whiteboard ID is required',
      });
    }

    const userAccess = await getUserWhiteboardAccess(ctx, whiteboardId, shareLinkToken);

    // Allow access if user has at least view permission
    if (!hasPermission(userAccess, WhiteboardPermission.VIEW)) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'This whiteboard is not publicly accessible',
      });
    }

    return {
      ctx: {
        ...ctx,
        userAccess,
      },
    };
  };
}

// ============================================================================
// CONVENIENCE MIDDLEWARE
// ============================================================================

/**
 * Middleware for viewing whiteboards
 */
export const requireViewAccess = requireAction('view');

/**
 * Middleware for editing whiteboards
 */
export const requireEditAccess = requireAction('edit');

/**
 * Middleware for commenting on whiteboards
 */
export const requireCommentAccess = requireAction('comment');

/**
 * Middleware for managing whiteboards
 */
export const requireManageAccess = requireAction('manage');

/**
 * Middleware for sharing whiteboards
 */
export const requireShareAccess = requireAction('share');

/**
 * Middleware for deleting whiteboards
 */
export const requireDeleteAccess = requireAction('delete');

/**
 * Middleware for owners only
 */
export const requireOwnership = requireOwnershipOrRole([]);

/**
 * Middleware for owners and editors
 */
export const requireOwnerOrEditor = requireOwnershipOrRole([WhiteboardRole.EDITOR]);

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get whiteboard access information for sharing
 */
export async function getWhiteboardAccessInfo(
  ctx: Context,
  whiteboardId: string
) {
  const whiteboard = await ctx.prisma.whiteboard.findUnique({
    where: { id: whiteboardId },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      userAccess: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'asc',
        },
      },
      shareLinks: {
        where: {
          isActive: true,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } },
          ],
        },
        orderBy: {
          createdAt: 'desc',
        },
      },
      invitations: {
        where: {
          status: 'pending',
          expiresAt: { gt: new Date() },
        },
        include: {
          inviter: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          invitee: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      },
    },
  });

  if (!whiteboard) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: 'Whiteboard not found',
    });
  }

  return whiteboard;
}

/**
 * Check if user can manage access for a whiteboard
 */
export async function canManageWhiteboardAccess(
  ctx: Context,
  whiteboardId: string
): Promise<boolean> {
  try {
    const userAccess = await getUserWhiteboardAccess(ctx, whiteboardId);
    return hasPermission(userAccess, WhiteboardPermission.MANAGE_ACCESS);
  } catch {
    return false;
  }
}
