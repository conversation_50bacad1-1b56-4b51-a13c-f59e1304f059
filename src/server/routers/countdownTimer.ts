import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { createTRPCRouter, publicProcedure } from '../trpc';
import {
  CountdownTimerSchema,
  CreateCountdownTimerSchema,
  UpdateCountdownTimerSchema,
} from '@/lib/schemas';

export const countdownTimerRouter = createTRPCRouter({
  // Get all countdown timers for a whiteboard
  getByWhiteboardId: publicProcedure
    .input(z.object({ whiteboardId: z.string().cuid() }))
    .output(z.array(CountdownTimerSchema))
    .query(async ({ ctx, input }) => {
      const timers = await ctx.prisma.countdownTimer.findMany({
        where: { whiteboardId: input.whiteboardId },
        orderBy: { createdAt: 'desc' },
      });

      return timers;
    }),

  // Get a single countdown timer by ID
  getById: publicProcedure
    .input(z.object({ id: z.string().cuid() }))
    .output(CountdownTimerSchema.nullable())
    .query(async ({ ctx, input }) => {
      const timer = await ctx.prisma.countdownTimer.findUnique({
        where: { id: input.id },
      });

      return timer;
    }),

  // Create a new countdown timer
  create: publicProcedure
    .input(CreateCountdownTimerSchema)
    .output(CountdownTimerSchema)
    .mutation(async ({ ctx, input }) => {
      // Verify whiteboard exists
      const whiteboard = await ctx.prisma.whiteboard.findUnique({
        where: { id: input.whiteboardId },
      });

      if (!whiteboard) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Whiteboard not found',
        });
      }

      const timer = await ctx.prisma.countdownTimer.create({
        data: input,
      });

      return timer;
    }),

  // Update a countdown timer
  update: publicProcedure
    .input(
      z.object({
        id: z.string().cuid(),
        data: UpdateCountdownTimerSchema,
      })
    )
    .output(CountdownTimerSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, data } = input;

      // Check if timer exists
      const existingTimer = await ctx.prisma.countdownTimer.findUnique({
        where: { id },
      });

      if (!existingTimer) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Countdown timer not found',
        });
      }

      const updatedTimer = await ctx.prisma.countdownTimer.update({
        where: { id },
        data,
      });

      return updatedTimer;
    }),

  // Delete a countdown timer
  delete: publicProcedure
    .input(z.object({ id: z.string().cuid() }))
    .output(z.object({ success: z.boolean() }))
    .mutation(async ({ ctx, input }) => {
      const { id } = input;

      // Check if timer exists
      const existingTimer = await ctx.prisma.countdownTimer.findUnique({
        where: { id },
      });

      if (!existingTimer) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Countdown timer not found',
        });
      }

      await ctx.prisma.countdownTimer.delete({
        where: { id },
      });

      return { success: true };
    }),

  // Update timer position
  updatePosition: publicProcedure
    .input(
      z.object({
        id: z.string().cuid(),
        positionX: z.number(),
        positionY: z.number(),
      })
    )
    .output(CountdownTimerSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, positionX, positionY } = input;

      const updatedTimer = await ctx.prisma.countdownTimer.update({
        where: { id },
        data: {
          positionX,
          positionY,
        },
      });

      return updatedTimer;
    }),

  // Toggle timer active state
  toggleActive: publicProcedure
    .input(z.object({ id: z.string().cuid() }))
    .output(CountdownTimerSchema)
    .mutation(async ({ ctx, input }) => {
      const { id } = input;

      const existingTimer = await ctx.prisma.countdownTimer.findUnique({
        where: { id },
      });

      if (!existingTimer) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Countdown timer not found',
        });
      }

      const updatedTimer = await ctx.prisma.countdownTimer.update({
        where: { id },
        data: {
          isActive: !existingTimer.isActive,
        },
      });

      return updatedTimer;
    }),

  // Get active timers for a whiteboard
  getActiveByWhiteboardId: publicProcedure
    .input(z.object({ whiteboardId: z.string().cuid() }))
    .output(z.array(CountdownTimerSchema))
    .query(async ({ ctx, input }) => {
      const timers = await ctx.prisma.countdownTimer.findMany({
        where: {
          whiteboardId: input.whiteboardId,
          isActive: true,
        },
        orderBy: { endDate: 'asc' },
      });

      return timers;
    }),

  // Bulk create countdown timers
  bulkCreate: publicProcedure
    .input(
      z.object({
        whiteboardId: z.string().cuid(),
        timers: z.array(CreateCountdownTimerSchema.omit({ whiteboardId: true })),
      })
    )
    .output(z.array(CountdownTimerSchema))
    .mutation(async ({ ctx, input }) => {
      const { whiteboardId, timers } = input;

      // Verify whiteboard exists
      const whiteboard = await ctx.prisma.whiteboard.findUnique({
        where: { id: whiteboardId },
      });

      if (!whiteboard) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Whiteboard not found',
        });
      }

      const createdTimers = await ctx.prisma.$transaction(
        timers.map((timer) =>
          ctx.prisma.countdownTimer.create({
            data: {
              ...timer,
              whiteboardId,
            },
          })
        )
      );

      return createdTimers;
    }),

  // Delete all timers for a whiteboard
  deleteByWhiteboardId: publicProcedure
    .input(z.object({ whiteboardId: z.string().cuid() }))
    .output(z.object({ count: z.number() }))
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.prisma.countdownTimer.deleteMany({
        where: { whiteboardId: input.whiteboardId },
      });

      return { count: result.count };
    }),
});
