import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { WhiteboardRole, WhiteboardPermission } from '@/types';
import { createTRPCRouter, protectedProcedure, publicProcedure } from '../trpc';
import { logInfo, logError, logDebug } from '@/lib/logger';
import { 
  checkShareAccess, 
  checkManageAccess,
  getUserWhiteboardAccess 
} from '../middleware/rbac-simple';
import { 
  generateShareToken, 
  getInvitationExpiry 
} from '@/lib/rbac';

export const sharingRouter = createTRPCRouter({
  // Create a share link
  createShareLink: protectedProcedure
    .input(z.object({
      whiteboardId: z.string().cuid(),
      role: z.nativeEnum(WhiteboardRole).default(WhiteboardRole.VIEWER),
      permissions: z.array(z.nativeEnum(WhiteboardPermission)).optional(),
      expiresInDays: z.number().positive().optional(),
      maxUses: z.number().positive().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      // Check share access inline
      await checkShareAccess(ctx, input);
      
      const { whiteboardId, role, permissions, expiresInDays, maxUses } = input;
      const createdBy = ctx.session.user.id;

      const token = generateShareToken();
      const expiresAt = expiresInDays ? getInvitationExpiry(expiresInDays) : null;

      const shareLink = await ctx.prisma.whiteboardShareLink.create({
        data: {
          whiteboardId,
          token,
          role,
          permissions: permissions || [],
          expiresAt,
          maxUses,
          createdBy,
        },
      });

      logInfo('Share link created', {
        whiteboardId,
        linkId: shareLink.id,
        role,
        createdBy,
        expiresAt,
        maxUses,
      });

      return shareLink;
    }),

  // Get share links
  getShareLinks: protectedProcedure
    .input(z.object({ whiteboardId: z.string().cuid() }))
    .query(async ({ ctx, input }) => {
      // Check manage access inline
      await checkManageAccess(ctx, input);
      
      const { whiteboardId } = input;

      const shareLinks = await ctx.prisma.whiteboardShareLink.findMany({
        where: {
          whiteboardId,
          isActive: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return shareLinks;
    }),

  // Deactivate a share link
  deactivateShareLink: protectedProcedure
    .input(z.object({
      whiteboardId: z.string().cuid(),
      linkId: z.string().cuid(),
    }))
    .mutation(async ({ ctx, input }) => {
      // Check manage access inline
      await checkManageAccess(ctx, input);
      
      const { whiteboardId, linkId } = input;

      await ctx.prisma.whiteboardShareLink.update({
        where: {
          id: linkId,
          whiteboardId,
        },
        data: {
          isActive: false,
        },
      });

      logInfo('Share link deactivated', {
        whiteboardId,
        linkId,
        deactivatedBy: ctx.session.user.id,
      });

      return { success: true };
    }),

  // Access whiteboard via share link
  accessViaShareLink: publicProcedure
    .input(z.object({ token: z.string() }))
    .query(async ({ ctx, input }) => {
      const { token } = input;

      const shareLink = await ctx.prisma.whiteboardShareLink.findUnique({
        where: { token },
        include: {
          whiteboard: {
            select: {
              id: true,
              title: true,
              content: true,
              userId: true,
              isPublic: true,
              publicAccessLevel: true,
              requiresAuth: true,
              allowAnonymousView: true,
              createdAt: true,
              updatedAt: true,
              allowComments: true,
              viewMode: true,
              backgroundColor: true,
              gridMode: true,
              snapToGrid: true,
              theme: true,
              viewportX: true,
              viewportY: true,
              viewportZoom: true,
            },
          },
        },
      });

      if (!shareLink || !shareLink.isActive) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Share link not found or inactive',
        });
      }

      // Check if link is expired
      if (shareLink.expiresAt && new Date() > shareLink.expiresAt) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Share link has expired',
        });
      }

      // Check usage limits
      if (shareLink.maxUses && shareLink.usedCount >= shareLink.maxUses) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Share link usage limit exceeded',
        });
      }

      // Increment usage count
      await ctx.prisma.whiteboardShareLink.update({
        where: { id: shareLink.id },
        data: { usedCount: { increment: 1 } },
      });

      return {
        whiteboard: shareLink.whiteboard,
        access: {
          role: shareLink.role,
          permissions: shareLink.permissions,
        },
      };
    }),

  // Send invitation to user
  inviteUser: protectedProcedure
    .input(z.object({
      whiteboardId: z.string().cuid(),
      email: z.string().email(),
      role: z.nativeEnum(WhiteboardRole).default(WhiteboardRole.VIEWER),
      permissions: z.array(z.nativeEnum(WhiteboardPermission)).optional(),
      message: z.string().optional(),
      expiresInDays: z.number().positive().default(7),
    }))
    .mutation(async ({ ctx, input }) => {
      // Check manage access inline
      await checkManageAccess(ctx, input);
      
      const { whiteboardId, email, role, permissions, message, expiresInDays } = input;
      const inviterUserId = ctx.session.user.id;

      // Check if user exists
      const inviteeUser = await ctx.prisma.user.findUnique({
        where: { email },
      });

      const expiresAt = getInvitationExpiry(expiresInDays);

      const invitation = await ctx.prisma.whiteboardInvitation.create({
        data: {
          whiteboardId,
          inviterUserId,
          inviteeUserId: inviteeUser?.id,
          inviteeEmail: email,
          role,
          permissions: permissions || [],
          message,
          expiresAt,
        },
      });

      logInfo('User invitation sent', {
        whiteboardId,
        inviterUserId,
        inviteeEmail: email,
        role,
      });

      // TODO: Send email notification
      // await sendInvitationEmail(invitation);

      return invitation;
    }),

  // Get invitations for a whiteboard
  getInvitations: protectedProcedure
    .input(z.object({ whiteboardId: z.string().cuid() }))
    .query(async ({ ctx, input }) => {
      // Check manage access inline
      await checkManageAccess(ctx, input);
      
      const { whiteboardId } = input;

      const invitations = await ctx.prisma.whiteboardInvitation.findMany({
        where: { whiteboardId },
        include: {
          inviter: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          invitee: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return invitations;
    }),

  // Accept invitation
  acceptInvitation: protectedProcedure
    .input(z.object({ invitationId: z.string().cuid() }))
    .mutation(async ({ ctx, input }) => {
      const { invitationId } = input;
      const userId = ctx.session.user.id;

      const invitation = await ctx.prisma.whiteboardInvitation.findUnique({
        where: { id: invitationId },
      });

      if (!invitation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Invitation not found',
        });
      }

      // Check if invitation is for this user
      if (invitation.inviteeUserId !== userId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'This invitation is not for you',
        });
      }

      // Check if invitation is expired
      if (invitation.expiresAt && new Date() > invitation.expiresAt) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Invitation has expired',
        });
      }

      // Check if invitation is still pending
      if (invitation.status !== 'pending') {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Invitation has already been processed',
        });
      }

      // Create user access and update invitation
      await ctx.prisma.$transaction([
        ctx.prisma.whiteboardUserAccess.upsert({
          where: {
            whiteboardId_userId: {
              whiteboardId: invitation.whiteboardId,
              userId,
            },
          },
          update: {
            role: invitation.role,
            permissions: invitation.permissions,
            grantedBy: invitation.inviterUserId,
          },
          create: {
            whiteboardId: invitation.whiteboardId,
            userId,
            role: invitation.role,
            permissions: invitation.permissions,
            grantedBy: invitation.inviterUserId,
          },
        }),
        ctx.prisma.whiteboardInvitation.update({
          where: { id: invitationId },
          data: {
            status: 'accepted',
            acceptedAt: new Date(),
          },
        }),
      ]);

      logInfo('Invitation accepted', {
        invitationId,
        whiteboardId: invitation.whiteboardId,
        userId,
        role: invitation.role,
      });

      return { success: true };
    }),
});
