import { createTRPCRouter } from '../trpc';
import { whiteboardRouter } from './whiteboard';
import { countdownTimerRouter } from './countdownTimer';
import { goalRouter } from './goal';
import { hillChartRouter } from './hillChart';
import { sharingRouter } from './sharing';

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  whiteboard: whiteboardRouter,
  countdownTimer: countdownTimerRouter,
  goal: goalRouter,
  hillChart: hillChartRouter,
  sharing: sharingRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;
