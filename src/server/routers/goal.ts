import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { createTRPCRouter, publicProcedure } from '../trpc';
import {
  GoalSchema,
  CreateGoalSchema,
  UpdateGoalSchema,
} from '@/lib/schemas';

export const goalRouter = createTRPCRouter({
  // Get all goals for a whiteboard
  getByWhiteboardId: publicProcedure
    .input(z.object({ whiteboardId: z.string().cuid() }))
    .output(z.array(GoalSchema))
    .query(async ({ ctx, input }) => {
      const goals = await ctx.prisma.goal.findMany({
        where: { whiteboardId: input.whiteboardId },
        orderBy: { createdAt: 'desc' },
      });

      return goals;
    }),

  // Get a single goal by ID
  getById: publicProcedure
    .input(z.object({ id: z.string().cuid() }))
    .output(GoalSchema.nullable())
    .query(async ({ ctx, input }) => {
      const goal = await ctx.prisma.goal.findUnique({
        where: { id: input.id },
      });

      return goal;
    }),

  // Create a new goal
  create: publicProcedure
    .input(CreateGoalSchema)
    .output(GoalSchema)
    .mutation(async ({ ctx, input }) => {
      // Verify whiteboard exists
      const whiteboard = await ctx.prisma.whiteboard.findUnique({
        where: { id: input.whiteboardId },
      });

      if (!whiteboard) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Whiteboard not found',
        });
      }

      const goal = await ctx.prisma.goal.create({
        data: input,
      });

      return goal;
    }),

  // Update a goal
  update: publicProcedure
    .input(
      z.object({
        id: z.string().cuid(),
        data: UpdateGoalSchema,
      })
    )
    .output(GoalSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, data } = input;

      // Check if goal exists
      const existingGoal = await ctx.prisma.goal.findUnique({
        where: { id },
      });

      if (!existingGoal) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Goal not found',
        });
      }

      const updatedGoal = await ctx.prisma.goal.update({
        where: { id },
        data,
      });

      return updatedGoal;
    }),

  // Delete a goal
  delete: publicProcedure
    .input(z.object({ id: z.string().cuid() }))
    .output(z.object({ success: z.boolean() }))
    .mutation(async ({ ctx, input }) => {
      const { id } = input;

      // Check if goal exists
      const existingGoal = await ctx.prisma.goal.findUnique({
        where: { id },
      });

      if (!existingGoal) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Goal not found',
        });
      }

      await ctx.prisma.goal.delete({
        where: { id },
      });

      return { success: true };
    }),

  // Update goal position
  updatePosition: publicProcedure
    .input(
      z.object({
        id: z.string().cuid(),
        positionX: z.number(),
        positionY: z.number(),
      })
    )
    .output(GoalSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, positionX, positionY } = input;

      const updatedGoal = await ctx.prisma.goal.update({
        where: { id },
        data: {
          positionX,
          positionY,
        },
      });

      return updatedGoal;
    }),

  // Update goal status
  updateStatus: publicProcedure
    .input(
      z.object({
        id: z.string().cuid(),
        status: z.enum(['not-started', 'in-progress', 'completed']),
      })
    )
    .output(GoalSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, status } = input;

      const updatedGoal = await ctx.prisma.goal.update({
        where: { id },
        data: { status },
      });

      return updatedGoal;
    }),

  // Get goals by status
  getByStatus: publicProcedure
    .input(
      z.object({
        whiteboardId: z.string().cuid(),
        status: z.enum(['not-started', 'in-progress', 'completed']),
      })
    )
    .output(z.array(GoalSchema))
    .query(async ({ ctx, input }) => {
      const goals = await ctx.prisma.goal.findMany({
        where: {
          whiteboardId: input.whiteboardId,
          status: input.status,
        },
        orderBy: { createdAt: 'desc' },
      });

      return goals;
    }),

  // Get goals by priority
  getByPriority: publicProcedure
    .input(
      z.object({
        whiteboardId: z.string().cuid(),
        priority: z.enum(['low', 'medium', 'high']),
      })
    )
    .output(z.array(GoalSchema))
    .query(async ({ ctx, input }) => {
      const goals = await ctx.prisma.goal.findMany({
        where: {
          whiteboardId: input.whiteboardId,
          priority: input.priority,
        },
        orderBy: { createdAt: 'desc' },
      });

      return goals;
    }),

  // Get overdue goals
  getOverdue: publicProcedure
    .input(z.object({ whiteboardId: z.string().cuid() }))
    .output(z.array(GoalSchema))
    .query(async ({ ctx, input }) => {
      const now = new Date();
      const goals = await ctx.prisma.goal.findMany({
        where: {
          whiteboardId: input.whiteboardId,
          dueDate: {
            lt: now,
          },
          status: {
            not: 'completed',
          },
        },
        orderBy: { dueDate: 'asc' },
      });

      return goals;
    }),

  // Bulk create goals
  bulkCreate: publicProcedure
    .input(
      z.object({
        whiteboardId: z.string().cuid(),
        goals: z.array(CreateGoalSchema.omit({ whiteboardId: true })),
      })
    )
    .output(z.array(GoalSchema))
    .mutation(async ({ ctx, input }) => {
      const { whiteboardId, goals } = input;

      // Verify whiteboard exists
      const whiteboard = await ctx.prisma.whiteboard.findUnique({
        where: { id: whiteboardId },
      });

      if (!whiteboard) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Whiteboard not found',
        });
      }

      const createdGoals = await ctx.prisma.$transaction(
        goals.map((goal) =>
          ctx.prisma.goal.create({
            data: {
              ...goal,
              whiteboardId,
            },
          })
        )
      );

      return createdGoals;
    }),

  // Delete all goals for a whiteboard
  deleteByWhiteboardId: publicProcedure
    .input(z.object({ whiteboardId: z.string().cuid() }))
    .output(z.object({ count: z.number() }))
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.prisma.goal.deleteMany({
        where: { whiteboardId: input.whiteboardId },
      });

      return { count: result.count };
    }),

  // Search goals by title or description
  search: publicProcedure
    .input(
      z.object({
        whiteboardId: z.string().cuid(),
        query: z.string().min(1),
      })
    )
    .output(z.array(GoalSchema))
    .query(async ({ ctx, input }) => {
      const { whiteboardId, query } = input;

      const goals = await ctx.prisma.goal.findMany({
        where: {
          whiteboardId,
          OR: [
            {
              title: {
                contains: query,
                mode: 'insensitive',
              },
            },
            {
              description: {
                contains: query,
                mode: 'insensitive',
              },
            },
          ],
        },
        orderBy: { createdAt: 'desc' },
      });

      return goals;
    }),
});
