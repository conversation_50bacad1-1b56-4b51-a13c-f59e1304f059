import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { WhiteboardRole, WhiteboardPermission } from '@/types';
import { createTRPCRouter, protectedProcedure, publicProcedure } from '../trpc';
import { logDatabaseQuery, logDatabaseError, logInfo, logError, logDebug } from '@/lib/logger';
import {
  checkViewAccess,
  checkEditAccess,
  checkShareAccess,
  checkManageAccess,
  checkPublicAccess,
  getUserWhiteboardAccess,
  getWhiteboardAccessInfo
} from '../middleware/rbac-simple';

// Define schemas directly in the router to avoid type conflicts
const WhiteboardSchema = z.object({
  id: z.string(),
  title: z.string(),
  content: z.any().nullable(),
  userId: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  isPublic: z.boolean(),
  allowComments: z.boolean(),
  viewMode: z.string(),
  backgroundColor: z.string(),
  gridMode: z.boolean(),
  snapToGrid: z.boolean(),
  theme: z.string(),
  viewportX: z.number(),
  viewportY: z.number(),
  viewportZoom: z.number(),
  // New RBAC fields
  publicAccessLevel: z.string(),
  requiresAuth: z.boolean(),
  allowAnonymousView: z.boolean(),
});

const WhiteboardListResponseSchema = z.object({
  whiteboards: z.array(WhiteboardSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
});

const PaginationSchema = z.object({
  page: z.number().positive().default(1),
  limit: z.number().positive().max(100).default(10),
});

export const whiteboardRouter = createTRPCRouter({
  // Get all whiteboards for the authenticated user with pagination
  getAll: protectedProcedure
    .input(PaginationSchema)
    .output(WhiteboardListResponseSchema)
    .query(async ({ ctx, input }) => {
      const startTime = Date.now();
      const { page, limit } = input;
      const skip = (page - 1) * limit;
      const userId = ctx.session.user.id;

      try {
        logDebug('Fetching whiteboards for user', {
          userId,
          page,
          limit,
          skip
        });

        const [whiteboards, total] = await Promise.all([
          ctx.prisma.whiteboard.findMany({
            where: { userId },
            skip,
            take: limit,
            orderBy: { updatedAt: 'desc' },
          }),
          ctx.prisma.whiteboard.count({ where: { userId } }),
        ]);

        const duration = Date.now() - startTime;
        logDatabaseQuery(
          `findMany whiteboards + count for user ${userId}`,
          duration,
          {
            userId,
            page,
            limit,
            resultCount: whiteboards.length,
            total
          }
        );

        logInfo('Successfully fetched whiteboards', {
          userId,
          count: whiteboards.length,
          total,
          page,
          duration,
        });

        return {
          whiteboards,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        };
      } catch (error) {
        logDatabaseError('getAll whiteboards', error, {
          userId,
          page,
          limit,
          duration: Date.now() - startTime,
        });
        throw error;
      }
    }),

  // Get a single whiteboard by ID
  getById: protectedProcedure
    .input(z.object({ id: z.string().cuid() }))
    .output(WhiteboardSchema.nullable())
    .query(async ({ ctx, input }) => {
      const startTime = Date.now();
      
      // Check access inline instead of using middleware
      const userAccess = await checkViewAccess(ctx, input);
      
      try {
        logDebug('Fetching whiteboard by ID', {
          whiteboardId: input.id,
          userId: ctx.session.user.id
        });

        const whiteboard = await ctx.prisma.whiteboard.findUnique({
          where: { id: input.id },
        });

        const duration = Date.now() - startTime;
        logDatabaseQuery(
          `findUnique whiteboard ${input.id}`,
          duration,
          { whiteboardId: input.id, found: !!whiteboard }
        );

        if (!whiteboard) {
          logInfo('Whiteboard not found', { whiteboardId: input.id });
          return null;
        }

        logInfo('Successfully fetched whiteboard', {
          whiteboardId: input.id,
          title: whiteboard.title,
          duration,
        });

        return whiteboard;
      } catch (error) {
        const duration = Date.now() - startTime;
        logDatabaseError('Failed to fetch whiteboard by ID', error, {
          whiteboardId: input.id,
          duration,
        });
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch whiteboard',
        });
      }
    }),

  // Create a new whiteboard for the authenticated user
  create: protectedProcedure
    .input(
      z.object({
        title: z.string().min(1, 'Whiteboard title is required'),
        content: z.any().optional(),
        isPublic: z.boolean().optional().default(false),
        allowComments: z.boolean().optional().default(true),
        viewMode: z.string().optional().default('edit'),
        backgroundColor: z.string().optional().default('#ffffff'),
        gridMode: z.boolean().optional().default(false),
        snapToGrid: z.boolean().optional().default(false),
        theme: z.string().optional().default('light'),
      })
    )
    .output(WhiteboardSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      const whiteboard = await ctx.prisma.whiteboard.create({
        data: {
          title: input.title,
          content: input.content || {},
          userId,
          isPublic: input.isPublic ?? false,
          allowComments: input.allowComments ?? true,
          viewMode: input.viewMode ?? 'edit',
          backgroundColor: input.backgroundColor ?? '#ffffff',
          gridMode: input.gridMode ?? false,
          snapToGrid: input.snapToGrid ?? false,
          theme: input.theme ?? 'light',
        },
      });

      return whiteboard;
    }),

  // Update a whiteboard with ownership validation
  update: protectedProcedure
    .input(
      z.object({
        id: z.string().cuid(),
        title: z.string().min(1).optional(),
        content: z.any().optional(),
        isPublic: z.boolean().optional(),
        allowComments: z.boolean().optional(),
        viewMode: z.string().optional(),
        backgroundColor: z.string().optional(),
        gridMode: z.boolean().optional(),
        snapToGrid: z.boolean().optional(),
        theme: z.string().optional(),
        viewportX: z.number().optional(),
        viewportY: z.number().optional(),
        viewportZoom: z.number().min(0.1).max(10).optional(),
        // RBAC fields for public access
        publicAccessLevel: z.string().optional(),
        requiresAuth: z.boolean().optional(),
        allowAnonymousView: z.boolean().optional(),
      })
    )
    .output(WhiteboardSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;
      const userId = ctx.session.user.id;

      // Check if whiteboard exists and user owns it
      const existingWhiteboard = await ctx.prisma.whiteboard.findFirst({
        where: {
          id,
          userId, // Ensure user owns the whiteboard
        },
      });

      if (!existingWhiteboard) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Whiteboard not found or you do not have permission to update it',
        });
      }

      // Handle public access logic
      const finalUpdateData = { ...updateData };
      
      // If isPublic is being set to true, ensure proper public access settings
      if (updateData.isPublic === true) {
        finalUpdateData.requiresAuth = finalUpdateData.requiresAuth ?? false;
        finalUpdateData.allowAnonymousView = finalUpdateData.allowAnonymousView ?? true;
        finalUpdateData.publicAccessLevel = finalUpdateData.publicAccessLevel ?? 'view';
      }
      
      // If isPublic is being set to false, reset to private settings
      if (updateData.isPublic === false) {
        finalUpdateData.requiresAuth = true;
        finalUpdateData.allowAnonymousView = false;
        finalUpdateData.publicAccessLevel = 'none';
      }

      const updatedWhiteboard = await ctx.prisma.whiteboard.update({
        where: { id },
        data: finalUpdateData,
      });

      return updatedWhiteboard;
    }),

  // Update whiteboard title with ownership validation
  updateTitle: protectedProcedure
    .input(
      z.object({
        id: z.string().cuid(),
        title: z.string().min(1, 'Title cannot be empty').max(100, 'Title must be less than 100 characters'),
      })
    )
    .output(WhiteboardSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, title } = input;
      const userId = ctx.session.user.id;

      // Check if whiteboard exists and user owns it
      const existingWhiteboard = await ctx.prisma.whiteboard.findFirst({
        where: {
          id,
          userId, // Ensure user owns the whiteboard
        },
      });

      if (!existingWhiteboard) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Whiteboard not found or you do not have permission to update it',
        });
      }

      const updatedWhiteboard = await ctx.prisma.whiteboard.update({
        where: { id },
        data: { title },
      });

      return updatedWhiteboard;
    }),

  // Delete a whiteboard with ownership validation
  delete: protectedProcedure
    .input(z.object({ id: z.string().cuid() }))
    .output(z.object({ success: z.boolean() }))
    .mutation(async ({ ctx, input }) => {
      const { id } = input;
      const userId = ctx.session.user.id;

      // Check if whiteboard exists and user owns it
      const existingWhiteboard = await ctx.prisma.whiteboard.findFirst({
        where: {
          id,
          userId, // Ensure user owns the whiteboard
        },
      });

      if (!existingWhiteboard) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Whiteboard not found or you do not have permission to delete it',
        });
      }

      await ctx.prisma.whiteboard.delete({
        where: { id },
      });

      return { success: true };
    }),

  // Search whiteboards by title for the authenticated user
  search: protectedProcedure
    .input(
      z.object({
        query: z.string().min(1),
        page: z.number().positive().default(1),
        limit: z.number().positive().max(100).default(10),
      })
    )
    .output(WhiteboardListResponseSchema)
    .query(async ({ ctx, input }) => {
      const { query, page, limit } = input;
      const skip = (page - 1) * limit;
      const userId = ctx.session.user.id;

      const where = {
        title: {
          contains: query,
          mode: 'insensitive' as const,
        },
        userId,
      };

      const [whiteboards, total] = await Promise.all([
        ctx.prisma.whiteboard.findMany({
          where,
          skip,
          take: limit,
          orderBy: { updatedAt: 'desc' },
        }),
        ctx.prisma.whiteboard.count({ where }),
      ]);

      return {
        whiteboards,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    }),

  // Get a public whiteboard by ID with RBAC validation (allows unauthenticated access)
  getPublic: publicProcedure
    .input(z.object({ id: z.string().cuid() }))
    .output(WhiteboardSchema.nullable())
    .query(async ({ ctx, input }) => {
      const startTime = Date.now();
      
      // Check public access inline
      const userAccess = await checkPublicAccess(ctx, input);
      
      try {
        logDebug('Fetching public whiteboard by ID', {
          whiteboardId: input.id,
          hasSession: !!ctx.session
        });

        const whiteboard = await ctx.prisma.whiteboard.findUnique({
          where: { id: input.id },
        });

        const duration = Date.now() - startTime;
        logDatabaseQuery(
          `findUnique public whiteboard ${input.id}`,
          duration,
          { whiteboardId: input.id, found: !!whiteboard }
        );

        if (!whiteboard) {
          logInfo('Public whiteboard not found', { whiteboardId: input.id });
          return null;
        }

        logInfo('Successfully fetched public whiteboard', {
          whiteboardId: input.id,
          title: whiteboard.title,
          duration,
        });

        return whiteboard;
      } catch (error) {
        const duration = Date.now() - startTime;
        logDatabaseError('Failed to fetch public whiteboard by ID', error, {
          whiteboardId: input.id,
          duration,
        });
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch public whiteboard',
        });
      }
    }),

  // Get recently updated public whiteboards
  getRecentPublic: publicProcedure
    .input(
      z.object({
        limit: z.number().positive().max(50).default(20),
      })
    )
    .output(
      z.object({
        whiteboards: z.array(
          WhiteboardSchema.extend({
            user: z.object({
              id: z.string(),
              name: z.string().nullable(),
              email: z.string(),
            }),
          })
        ),
      })
    )
    .query(async ({ ctx, input }) => {
      const startTime = Date.now();
      const { limit } = input;

      try {
        logDebug('Fetching recent public whiteboards', {
          limit
        });

        const whiteboards = await ctx.prisma.whiteboard.findMany({
          where: {
            isPublic: true,
          },
          take: limit,
          orderBy: { updatedAt: 'desc' },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });

        const duration = Date.now() - startTime;
        logDatabaseQuery(
          `findMany recent public whiteboards`,
          duration,
          {
            limit,
            resultCount: whiteboards.length
          }
        );

        logInfo('Successfully fetched recent public whiteboards', {
          count: whiteboards.length,
          duration,
        });

        return {
          whiteboards,
        };
      } catch (error) {
        logDatabaseError('getRecentPublic whiteboards', error, {
          limit,
          duration: Date.now() - startTime,
        });
        throw error;
      }
    }),

  // RBAC Endpoints

  // Get whiteboard access information
  getAccessInfo: protectedProcedure
    .input(z.object({ id: z.string().cuid() }))
    .query(async ({ ctx, input }) => {
      // Check manage access inline
      await checkManageAccess(ctx, input);
      
      const { id } = input;
      return await getWhiteboardAccessInfo(ctx, id);
    }),

  // Grant access to a user
  grantAccess: protectedProcedure
    .input(z.object({
      whiteboardId: z.string().cuid(),
      userId: z.string(),
      role: z.nativeEnum(WhiteboardRole),
      permissions: z.array(z.nativeEnum(WhiteboardPermission)).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      // Check manage access inline
      await checkManageAccess(ctx, input);
      
      const { whiteboardId, userId, role, permissions } = input;
      const grantedBy = ctx.session.user.id;

      // Check if user exists
      const targetUser = await ctx.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!targetUser) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }

      // Create or update user access
      const userAccess = await ctx.prisma.whiteboardUserAccess.upsert({
        where: {
          whiteboardId_userId: {
            whiteboardId,
            userId,
          },
        },
        update: {
          role,
          permissions: permissions || [],
          grantedBy,
        },
        create: {
          whiteboardId,
          userId,
          role,
          permissions: permissions || [],
          grantedBy,
        },
      });

      logInfo('User access granted', {
        whiteboardId,
        userId,
        role,
        grantedBy,
      });

      return userAccess;
    }),

  // Revoke access from a user
  revokeAccess: protectedProcedure
    .input(z.object({
      whiteboardId: z.string().cuid(),
      userId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      // Check manage access inline
      await checkManageAccess(ctx, input);
      
      const { whiteboardId, userId } = input;

      await ctx.prisma.whiteboardUserAccess.delete({
        where: {
          whiteboardId_userId: {
            whiteboardId,
            userId,
          },
        },
      });

      logInfo('User access revoked', {
        whiteboardId,
        userId,
        revokedBy: ctx.session.user.id,
      });

      return { success: true };
    }),
});
