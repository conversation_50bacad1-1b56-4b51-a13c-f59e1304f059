import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { createTRPCRouter, publicProcedure } from '../trpc';
import {
  HillChartSchema,
  CreateHillChartSchema,
  UpdateHillChartSchema,
  HillChartItemDbSchema,
  CreateHillChartItemSchema,
  UpdateHillChartItemSchema,
} from '@/lib/schemas';

export const hillChartRouter = createTRPCRouter({
  // Get all hill charts for a whiteboard
  getByWhiteboardId: publicProcedure
    .input(z.object({ whiteboardId: z.string().cuid() }))
    .query(async ({ ctx, input }) => {
      const hillCharts = await ctx.prisma.hillChart.findMany({
        where: { whiteboardId: input.whiteboardId },
        include: {
          items: true,
        },
        orderBy: { createdAt: 'desc' },
      });

      return hillCharts;
    }),

  // Get a single hill chart by ID with items
  getById: publicProcedure
    .input(z.object({ id: z.string().cuid() }))
    .query(async ({ ctx, input }) => {
      const hillChart = await ctx.prisma.hillChart.findUnique({
        where: { id: input.id },
        include: {
          items: true,
        },
      });

      return hillChart;
    }),

  // Create a new hill chart
  create: publicProcedure
    .input(CreateHillChartSchema)
    .output(HillChartSchema)
    .mutation(async ({ ctx, input }) => {
      // Verify whiteboard exists
      const whiteboard = await ctx.prisma.whiteboard.findUnique({
        where: { id: input.whiteboardId },
      });

      if (!whiteboard) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Whiteboard not found',
        });
      }

      const hillChart = await ctx.prisma.hillChart.create({
        data: input,
      });

      return hillChart;
    }),

  // Update a hill chart
  update: publicProcedure
    .input(
      z.object({
        id: z.string().cuid(),
        data: UpdateHillChartSchema,
      })
    )
    .output(HillChartSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, data } = input;

      // Check if hill chart exists
      const existingHillChart = await ctx.prisma.hillChart.findUnique({
        where: { id },
      });

      if (!existingHillChart) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Hill chart not found',
        });
      }

      const updatedHillChart = await ctx.prisma.hillChart.update({
        where: { id },
        data,
      });

      return updatedHillChart;
    }),

  // Delete a hill chart
  delete: publicProcedure
    .input(z.object({ id: z.string().cuid() }))
    .output(z.object({ success: z.boolean() }))
    .mutation(async ({ ctx, input }) => {
      const { id } = input;

      // Check if hill chart exists
      const existingHillChart = await ctx.prisma.hillChart.findUnique({
        where: { id },
      });

      if (!existingHillChart) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Hill chart not found',
        });
      }

      await ctx.prisma.hillChart.delete({
        where: { id },
      });

      return { success: true };
    }),

  // Update hill chart position
  updatePosition: publicProcedure
    .input(
      z.object({
        id: z.string().cuid(),
        positionX: z.number(),
        positionY: z.number(),
      })
    )
    .output(HillChartSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, positionX, positionY } = input;

      const updatedHillChart = await ctx.prisma.hillChart.update({
        where: { id },
        data: {
          positionX,
          positionY,
        },
      });

      return updatedHillChart;
    }),

  // Hill Chart Items operations
  items: createTRPCRouter({
    // Get all items for a hill chart
    getByHillChartId: publicProcedure
      .input(z.object({ hillChartId: z.string().cuid() }))
      .output(z.array(HillChartItemDbSchema))
      .query(async ({ ctx, input }) => {
        const items = await ctx.prisma.hillChartItem.findMany({
          where: { hillChartId: input.hillChartId },
          orderBy: { position: 'asc' },
        });

        return items;
      }),

    // Create a new hill chart item
    create: publicProcedure
      .input(CreateHillChartItemSchema)
      .output(HillChartItemDbSchema)
      .mutation(async ({ ctx, input }) => {
        // Verify hill chart exists
        const hillChart = await ctx.prisma.hillChart.findUnique({
          where: { id: input.hillChartId },
        });

        if (!hillChart) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Hill chart not found',
          });
        }

        const item = await ctx.prisma.hillChartItem.create({
          data: input,
        });

        return item;
      }),

    // Update a hill chart item
    update: publicProcedure
      .input(
        z.object({
          id: z.string().cuid(),
          data: UpdateHillChartItemSchema,
        })
      )
      .output(HillChartItemDbSchema)
      .mutation(async ({ ctx, input }) => {
        const { id, data } = input;

        // Check if item exists
        const existingItem = await ctx.prisma.hillChartItem.findUnique({
          where: { id },
        });

        if (!existingItem) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Hill chart item not found',
          });
        }

        const updatedItem = await ctx.prisma.hillChartItem.update({
          where: { id },
          data,
        });

        return updatedItem;
      }),

    // Delete a hill chart item
    delete: publicProcedure
      .input(z.object({ id: z.string().cuid() }))
      .output(z.object({ success: z.boolean() }))
      .mutation(async ({ ctx, input }) => {
        const { id } = input;

        // Check if item exists
        const existingItem = await ctx.prisma.hillChartItem.findUnique({
          where: { id },
        });

        if (!existingItem) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Hill chart item not found',
          });
        }

        await ctx.prisma.hillChartItem.delete({
          where: { id },
        });

        return { success: true };
      }),

    // Update item position on the hill
    updatePosition: publicProcedure
      .input(
        z.object({
          id: z.string().cuid(),
          position: z.number().min(0).max(100),
        })
      )
      .output(HillChartItemDbSchema)
      .mutation(async ({ ctx, input }) => {
        const { id, position } = input;

        const updatedItem = await ctx.prisma.hillChartItem.update({
          where: { id },
          data: { position },
        });

        return updatedItem;
      }),

    // Bulk create items for a hill chart
    bulkCreate: publicProcedure
      .input(
        z.object({
          hillChartId: z.string().cuid(),
          items: z.array(CreateHillChartItemSchema.omit({ hillChartId: true })),
        })
      )
      .output(z.array(HillChartItemDbSchema))
      .mutation(async ({ ctx, input }) => {
        const { hillChartId, items } = input;

        // Verify hill chart exists
        const hillChart = await ctx.prisma.hillChart.findUnique({
          where: { id: hillChartId },
        });

        if (!hillChart) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Hill chart not found',
          });
        }

        const createdItems = await ctx.prisma.$transaction(
          items.map((item) =>
            ctx.prisma.hillChartItem.create({
              data: {
                ...item,
                hillChartId,
              },
            })
          )
        );

        return createdItems;
      }),
  }),

  // Bulk create hill charts
  bulkCreate: publicProcedure
    .input(
      z.object({
        whiteboardId: z.string().cuid(),
        hillCharts: z.array(CreateHillChartSchema.omit({ whiteboardId: true })),
      })
    )
    .output(z.array(HillChartSchema))
    .mutation(async ({ ctx, input }) => {
      const { whiteboardId, hillCharts } = input;

      // Verify whiteboard exists
      const whiteboard = await ctx.prisma.whiteboard.findUnique({
        where: { id: whiteboardId },
      });

      if (!whiteboard) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Whiteboard not found',
        });
      }

      const createdHillCharts = await ctx.prisma.$transaction(
        hillCharts.map((hillChart) =>
          ctx.prisma.hillChart.create({
            data: {
              ...hillChart,
              whiteboardId,
            },
          })
        )
      );

      return createdHillCharts;
    }),

  // Delete all hill charts for a whiteboard
  deleteByWhiteboardId: publicProcedure
    .input(z.object({ whiteboardId: z.string().cuid() }))
    .output(z.object({ count: z.number() }))
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.prisma.hillChart.deleteMany({
        where: { whiteboardId: input.whiteboardId },
      });

      return { count: result.count };
    }),
});
