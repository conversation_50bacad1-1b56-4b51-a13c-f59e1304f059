import { WhiteboardRole, WhiteboardPermission } from '@/types';

// ============================================================================
// PERMISSION DEFINITIONS
// ============================================================================

// Permission definitions for each role
export const ROLE_PERMISSIONS: Record<WhiteboardRole, WhiteboardPermission[]> = {
  OWNER: [
    WhiteboardPermission.VIEW,
    WhiteboardPermission.EDIT,
    WhiteboardPermission.COMMENT,
    WhiteboardPermission.MANAGE_SETTINGS,
    WhiteboardPermission.MANAGE_ACCESS,
    WhiteboardPermission.DELETE,
    WhiteboardPermission.SHARE,
  ],
  EDITOR: [
    WhiteboardPermission.VIEW,
    WhiteboardPermission.EDIT,
    WhiteboardPermission.COMMENT,
    WhiteboardPermission.SHARE,
  ],
  VIEWER: [
    WhiteboardPermission.VIEW,
  ],
  COMMENTER: [
    WhiteboardPermission.VIEW,
    WhiteboardPermission.COMMENT,
  ],
};

// Public access level permissions
export const PUBLIC_ACCESS_PERMISSIONS: Record<string, WhiteboardPermission[]> = {
  none: [],
  view: [WhiteboardPermission.VIEW],
  comment: [WhiteboardPermission.VIEW, WhiteboardPermission.COMMENT],
  edit: [WhiteboardPermission.VIEW, WhiteboardPermission.EDIT, WhiteboardPermission.COMMENT],
};

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface UserAccess {
  userId?: string;
  role?: WhiteboardRole;
  permissions: WhiteboardPermission[];
  isOwner: boolean;
  isAuthenticated: boolean;
  viaShareLink?: boolean;
}

export interface WhiteboardAccessContext {
  whiteboardId: string;
  ownerId: string;
  isPublic: boolean;
  publicAccessLevel: string;
  requiresAuth: boolean;
  allowAnonymousView: boolean;
}

export interface AccessRequest {
  whiteboardId: string;
  userId?: string;
  userRole?: WhiteboardRole;
  customPermissions?: WhiteboardPermission[];
  shareLinkToken?: string;
  shareLinkRole?: WhiteboardRole;
  shareLinkPermissions?: WhiteboardPermission[];
}

// ============================================================================
// PERMISSION UTILITIES
// ============================================================================

/**
 * Get permissions for a specific role
 */
export function getRolePermissions(role: WhiteboardRole): WhiteboardPermission[] {
  return ROLE_PERMISSIONS[role] || [];
}

/**
 * Get permissions for public access level
 */
export function getPublicAccessPermissions(accessLevel: string): WhiteboardPermission[] {
  return PUBLIC_ACCESS_PERMISSIONS[accessLevel] || [];
}

/**
 * Check if a user has a specific permission
 */
export function hasPermission(
  userAccess: UserAccess,
  permission: WhiteboardPermission
): boolean {
  return userAccess.permissions.includes(permission);
}

/**
 * Check if a user has any of the specified permissions
 */
export function hasAnyPermission(
  userAccess: UserAccess,
  permissions: WhiteboardPermission[]
): boolean {
  return permissions.some(permission => userAccess.permissions.includes(permission));
}

/**
 * Check if a user has all of the specified permissions
 */
export function hasAllPermissions(
  userAccess: UserAccess,
  permissions: WhiteboardPermission[]
): boolean {
  return permissions.every(permission => userAccess.permissions.includes(permission));
}

// ============================================================================
// ACCESS DETERMINATION
// ============================================================================

/**
 * Determine user access for a whiteboard
 */
export function determineUserAccess(
  context: WhiteboardAccessContext,
  request: AccessRequest
): UserAccess {
  const { userId, userRole, customPermissions, shareLinkToken, shareLinkRole, shareLinkPermissions } = request;
  const isAuthenticated = !!userId;
  const isOwner = userId === context.ownerId;

  // Owner has full access
  if (isOwner) {
    return {
      userId,
      role: WhiteboardRole.OWNER,
      permissions: getRolePermissions(WhiteboardRole.OWNER),
      isOwner: true,
      isAuthenticated: true,
    };
  }

  // User has explicit access via share link
  if (shareLinkToken && shareLinkRole) {
    return {
      userId,
      role: shareLinkRole,
      permissions: shareLinkPermissions || getRolePermissions(shareLinkRole),
      isOwner: false,
      isAuthenticated: false,
      viaShareLink: true,
    };
  }

  // User has explicit access via user access record
  if (userId && userRole) {
    return {
      userId,
      role: userRole,
      permissions: customPermissions || getRolePermissions(userRole),
      isOwner: false,
      isAuthenticated: true,
    };
  }

  // Public access
  if (context.isPublic) {
    // Check if authentication is required
    if (context.requiresAuth && !isAuthenticated) {
      return {
        userId,
        permissions: [],
        isOwner: false,
        isAuthenticated,
      };
    }

    // Check if anonymous viewing is allowed
    if (!isAuthenticated && !context.allowAnonymousView) {
      return {
        userId,
        permissions: [],
        isOwner: false,
        isAuthenticated,
      };
    }

    // Grant public access permissions
    return {
      userId,
      permissions: getPublicAccessPermissions(context.publicAccessLevel),
      isOwner: false,
      isAuthenticated,
    };
  }

  // No access
  return {
    userId,
    permissions: [],
    isOwner: false,
    isAuthenticated,
  };
}

// ============================================================================
// ROLE MANAGEMENT
// ============================================================================

/**
 * Validate if a role can be assigned by another role
 */
export function canAssignRole(
  assignerRole: WhiteboardRole,
  targetRole: WhiteboardRole
): boolean {
  // Only owners can assign any role
  if (assignerRole === WhiteboardRole.OWNER) {
    return true;
  }

  // Editors can only assign viewer and commenter roles
  if (assignerRole === WhiteboardRole.EDITOR) {
    return [WhiteboardRole.VIEWER, WhiteboardRole.COMMENTER].includes(targetRole);
  }

  // Viewers and commenters cannot assign roles
  return false;
}

/**
 * Get the maximum role that can be assigned by a user
 */
export function getMaxAssignableRole(assignerRole: WhiteboardRole): WhiteboardRole {
  switch (assignerRole) {
    case WhiteboardRole.OWNER:
      return WhiteboardRole.EDITOR; // Owners can assign up to editor
    case WhiteboardRole.EDITOR:
      return WhiteboardRole.COMMENTER; // Editors can assign up to commenter
    default:
      return WhiteboardRole.VIEWER; // Others can only assign viewer
  }
}

// ============================================================================
// ACTION CHECKS
// ============================================================================

/**
 * Check if a user can perform an action on a whiteboard
 */
export function canPerformAction(
  userAccess: UserAccess,
  action: 'view' | 'edit' | 'comment' | 'manage' | 'share' | 'delete'
): boolean {
  switch (action) {
    case 'view':
      return hasPermission(userAccess, WhiteboardPermission.VIEW);
    case 'edit':
      return hasPermission(userAccess, WhiteboardPermission.EDIT);
    case 'comment':
      return hasPermission(userAccess, WhiteboardPermission.COMMENT);
    case 'manage':
      return hasAnyPermission(userAccess, [
        WhiteboardPermission.MANAGE_SETTINGS,
        WhiteboardPermission.MANAGE_ACCESS,
      ]);
    case 'share':
      return hasPermission(userAccess, WhiteboardPermission.SHARE);
    case 'delete':
      return hasPermission(userAccess, WhiteboardPermission.DELETE);
    default:
      return false;
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Generate a secure share token
 */
export function generateShareToken(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Calculate expiration date for invitations
 */
export function getInvitationExpiry(days: number = 7): Date {
  const expiry = new Date();
  expiry.setDate(expiry.getDate() + days);
  return expiry;
}

/**
 * Check if an invitation is expired
 */
export function isInvitationExpired(expiresAt: Date): boolean {
  return new Date() > expiresAt;
}

/**
 * Get available roles for a user to assign
 */
export function getAvailableRoles(assignerRole: WhiteboardRole): WhiteboardRole[] {
  const allRoles = Object.values(WhiteboardRole);
  return allRoles.filter(role => canAssignRole(assignerRole, role));
}

/**
 * Get role display name
 */
export function getRoleDisplayName(role: WhiteboardRole): string {
  switch (role) {
    case WhiteboardRole.OWNER:
      return 'Owner';
    case WhiteboardRole.EDITOR:
      return 'Editor';
    case WhiteboardRole.COMMENTER:
      return 'Commenter';
    case WhiteboardRole.VIEWER:
      return 'Viewer';
    default:
      return 'Unknown';
  }
}

/**
 * Get role color for UI
 */
export function getRoleColor(role: WhiteboardRole): 'red' | 'blue' | 'green' | 'gray' {
  switch (role) {
    case WhiteboardRole.OWNER:
      return 'red';
    case WhiteboardRole.EDITOR:
      return 'blue';
    case WhiteboardRole.COMMENTER:
      return 'green';
    case WhiteboardRole.VIEWER:
      return 'gray';
    default:
      return 'gray';
  }
}
