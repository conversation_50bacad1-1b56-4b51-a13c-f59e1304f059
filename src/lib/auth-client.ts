import { createAuthClient } from "better-auth/react";

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
});

console.log('process.env.NEXT_PUBLIC_APP_URL ', process.env.NEXT_PUBLIC_APP_URL )
export const {
  signIn,
  signOut,
  signUp,
  useSession,
} = authClient;

// Google OAuth sign-in helper
export const signInWithGoogle = async () => {
  return await authClient.signIn.social({
    provider: "google",
  });
};

// Google OAuth sign-in with additional scopes
export const signInWithGoogleScopes = async (scopes?: string[]) => {
  return await authClient.signIn.social({
    provider: "google",
    scopes,
  });
};

// Link Google account for additional permissions
export const linkGoogleAccount = async (scopes?: string[]) => {
  return await authClient.linkSocial({
    provider: "google",
    scopes,
  });
};
