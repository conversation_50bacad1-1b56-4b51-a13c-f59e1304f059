import { betterAuth } from "better-auth";
import { Kysely, PostgresDialect } from "kysely";
import { Pool } from "pg";

// Use the full DATABASE_URL if available, otherwise fallback to Supabase
const databaseUrl = process.env.DATABASE_URL || process.env.SUPABASE_DATABASE_URL;

if (!databaseUrl) {
  throw new Error('DATABASE_URL or SUPABASE_DATABASE_URL environment variable is required');
}

console.log('Database URL:', databaseUrl);

// Create Kysely instance for PostgreSQL with proper SSL configuration for Supabase
const db = new Kysely({
  dialect: new PostgresDialect({
    pool: new Pool({
      connectionString: databaseUrl,
      ssl: {
        rejectUnauthorized: false, // Supabase requires SSL
      },
      max: 10,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    }),
  }),
});

// Initialize BetterAuth with error handling for build time
let authInstance: ReturnType<typeof betterAuth> | { handler: () => Response; api: object };

try {
  authInstance = betterAuth({
    database: new Pool({
      connectionString: databaseUrl,
      ssl: {
        rejectUnauthorized: false, // Supabase requires SSL
      },
      max: 10,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    }),

    emailAndPassword: {
      enabled: true,
      requireEmailVerification: false,
    },
    socialProviders: {
      google: {
        clientId: process.env.GOOGLE_CLIENT_ID as string,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
        accessType: "offline", // Always get refresh token
        prompt: "select_account", // Always ask to select account
      },
    },
    session: {
      expiresIn: 60 * 60 * 24 * 7, // 7 days
      updateAge: 60 * 60 * 24, // 1 day
    },
    user: {
      additionalFields: {
        name: {
          type: "string",
          required: false,
        },
      },
    },
    secret: process.env.BETTER_AUTH_SECRET || "fallback-secret-key-for-development",
    baseURL: process.env.BETTER_AUTH_URL || "https://anchorboard.xyz/",
    // (process.env.NODE_ENV == "production" ? "https://anchorboard.xyz/" : "http://localhost:3000"),
  });
} catch (error) {
  console.warn('BetterAuth initialization failed during build:', error);
  // Create a minimal fallback for build time
  authInstance = {
    handler: () => new Response('Auth not available during build', { status: 503 }),
    api: {},
  };
}

export const auth = authInstance;
