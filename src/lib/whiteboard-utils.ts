import type { Node, Edge, Viewport } from 'reactflow';
import type {
  WhiteboardData,
  CountdownTimerNodeData,
  GoalNodeData,
  HillChartNodeData,
  URLNodeData,
  CountdownTimer,
  Goal,
  HillChart,
  ReactFlowData
} from '@/types';

/**
 * Transforms React Flow data to the storage format
 */
export function transformReactFlowDataForStorage(
  nodes: Node[],
  edges: Edge[],
  viewport: Viewport,
  additionalData?: Record<string, unknown>
): string {
  const data: ReactFlowData & Record<string, unknown> = {
    nodes,
    edges,
    viewport
  };

  // Only include additional data if it's not empty arrays (for backward compatibility)
  if (additionalData) {
    Object.entries(additionalData).forEach(([key, value]) => {
      if (Array.isArray(value) && value.length > 0) {
        data[key] = value;
      } else if (!Array.isArray(value)) {
        data[key] = value;
      }
    });
  }

  return JSON.stringify(data);
}

/**
 * Transforms stored data back to React Flow format
 */
export function transformStoredDataToReactFlow(
  storedData: string
): {
  nodes: Node[],
  edges: Edge[],
  viewport: Viewport,
  additionalData: Record<string, unknown>
} {
  try {
    const parsed = JSON.parse(storedData);
    let nodes = parsed.nodes || [];

    // Convert legacy countdown timers, goals, and hill charts to nodes
    if (parsed.countdownTimers && Array.isArray(parsed.countdownTimers)) {
      const timerNodes = parsed.countdownTimers.map((timer: CountdownTimer) =>
        createCountdownTimerNode(timer, timer.position)
      );
      nodes = [...nodes, ...timerNodes];
    }

    if (parsed.goals && Array.isArray(parsed.goals)) {
      const goalNodes = parsed.goals.map((goal: Goal) =>
        createGoalNode(goal, goal.position)
      );
      nodes = [...nodes, ...goalNodes];
    }

    if (parsed.hillCharts && Array.isArray(parsed.hillCharts)) {
      const hillChartNodes = parsed.hillCharts.map((hillChart: HillChart) =>
        createHillChartNode(hillChart, hillChart.position)
      );
      nodes = [...nodes, ...hillChartNodes];
    }

    return {
      nodes,
      edges: parsed.edges || [],
      viewport: parsed.viewport || { x: 0, y: 0, zoom: 1 },
      additionalData: {
        // Keep empty arrays for backward compatibility
        countdownTimers: [],
        goals: [],
        hillCharts: []
      }
    };
  } catch (error) {
    console.error('Failed to parse stored whiteboard data:', error);
    return {
      nodes: [],
      edges: [],
      viewport: { x: 0, y: 0, zoom: 1 },
      additionalData: {
        countdownTimers: [],
        goals: [],
        hillCharts: []
      }
    };
  }
}

/**
 * Creates a default node with common properties
 */
export function createDefaultNode(
  type: string,
  position: { x: number; y: number },
  label: string,
  customData?: Record<string, unknown>
): Node {
  const baseData: Record<string, unknown> = {
    label,
    color: '#374151',
    backgroundColor: '#ffffff',
    fontSize: 14,
    ...customData
  };

  // Adjust default properties based on node type
  switch (type) {
    case 'circleNode':
      baseData.backgroundColor = '#dbeafe';
      baseData.width = 100;
      baseData.height = 100;
      break;
    case 'rectangleNode':
      baseData.backgroundColor = '#f3f4f6';
      baseData.width = 150;
      baseData.height = 80;
      break;
    case 'textNode':
    default:
      baseData.backgroundColor = '#ffffff';
      break;
  }

  return {
    id: `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type,
    position,
    data: baseData,
  };
}

/**
 * Creates a countdown timer node from CountdownTimer data
 */
export function createCountdownTimerNode(
  timer: Omit<CountdownTimer, 'id' | 'whiteboardId'>,
  position: { x: number; y: number }
): Node<CountdownTimerNodeData> {
  return {
    id: `countdownTimer-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type: 'countdownTimer',
    position,
    data: {
      title: timer.title,
      endDate: timer.endDate,
      style: timer.style,
      isActive: timer.isActive,
    },
  };
}

/**
 * Creates a goal node from Goal data
 */
export function createGoalNode(
  goal: Omit<Goal, 'id' | 'whiteboardId'>,
  position: { x: number; y: number }
): Node<GoalNodeData> {
  return {
    id: `goal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type: 'goal',
    position,
    data: {
      title: goal.title,
      description: goal.description,
      dueDate: goal.dueDate,
      priority: goal.priority,
      status: goal.status,
      tags: goal.tags,
    },
  };
}

/**
 * Creates a hill chart node from HillChart data
 */
export function createHillChartNode(
  hillChart: Omit<HillChart, 'id' | 'whiteboardId'>,
  position: { x: number; y: number }
): Node<HillChartNodeData> {
  return {
    id: `hillChart-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type: 'hillChart',
    position,
    data: {
      title: hillChart.title,
      items: hillChart.items,
      width: hillChart.width,
      height: hillChart.height,
    },
  };
}

/**
 * Creates a URL node for website shortcuts
 */
export function createUrlNode(
  urlData: {
    title: string;
    url: string;
    description?: string;
    backgroundColor?: string;
    textColor?: string;
  },
  position: { x: number; y: number }
): Node {
  return {
    id: `url-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type: 'url',
    position,
    data: {
      title: urlData.title,
      url: urlData.url,
      description: urlData.description,
      backgroundColor: urlData.backgroundColor || '#ffffff',
      textColor: urlData.textColor || '#1f2937',
    },
  };
}

/**
 * Creates a todo list node for task management
 */
export function createTodoListNode(
  todoData: {
    title: string;
    items?: Array<{
      id: string;
      text: string;
      completed: boolean;
      createdAt: Date;
    }>;
    backgroundColor?: string;
    textColor?: string;
    maxItems?: number;
  },
  position: { x: number; y: number }
): Node {
  return {
    id: `todoList-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type: 'todoList',
    position,
    data: {
      title: todoData.title,
      items: todoData.items || [],
      backgroundColor: todoData.backgroundColor || '#ffffff',
      textColor: todoData.textColor || '#1f2937',
      maxItems: todoData.maxItems || 20,
    },
  };
}

/**
 * Validates React Flow data structure
 */
export function validateReactFlowData(data: unknown): boolean {
  if (!data || typeof data !== 'object') {
    return false;
  }

  const dataObj = data as Record<string, unknown>;

  // Check if nodes is an array
  if (!Array.isArray(dataObj.nodes)) {
    return false;
  }

  // Check if edges is an array
  if (!Array.isArray(dataObj.edges)) {
    return false;
  }

  // Check if viewport has required properties
  const viewport = dataObj.viewport as Record<string, unknown>;
  if (!viewport ||
      typeof viewport.x !== 'number' ||
      typeof viewport.y !== 'number' ||
      typeof viewport.zoom !== 'number') {
    return false;
  }

  // Validate each node has required properties
  for (const node of dataObj.nodes) {
    const nodeObj = node as Record<string, unknown>;
    const position = nodeObj.position as Record<string, unknown>;
    if (!nodeObj.id || !position || typeof position.x !== 'number' || typeof position.y !== 'number') {
      return false;
    }
  }

  // Validate each edge has required properties
  for (const edge of dataObj.edges) {
    const edgeObj = edge as Record<string, unknown>;
    if (!edgeObj.id || !edgeObj.source || !edgeObj.target) {
      return false;
    }
  }

  return true;
}

/**
 * Migrates old Excalidraw data to React Flow format (if needed)
 */
export function migrateExcalidrawToReactFlow(excalidrawData: string): ReactFlowData {
  try {
    const parsed = JSON.parse(excalidrawData);
    
    // If it's already React Flow data, return as is
    if (parsed.nodes && parsed.edges && parsed.viewport) {
      return parsed;
    }

    // If it's Excalidraw data, convert basic elements to nodes
    const nodes: Node[] = [];
    const edges: Edge[] = [];
    
    if (parsed.elements && Array.isArray(parsed.elements)) {
      parsed.elements.forEach((element: Record<string, unknown>, index: number) => {
        if (element.type === 'rectangle' || element.type === 'ellipse' || element.type === 'text') {
          const node: Node = {
            id: (element.id as string) || `migrated-${index}`,
            type: element.type === 'ellipse' ? 'circleNode' :
                  element.type === 'rectangle' ? 'rectangleNode' : 'textNode',
            position: {
              x: (element.x as number) || 0,
              y: (element.y as number) || 0
            },
            data: {
              label: (element.text as string) || (element.type as string) || 'Migrated Element',
              color: (element.strokeColor as string) || '#374151',
              backgroundColor: (element.backgroundColor as string) || '#ffffff',
              fontSize: (element.fontSize as number) || 14,
              width: element.width as number,
              height: element.height as number
            }
          };
          nodes.push(node);
        }
      });
    }

    return {
      nodes,
      edges,
      viewport: { x: 0, y: 0, zoom: 1 }
    };
  } catch (error) {
    console.error('Failed to migrate Excalidraw data:', error);
    return {
      nodes: [],
      edges: [],
      viewport: { x: 0, y: 0, zoom: 1 }
    };
  }
}

/**
 * Exports React Flow data to different formats
 */
export function exportReactFlowData(
  nodes: Node[],
  edges: Edge[],
  viewport: Viewport,
  format: 'json' | 'csv' = 'json'
): string {
  const data = { nodes, edges, viewport };
  
  switch (format) {
    case 'csv':
      // Simple CSV export for nodes
      const csvHeader = 'id,type,label,x,y,width,height\n';
      const csvRows = nodes.map(node => 
        `${node.id},${node.type || 'default'},${node.data?.label || ''},${node.position.x},${node.position.y},${node.data?.width || ''},${node.data?.height || ''}`
      ).join('\n');
      return csvHeader + csvRows;
    
    case 'json':
    default:
      return JSON.stringify(data, null, 2);
  }
}
