import winston from 'winston';
import { Logtail } from '@logtail/node';
import { LogtailTransport } from '@logtail/winston';

//  
// Create a Logtail client (only if source token is provided)
const T = process.env.LOGTAIL_SOURCE_TOKEN ||  'J4vsX3h7CAGZ1pG8gUEAVZsg'
const logtail =T 
  ? new Logtail(T, {
      endpoint: process.env.LOGTAIL_ENDPOINT || 'http://s1376153.eu-nbg-2.betterstackdata.com',
    })
  : null;

// Define log levels and colors
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

winston.addColors(logColors);

// Create custom format for console logging
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`
  )
);

// Create custom format for file/remote logging
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create transports array
const transports: winston.transport[] = [];

// Console transport (always enabled in development)
if (process.env.NODE_ENV === 'development') {
  transports.push(
    new winston.transports.Console({
      format: consoleFormat,
    })
  );
}

// File transport for production
// if (process.env.NODE_ENV === 'production') {
//   transports.push(
//     new winston.transports.File({
//       filename: 'logs/error.log',
//       level: 'error',
//       format: fileFormat,
//     }),
//     new winston.transports.File({
//       filename: 'logs/combined.log',
//       format: fileFormat,
//     })
//   );
// }

// Logtail transport (if configured)
if (logtail) {
  transports.push(new LogtailTransport(logtail));
}

// Create the Winston logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'development' ? 'debug' : 'info'),
  levels: logLevels,
  format: fileFormat,
  transports,
  exitOnError: false,
});

// Create a stream object for Morgan HTTP logging
export const loggerStream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};

// Helper functions for different log levels
export const logError = (message: string, error?: Error | unknown, meta?: object) => {
  const errorInfo = error instanceof Error 
    ? { message: error.message, stack: error.stack, name: error.name }
    : error;
  
  logger.error(message, { error: errorInfo, ...meta });
};

export const logWarn = (message: string, meta?: object) => {
  logger.warn(message, meta);
};

export const logInfo = (message: string, meta?: object) => {
  logger.info(message, meta);
};

export const logDebug = (message: string, meta?: object) => {
  logger.debug(message, meta);
};

export const logHttp = (message: string, meta?: object) => {
  logger.http(message, meta);
};

// Auth-specific logging helpers
export const logAuthSuccess = (userId: string, action: string, meta?: object) => {
  logger.info(`Auth Success: ${action}`, { 
    userId, 
    action, 
    timestamp: new Date().toISOString(),
    ...meta 
  });
};

export const logAuthError = (action: string, error: Error | unknown, meta?: object) => {
  const errorInfo = error instanceof Error 
    ? { message: error.message, stack: error.stack, name: error.name }
    : error;
  
  logger.error(`Auth Error: ${action}`, { 
    action, 
    error: errorInfo,
    timestamp: new Date().toISOString(),
    ...meta 
  });
};

export const logAuthAttempt = (action: string, meta?: object) => {
  logger.info(`Auth Attempt: ${action}`, { 
    action, 
    timestamp: new Date().toISOString(),
    ...meta 
  });
};

// Database-specific logging helpers
export const logDatabaseQuery = (query: string, duration?: number, meta?: object) => {
  logger.debug('Database Query', { 
    query, 
    duration,
    timestamp: new Date().toISOString(),
    ...meta 
  });
};

export const logDatabaseError = (operation: string, error: Error | unknown, meta?: object) => {
  const errorInfo = error instanceof Error 
    ? { message: error.message, stack: error.stack, name: error.name }
    : error;
  
  logger.error(`Database Error: ${operation}`, { 
    operation, 
    error: errorInfo,
    timestamp: new Date().toISOString(),
    ...meta 
  });
};

// API-specific logging helpers
export const logApiRequest = (method: string, url: string, userId?: string, meta?: object) => {
  logger.http(`API Request: ${method} ${url}`, { 
    method, 
    url, 
    userId,
    timestamp: new Date().toISOString(),
    ...meta 
  });
};

export const logApiResponse = (method: string, url: string, statusCode: number, duration?: number, meta?: object) => {
  logger.http(`API Response: ${method} ${url} - ${statusCode}`, { 
    method, 
    url, 
    statusCode,
    duration,
    timestamp: new Date().toISOString(),
    ...meta 
  });
};

export const logApiError = (method: string, url: string, error: Error | unknown, meta?: object) => {
  const errorInfo = error instanceof Error 
    ? { message: error.message, stack: error.stack, name: error.name }
    : error;
  
  logger.error(`API Error: ${method} ${url}`, { 
    method, 
    url, 
    error: errorInfo,
    timestamp: new Date().toISOString(),
    ...meta 
  });
};

export default logger;
