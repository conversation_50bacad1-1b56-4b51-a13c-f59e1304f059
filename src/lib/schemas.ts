import { z } from 'zod';

// Base schemas for common types
export const PositionSchema = z.object({
  x: z.number(),
  y: z.number(),
});

export const ViewportSchema = z.object({
  x: z.number(),
  y: z.number(),
  zoom: z.number(),
});

// User schemas
export const UserSchema = z.object({
  id: z.string().cuid(),
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address'),
  avatar: z.string().url().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const CreateUserSchema = UserSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const UpdateUserSchema = CreateUserSchema.partial();

// User Session schemas
export const UserSessionSchema = z.object({
  id: z.string().cuid(),
  userId: z.string().cuid(),
  sessionId: z.string(),
  isActive: z.boolean().default(true),
  lastSeen: z.date(),
  createdAt: z.date(),
});

export const CreateUserSessionSchema = UserSessionSchema.omit({
  id: true,
  createdAt: true,
});

// Whiteboard Settings schemas
export const WhiteboardSettingsSchema = z.object({
  isPublic: z.boolean().default(false),
  allowComments: z.boolean().default(true),
  viewMode: z.enum(['edit', 'view']).default('edit'),
  backgroundColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid hex color').default('#ffffff'),
  gridMode: z.boolean().default(false),
  snapToGrid: z.boolean().default(false),
  theme: z.enum(['light', 'dark']).default('light'),
});

// Whiteboard schemas
export const WhiteboardSchema = z.object({
  id: z.string().cuid(),
  title: z.string().min(1, 'Whiteboard title is required'),
  content: z.any().optional(), // Use z.any() for JSON fields to match Prisma JsonValue
  userId: z.string().cuid(),
  createdAt: z.date(),
  updatedAt: z.date(),
  // Settings
  isPublic: z.boolean(),
  allowComments: z.boolean(),
  viewMode: z.string(), // Use string instead of enum to match database
  backgroundColor: z.string(),
  gridMode: z.boolean(),
  snapToGrid: z.boolean(),
  theme: z.string(), // Use string instead of enum to match database
  // Viewport
  viewportX: z.number(),
  viewportY: z.number(),
  viewportZoom: z.number(),
});

export const CreateWhiteboardSchema = WhiteboardSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const UpdateWhiteboardSchema = CreateWhiteboardSchema.partial().extend({
  title: z.string().min(1, 'Whiteboard title is required').optional(),
});

// Node data schemas for different node types
export const CustomNodeDataSchema = z.object({
  label: z.string(),
  color: z.string().optional(),
  backgroundColor: z.string().optional(),
  fontSize: z.number().positive().optional(),
  width: z.number().positive().optional(),
  height: z.number().positive().optional(),
});

export const CountdownTimerNodeDataSchema = z.object({
  title: z.string().min(1, 'Timer title is required'),
  endDate: z.date(),
  style: z.object({
    color: z.string().default('#000000'),
    fontSize: z.number().positive().default(16),
    backgroundColor: z.string().optional(),
  }),
  isActive: z.boolean().default(true),
});

export const GoalNodeDataSchema = z.object({
  title: z.string().min(1, 'Goal title is required'),
  description: z.string().optional(),
  dueDate: z.date().optional(),
  priority: z.enum(['low', 'medium', 'high']).default('medium'),
  status: z.enum(['not-started', 'in-progress', 'completed']).default('not-started'),
  tags: z.array(z.string()).default([]),
});

export const HillChartItemSchema = z.object({
  id: z.string().cuid(),
  name: z.string().min(1, 'Item name is required'),
  position: z.number().min(0).max(100),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid hex color'),
  description: z.string().optional(),
});

export const HillChartNodeDataSchema = z.object({
  title: z.string().min(1, 'Hill chart title is required'),
  items: z.array(HillChartItemSchema).default([]),
  width: z.number().positive().default(400),
  height: z.number().positive().default(200),
});

export const URLNodeDataSchema = z.object({
  title: z.string().min(1, 'URL title is required'),
  url: z.string().min(1, 'URL is required'),
  favicon: z.string().optional(),
  description: z.string().optional(),
  backgroundColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid hex color').optional(),
  textColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid hex color').optional(),
});

// Whiteboard Node schemas
export const WhiteboardNodeSchema = z.object({
  id: z.string().cuid(),
  whiteboardId: z.string().cuid(),
  nodeId: z.string(),
  type: z.string(),
  positionX: z.number(),
  positionY: z.number(),
  width: z.number().positive().optional(),
  height: z.number().positive().optional(),
  data: z.record(z.unknown()),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const CreateWhiteboardNodeSchema = WhiteboardNodeSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const UpdateWhiteboardNodeSchema = CreateWhiteboardNodeSchema.partial().extend({
  nodeId: z.string().optional(),
  whiteboardId: z.string().cuid().optional(),
});

// Whiteboard Edge schemas
export const CustomEdgeDataSchema = z.object({
  label: z.string().optional(),
  color: z.string().optional(),
  strokeWidth: z.number().positive().optional(),
});

export const WhiteboardEdgeSchema = z.object({
  id: z.string().cuid(),
  whiteboardId: z.string().cuid(),
  edgeId: z.string(),
  source: z.string(),
  target: z.string(),
  type: z.string().default('default'),
  data: z.record(z.unknown()).optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const CreateWhiteboardEdgeSchema = WhiteboardEdgeSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const UpdateWhiteboardEdgeSchema = CreateWhiteboardEdgeSchema.partial().extend({
  edgeId: z.string().optional(),
  whiteboardId: z.string().cuid().optional(),
});

// Countdown Timer schemas
export const CountdownTimerSchema = z.object({
  id: z.string().cuid(),
  whiteboardId: z.string().cuid(),
  title: z.string().min(1, 'Timer title is required'),
  endDate: z.date(),
  positionX: z.number(),
  positionY: z.number(),
  isActive: z.boolean().default(true),
  color: z.string().default('#000000'),
  fontSize: z.number().positive().default(16),
  backgroundColor: z.string().nullable().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const CreateCountdownTimerSchema = CountdownTimerSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const UpdateCountdownTimerSchema = CreateCountdownTimerSchema.partial().extend({
  whiteboardId: z.string().cuid().optional(),
});

// Goal schemas
export const GoalSchema = z.object({
  id: z.string().cuid(),
  whiteboardId: z.string().cuid(),
  title: z.string().min(1, 'Goal title is required'),
  description: z.string().nullable().optional(),
  dueDate: z.date().nullable().optional(),
  priority: z.string().optional(), // Allow any string from database
  status: z.string().optional(), // Allow any string from database
  positionX: z.number(),
  positionY: z.number(),
  tags: z.string().default(''), // Comma-separated tags in database
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const CreateGoalSchema = GoalSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const UpdateGoalSchema = CreateGoalSchema.partial().extend({
  whiteboardId: z.string().cuid().optional(),
});

// Hill Chart schemas
export const HillChartSchema = z.object({
  id: z.string().cuid(),
  whiteboardId: z.string().cuid(),
  title: z.string().min(1, 'Hill chart title is required'),
  positionX: z.number(),
  positionY: z.number(),
  width: z.number().positive().default(400),
  height: z.number().positive().default(200),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const CreateHillChartSchema = HillChartSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const UpdateHillChartSchema = CreateHillChartSchema.partial().extend({
  whiteboardId: z.string().cuid().optional(),
});

// Hill Chart Item schemas
export const HillChartItemDbSchema = z.object({
  id: z.string().cuid(),
  hillChartId: z.string().cuid(),
  name: z.string().min(1, 'Item name is required'),
  position: z.number().min(0).max(100),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid hex color'),
  description: z.string().nullable().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const CreateHillChartItemSchema = HillChartItemDbSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const UpdateHillChartItemSchema = CreateHillChartItemSchema.partial().extend({
  hillChartId: z.string().cuid().optional(),
});

// React Flow data schemas
export const ReactFlowNodeSchema = z.object({
  id: z.string(),
  type: z.string().optional(),
  position: PositionSchema,
  data: z.record(z.unknown()),
  width: z.number().optional(),
  height: z.number().optional(),
  selected: z.boolean().optional(),
  dragging: z.boolean().optional(),
});

export const ReactFlowEdgeSchema = z.object({
  id: z.string(),
  source: z.string(),
  target: z.string(),
  type: z.string().optional(),
  data: z.record(z.unknown()).optional(),
  animated: z.boolean().optional(),
  selected: z.boolean().optional(),
});

export const ReactFlowDataSchema = z.object({
  nodes: z.array(ReactFlowNodeSchema),
  edges: z.array(ReactFlowEdgeSchema),
  viewport: ViewportSchema,
});

// API request/response schemas
export const PaginationSchema = z.object({
  page: z.number().positive().default(1),
  limit: z.number().positive().max(100).default(10),
});

export const WhiteboardListResponseSchema = z.object({
  whiteboards: z.array(WhiteboardSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
});

// Type inference from schemas
export type User = z.infer<typeof UserSchema>;
export type CreateUser = z.infer<typeof CreateUserSchema>;
export type UpdateUser = z.infer<typeof UpdateUserSchema>;

export type UserSession = z.infer<typeof UserSessionSchema>;
export type CreateUserSession = z.infer<typeof CreateUserSessionSchema>;

export type WhiteboardSettings = z.infer<typeof WhiteboardSettingsSchema>;
export type Whiteboard = z.infer<typeof WhiteboardSchema>;
export type CreateWhiteboard = z.infer<typeof CreateWhiteboardSchema>;
export type UpdateWhiteboard = z.infer<typeof UpdateWhiteboardSchema>;

export type WhiteboardNode = z.infer<typeof WhiteboardNodeSchema>;
export type CreateWhiteboardNode = z.infer<typeof CreateWhiteboardNodeSchema>;
export type UpdateWhiteboardNode = z.infer<typeof UpdateWhiteboardNodeSchema>;

export type WhiteboardEdge = z.infer<typeof WhiteboardEdgeSchema>;
export type CreateWhiteboardEdge = z.infer<typeof CreateWhiteboardEdgeSchema>;
export type UpdateWhiteboardEdge = z.infer<typeof UpdateWhiteboardEdgeSchema>;

export type CountdownTimer = z.infer<typeof CountdownTimerSchema>;
export type CreateCountdownTimer = z.infer<typeof CreateCountdownTimerSchema>;
export type UpdateCountdownTimer = z.infer<typeof UpdateCountdownTimerSchema>;

export type Goal = z.infer<typeof GoalSchema>;
export type CreateGoal = z.infer<typeof CreateGoalSchema>;
export type UpdateGoal = z.infer<typeof UpdateGoalSchema>;

export type HillChart = z.infer<typeof HillChartSchema>;
export type CreateHillChart = z.infer<typeof CreateHillChartSchema>;
export type UpdateHillChart = z.infer<typeof UpdateHillChartSchema>;

export type HillChartItem = z.infer<typeof HillChartItemDbSchema>;
export type CreateHillChartItem = z.infer<typeof CreateHillChartItemSchema>;
export type UpdateHillChartItem = z.infer<typeof UpdateHillChartItemSchema>;

export type ReactFlowData = z.infer<typeof ReactFlowDataSchema>;
export type Pagination = z.infer<typeof PaginationSchema>;
export type WhiteboardListResponse = z.infer<typeof WhiteboardListResponseSchema>;
