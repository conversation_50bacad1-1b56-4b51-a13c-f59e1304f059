'use client';

import { useCallback } from 'react';
import { api } from '@/lib/trpc';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

// Types for whiteboard data
export interface WhiteboardData {
  id: string;
  title: string;
  content?: Record<string, unknown> | null;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  isPublic: boolean;
  allowComments: boolean;
  viewMode: string;
  backgroundColor: string;
  gridMode: boolean;
  snapToGrid: boolean;
  theme: string;
  viewportX: number;
  viewportY: number;
  viewportZoom: number;
}

export interface CreateWhiteboardInput {
  title: string;
  content?: Record<string, unknown>;
  isPublic?: boolean;
  allowComments?: boolean;
  viewMode?: string;
  backgroundColor?: string;
  gridMode?: boolean;
  snapToGrid?: boolean;
  theme?: string;
}

export interface UpdateWhiteboardInput {
  id: string;
  title?: string;
  content?: Record<string, unknown>;
  isPublic?: boolean;
  allowComments?: boolean;
  viewMode?: string;
  backgroundColor?: string;
  gridMode?: boolean;
  snapToGrid?: boolean;
  theme?: string;
  viewportX?: number;
  viewportY?: number;
  viewportZoom?: number;
}

/**
 * Hook for fetching all whiteboards for the authenticated user
 */
export function useWhiteboards(page = 1, limit = 10) {
  return api.whiteboard.getAll.useQuery(
    { page, limit },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: (failureCount, error) => {
        // Don't retry on authentication errors
        if (error?.data?.code === 'UNAUTHORIZED') {
          return false;
        }
        return failureCount < 3;
      },
      refetchOnWindowFocus: false,
    }
  );
}

/**
 * Hook for fetching a single whiteboard by ID
 */
export function useWhiteboard(id: string | undefined) {
  return api.whiteboard.getById.useQuery(
    { id: id! },
    {
      enabled: !!id,
      staleTime: 2 * 60 * 1000, // 2 minutes
      retry: (failureCount, error) => {
        // Don't retry on not found or unauthorized errors
        if (error?.data?.code === 'NOT_FOUND' || error?.data?.code === 'UNAUTHORIZED') {
          return false;
        }
        return failureCount < 2;
      },
    }
  );
}

/**
 * Hook for creating a new whiteboard with optimistic updates
 */
export function useCreateWhiteboard() {
  const queryClient = useQueryClient();
  
  return api.whiteboard.create.useMutation({
    onMutate: async (newWhiteboard) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['whiteboard', 'getAll'] });

      // Snapshot the previous value
      const previousWhiteboards = queryClient.getQueryData(['whiteboard', 'getAll']);

      // Optimistically update to the new value
      const optimisticWhiteboard: WhiteboardData = {
        id: `temp-${Date.now()}`,
        title: newWhiteboard.title,
        content: newWhiteboard.content || {},
        userId: 'current-user', // This will be replaced by the server
        createdAt: new Date(),
        updatedAt: new Date(),
        isPublic: newWhiteboard.isPublic ?? false,
        allowComments: newWhiteboard.allowComments ?? true,
        viewMode: newWhiteboard.viewMode ?? 'edit',
        backgroundColor: newWhiteboard.backgroundColor ?? '#ffffff',
        gridMode: newWhiteboard.gridMode ?? false,
        snapToGrid: newWhiteboard.snapToGrid ?? false,
        theme: newWhiteboard.theme ?? 'light',
        viewportX: 0,
        viewportY: 0,
        viewportZoom: 1,
      };

      queryClient.setQueryData(['whiteboard', 'getAll'], (old: any) => {
        if (!old) return old;
        return {
          ...old,
          whiteboards: [optimisticWhiteboard, ...old.whiteboards],
          total: old.total + 1,
        };
      });

      return { previousWhiteboards };
    },
    onError: (_err, _newWhiteboard, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      queryClient.setQueryData(['whiteboard', 'getAll'], context?.previousWhiteboards);
      toast.error('Failed to create whiteboard. Please try again.');
    },
    onSuccess: (_data) => {
      toast.success('Whiteboard created successfully!');
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['whiteboard', 'getAll'] });
    },
  });
}

/**
 * Hook for updating a whiteboard with optimistic updates
 */
export function useUpdateWhiteboard() {
  const queryClient = useQueryClient();
  
  return api.whiteboard.update.useMutation({
    onMutate: async (updatedWhiteboard) => {
      const { id } = updatedWhiteboard;
      
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['whiteboard', 'getById', { id }] });
      await queryClient.cancelQueries({ queryKey: ['whiteboard', 'getAll'] });

      // Snapshot the previous values
      const previousWhiteboard = queryClient.getQueryData(['whiteboard', 'getById', { id }]);
      const previousWhiteboards = queryClient.getQueryData(['whiteboard', 'getAll']);

      // Optimistically update the single whiteboard
      queryClient.setQueryData(['whiteboard', 'getById', { id }], (old: WhiteboardData | null) => {
        if (!old) return old;
        return { ...old, ...updatedWhiteboard, updatedAt: new Date() };
      });

      // Optimistically update the whiteboard list
      queryClient.setQueryData(['whiteboard', 'getAll'], (old: any) => {
        if (!old) return old;
        return {
          ...old,
          whiteboards: old.whiteboards.map((wb: WhiteboardData) =>
            wb.id === id ? { ...wb, ...updatedWhiteboard, updatedAt: new Date() } : wb
          ),
        };
      });

      return { previousWhiteboard, previousWhiteboards };
    },
    onError: (_err, updatedWhiteboard, context) => {
      const { id } = updatedWhiteboard;

      // If the mutation fails, use the context to roll back
      queryClient.setQueryData(['whiteboard', 'getById', { id }], context?.previousWhiteboard);
      queryClient.setQueryData(['whiteboard', 'getAll'], context?.previousWhiteboards);
      toast.error('Failed to update whiteboard. Please try again.');
    },
    onSuccess: (_data) => {
      toast.success('Whiteboard updated successfully!');
    },
    onSettled: (_data, _error, variables) => {
      const { id } = variables;
      
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['whiteboard', 'getById', { id }] });
      queryClient.invalidateQueries({ queryKey: ['whiteboard', 'getAll'] });
    },
  });
}

/**
 * Hook for deleting a whiteboard with optimistic updates
 */
export function useDeleteWhiteboard() {
  const queryClient = useQueryClient();
  
  return api.whiteboard.delete.useMutation({
    onMutate: async ({ id }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['whiteboard', 'getAll'] });

      // Snapshot the previous value
      const previousWhiteboards = queryClient.getQueryData(['whiteboard', 'getAll']);

      // Optimistically remove the whiteboard
      queryClient.setQueryData(['whiteboard', 'getAll'], (old: any) => {
        if (!old) return old;
        return {
          ...old,
          whiteboards: old.whiteboards.filter((wb: WhiteboardData) => wb.id !== id),
          total: old.total - 1,
        };
      });

      // Remove the individual whiteboard from cache
      queryClient.removeQueries({ queryKey: ['whiteboard', 'getById', { id }] });

      return { previousWhiteboards };
    },
    onError: (_err, { id: _id }, context) => {
      // If the mutation fails, use the context to roll back
      queryClient.setQueryData(['whiteboard', 'getAll'], context?.previousWhiteboards);
      toast.error('Failed to delete whiteboard. Please try again.');
    },
    onSuccess: () => {
      toast.success('Whiteboard deleted successfully!');
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['whiteboard', 'getAll'] });
    },
  });
}

/**
 * Hook for updating whiteboard title with optimistic updates
 */
export function useUpdateWhiteboardTitle() {
  const queryClient = useQueryClient();

  return api.whiteboard.updateTitle.useMutation({
    onMutate: async ({ id, title }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['whiteboard', 'getById', { id }] });
      await queryClient.cancelQueries({ 
        predicate: (query) => 
          Array.isArray(query.queryKey) && 
          query.queryKey[0] === 'whiteboard' && 
          query.queryKey[1] === 'getAll'
      });

      // Snapshot the previous values
      const previousWhiteboard = queryClient.getQueryData(['whiteboard', 'getById', { id }]);
      const previousWhiteboards = queryClient.getQueryData(['whiteboard', 'getAll']);

      // Optimistically update the individual whiteboard
      queryClient.setQueryData(['whiteboard', 'getById', { id }], (old: any) => {
        if (!old) return old;
        return {
          ...old,
          title,
          updatedAt: new Date(),
        };
      });

      // Optimistically update all variations of the whiteboard list
      queryClient.setQueriesData(
        { 
          predicate: (query) => 
            Array.isArray(query.queryKey) && 
            query.queryKey[0] === 'whiteboard' && 
            query.queryKey[1] === 'getAll'
        },
        (old: any) => {
          if (!old || !old.whiteboards) return old;
          return {
            ...old,
            whiteboards: old.whiteboards.map((wb: WhiteboardData) =>
              wb.id === id ? { ...wb, title, updatedAt: new Date() } : wb
            ),
          };
        }
      );

      return { previousWhiteboard, previousWhiteboards };
    },
    onError: (_err, { id }, context) => {
      // If the mutation fails, use the context to roll back
      if (context?.previousWhiteboard) {
        queryClient.setQueryData(['whiteboard', 'getById', { id }], context.previousWhiteboard);
      }
      if (context?.previousWhiteboards) {
        queryClient.setQueryData(['whiteboard', 'getAll'], context.previousWhiteboards);
      }
      toast.error('Failed to update whiteboard title. Please try again.');
    },
    onSuccess: async (_data, { id }) => {
      // Invalidate queries to refetch with fresh data
      await queryClient.invalidateQueries({ queryKey: ['whiteboard', 'getById', { id }] });
      await queryClient.invalidateQueries({ 
        predicate: (query) => 
          Array.isArray(query.queryKey) && 
          query.queryKey[0] === 'whiteboard' && 
          query.queryKey[1] === 'getAll'
      });
      
      toast.success('Whiteboard title updated successfully!');
    },
  });
}

/**
 * Hook for searching whiteboards
 */
export function useSearchWhiteboards(query: string, page = 1, limit = 10) {
  return api.whiteboard.search.useQuery(
    { query, page, limit },
    {
      enabled: query.length > 0,
      staleTime: 30 * 1000, // 30 seconds
      retry: 1,
    }
  );
}

/**
 * Hook for debounced whiteboard updates (for auto-save functionality)
 */
export function useAutoSaveWhiteboard() {
  const updateWhiteboard = useUpdateWhiteboard();
  
  const autoSave = useCallback(
    (whiteboardData: UpdateWhiteboardInput) => {
      updateWhiteboard.mutate(whiteboardData, {
        onError: () => {
          // Silent error for auto-save - we don't want to spam the user with error messages
          console.error('Auto-save failed for whiteboard:', whiteboardData.id);
        },
      });
    },
    [updateWhiteboard]
  );

  return {
    autoSave,
    isAutoSaving: updateWhiteboard.isPending,
    autoSaveError: updateWhiteboard.error,
  };
}

/**
 * Hook for fetching a public whiteboard by ID (no authentication required)
 */
export function usePublicWhiteboard(whiteboardId?: string) {
  return api.whiteboard.getPublic.useQuery(
    { id: whiteboardId! },
    {
      enabled: !!whiteboardId,
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 2,
      refetchOnWindowFocus: false,
    }
  );
}

/**
 * Hook for fetching recently updated public whiteboards
 */
export function useRecentPublicWhiteboards(limit = 20) {
  return api.whiteboard.getRecentPublic.useQuery(
    { limit },
    {
      staleTime: 2 * 60 * 1000, // 2 minutes
      refetchOnWindowFocus: true,
      retry: 1,
    }
  );
}
