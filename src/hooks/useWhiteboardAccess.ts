'use client';

import { api } from '@/lib/trpc';
import { WhiteboardRole, WhiteboardPermission } from '@/types';
import { toast } from 'sonner';

/**
 * Hook for managing whiteboard access and permissions
 */
export function useWhiteboardAccess(whiteboardId?: string) {
  // Get access information for a whiteboard
  const {
    data: accessInfo,
    isLoading: isLoadingAccess,
    error: accessError,
    refetch: refetchAccess,
  } = api.whiteboard.getAccessInfo.useQuery(
    { id: whiteboardId! },
    {
      enabled: !!whiteboardId,
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );

  // Grant access mutation
  const grantAccessMutation = api.whiteboard.grantAccess.useMutation({
    onSuccess: () => {
      toast.success('Access granted successfully');
      refetchAccess();
    },
    onError: (error) => {
      toast.error(`Failed to grant access: ${error.message}`);
    },
  });

  // Revoke access mutation
  const revokeAccessMutation = api.whiteboard.revokeAccess.useMutation({
    onSuccess: () => {
      toast.success('Access revoked successfully');
      refetchAccess();
    },
    onError: (error) => {
      toast.error(`Failed to revoke access: ${error.message}`);
    },
  });

  const grantAccess = (userId: string, role: WhiteboardRole, permissions?: WhiteboardPermission[]) => {
    if (!whiteboardId) return;
    
    grantAccessMutation.mutate({
      whiteboardId,
      userId,
      role,
      permissions,
    });
  };

  const revokeAccess = (userId: string) => {
    if (!whiteboardId) return;
    
    revokeAccessMutation.mutate({
      whiteboardId,
      userId,
    });
  };

  return {
    accessInfo,
    isLoadingAccess,
    accessError,
    refetchAccess,
    grantAccess,
    revokeAccess,
    isGrantingAccess: grantAccessMutation.isPending,
    isRevokingAccess: revokeAccessMutation.isPending,
  };
}

/**
 * Hook for managing share links
 */
export function useWhiteboardSharing(whiteboardId?: string) {
  // Get share links
  const {
    data: shareLinks,
    isLoading: isLoadingLinks,
    error: linksError,
    refetch: refetchLinks,
  } = api.sharing.getShareLinks.useQuery(
    { whiteboardId: whiteboardId! },
    {
      enabled: !!whiteboardId,
      staleTime: 1 * 60 * 1000, // 1 minute
    }
  );

  // Create share link mutation
  const createLinkMutation = api.sharing.createShareLink.useMutation({
    onSuccess: () => {
      toast.success('Share link created successfully');
      refetchLinks();
    },
    onError: (error) => {
      toast.error(`Failed to create share link: ${error.message}`);
    },
  });

  // Deactivate share link mutation
  const deactivateLinkMutation = api.sharing.deactivateShareLink.useMutation({
    onSuccess: () => {
      toast.success('Share link deactivated');
      refetchLinks();
    },
    onError: (error) => {
      toast.error(`Failed to deactivate share link: ${error.message}`);
    },
  });

  const createShareLink = (
    role: WhiteboardRole = WhiteboardRole.VIEWER,
    options?: {
      permissions?: WhiteboardPermission[];
      expiresInDays?: number;
      maxUses?: number;
    }
  ) => {
    if (!whiteboardId) return;
    
    createLinkMutation.mutate({
      whiteboardId,
      role,
      ...options,
    });
  };

  const deactivateShareLink = (linkId: string) => {
    if (!whiteboardId) return;
    
    deactivateLinkMutation.mutate({
      whiteboardId,
      linkId,
    });
  };

  return {
    shareLinks,
    isLoadingLinks,
    linksError,
    refetchLinks,
    createShareLink,
    deactivateShareLink,
    isCreatingLink: createLinkMutation.isPending,
    isDeactivatingLink: deactivateLinkMutation.isPending,
  };
}

/**
 * Hook for managing invitations
 */
export function useWhiteboardInvitations(whiteboardId?: string) {
  // Get invitations
  const {
    data: invitations,
    isLoading: isLoadingInvitations,
    error: invitationsError,
    refetch: refetchInvitations,
  } = api.sharing.getInvitations.useQuery(
    { whiteboardId: whiteboardId! },
    {
      enabled: !!whiteboardId,
      staleTime: 1 * 60 * 1000, // 1 minute
    }
  );

  // Send invitation mutation
  const inviteUserMutation = api.sharing.inviteUser.useMutation({
    onSuccess: () => {
      toast.success('Invitation sent successfully');
      refetchInvitations();
    },
    onError: (error) => {
      toast.error(`Failed to send invitation: ${error.message}`);
    },
  });

  // Accept invitation mutation
  const acceptInvitationMutation = api.sharing.acceptInvitation.useMutation({
    onSuccess: () => {
      toast.success('Invitation accepted');
      refetchInvitations();
    },
    onError: (error) => {
      toast.error(`Failed to accept invitation: ${error.message}`);
    },
  });

  const inviteUser = (
    email: string,
    role: WhiteboardRole = WhiteboardRole.VIEWER,
    options?: {
      permissions?: WhiteboardPermission[];
      message?: string;
      expiresInDays?: number;
    }
  ) => {
    if (!whiteboardId) return;
    
    inviteUserMutation.mutate({
      whiteboardId,
      email,
      role,
      ...options,
    });
  };

  const acceptInvitation = (invitationId: string) => {
    acceptInvitationMutation.mutate({ invitationId });
  };

  return {
    invitations,
    isLoadingInvitations,
    invitationsError,
    refetchInvitations,
    inviteUser,
    acceptInvitation,
    isInvitingUser: inviteUserMutation.isPending,
    isAcceptingInvitation: acceptInvitationMutation.isPending,
  };
}

/**
 * Hook for accessing whiteboard via share link
 */
export function useShareLinkAccess(token?: string) {
  return api.sharing.accessViaShareLink.useQuery(
    { token: token! },
    {
      enabled: !!token,
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: false, // Don't retry on error (invalid tokens)
    }
  );
}

/**
 * Hook for checking user permissions on a whiteboard
 */
export function useWhiteboardPermissions(whiteboardId?: string, userAccess?: any) {
  const hasPermission = (permission: WhiteboardPermission): boolean => {
    if (!userAccess?.permissions) return false;
    return userAccess.permissions.includes(permission);
  };

  const canView = hasPermission(WhiteboardPermission.VIEW);
  const canEdit = hasPermission(WhiteboardPermission.EDIT);
  const canComment = hasPermission(WhiteboardPermission.COMMENT);
  const canManageSettings = hasPermission(WhiteboardPermission.MANAGE_SETTINGS);
  const canManageAccess = hasPermission(WhiteboardPermission.MANAGE_ACCESS);
  const canDelete = hasPermission(WhiteboardPermission.DELETE);
  const canShare = hasPermission(WhiteboardPermission.SHARE);

  const isOwner = userAccess?.isOwner || false;
  const isAuthenticated = userAccess?.isAuthenticated || false;

  return {
    hasPermission,
    canView,
    canEdit,
    canComment,
    canManageSettings,
    canManageAccess,
    canDelete,
    canShare,
    isOwner,
    isAuthenticated,
    role: userAccess?.role,
    permissions: userAccess?.permissions || [],
  };
}
