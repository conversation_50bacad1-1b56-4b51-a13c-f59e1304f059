'use client';

import { useCallback, useState } from 'react';
import { TRPCClientError } from '@trpc/client';
import type { AppRouter } from '@/server/routers/_app';

export interface ErrorState {
  message: string;
  code?: string;
  details?: unknown;
  timestamp: Date;
}

export interface ErrorHandlingOptions {
  showToast?: boolean;
  logError?: boolean;
  fallbackMessage?: string;
}

/**
 * Custom hook for handling tRPC and general errors
 */
export function useErrorHandling() {
  const [errors, setErrors] = useState<Record<string, ErrorState>>({});

  // Clear a specific error
  const clearError = useCallback((key: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[key];
      return newErrors;
    });
  }, []);

  // Clear all errors
  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  // Handle tRPC errors
  const handleTRPCError = useCallback((
    error: TRPCClientError<AppRouter>,
    key: string,
    options: ErrorHandlingOptions = {}
  ) => {
    const {
      showToast = false,
      logError = true,
      fallbackMessage = 'An unexpected error occurred'
    } = options;

    let message = fallbackMessage;
    const code = error.data?.code;
    const details = error.data;

    // Handle different types of tRPC errors
    switch (error.data?.code) {
      case 'BAD_REQUEST':
        message = 'Invalid request. Please check your input and try again.';
        break;
      case 'UNAUTHORIZED':
        message = 'You are not authorized to perform this action.';
        break;
      case 'FORBIDDEN':
        message = 'Access denied. You do not have permission to perform this action.';
        break;
      case 'NOT_FOUND':
        message = 'The requested resource was not found.';
        break;
      case 'CONFLICT':
        message = 'A conflict occurred. The resource may have been modified by another user.';
        break;
      case 'PRECONDITION_FAILED':
        message = 'Precondition failed. Please refresh and try again.';
        break;
      case 'PAYLOAD_TOO_LARGE':
        message = 'The data you are trying to save is too large.';
        break;
      case 'TOO_MANY_REQUESTS':
        message = 'Too many requests. Please wait a moment and try again.';
        break;
      case 'INTERNAL_SERVER_ERROR':
        message = 'An internal server error occurred. Please try again later.';
        break;
      case 'TIMEOUT':
        message = 'The request timed out. Please check your connection and try again.';
        break;
      default:
        // Try to extract a meaningful message from the error
        if (error.message) {
          message = error.message;
        } else if (error.data?.zodError) {
          // Handle Zod validation errors
          const zodError = error.data.zodError;
          if (zodError.fieldErrors) {
            const fieldErrors = Object.entries(zodError.fieldErrors)
              .map(([field, errors]) => `${field}: ${errors?.join(', ')}`)
              .join('; ');
            message = `Validation error: ${fieldErrors}`;
          } else if (zodError.formErrors?.length) {
            message = `Validation error: ${zodError.formErrors.join(', ')}`;
          }
        }
    }

    const errorState: ErrorState = {
      message,
      code,
      details,
      timestamp: new Date(),
    };

    setErrors(prev => ({
      ...prev,
      [key]: errorState,
    }));

    if (logError) {
      console.error(`tRPC Error [${key}]:`, error);
    }

    if (showToast) {
      // You can integrate with a toast library here
      console.warn('Toast notification:', message);
    }

    return errorState;
  }, []);

  // Handle general errors
  const handleError = useCallback((
    error: Error | unknown,
    key: string,
    options: ErrorHandlingOptions = {}
  ) => {
    const {
      showToast = false,
      logError = true,
      fallbackMessage = 'An unexpected error occurred'
    } = options;

    let message = fallbackMessage;
    let code: string | undefined;
    let details: unknown;

    if (error instanceof Error) {
      message = error.message || fallbackMessage;
      code = 'GENERIC_ERROR';
      details = {
        name: error.name,
        stack: error.stack,
      };
    } else {
      details = error;
    }

    const errorState: ErrorState = {
      message,
      code,
      details,
      timestamp: new Date(),
    };

    setErrors(prev => ({
      ...prev,
      [key]: errorState,
    }));

    if (logError) {
      console.error(`Error [${key}]:`, error);
    }

    if (showToast) {
      console.warn('Toast notification:', message);
    }

    return errorState;
  }, []);

  // Get error by key
  const getError = useCallback((key: string) => {
    return errors[key];
  }, [errors]);

  // Check if there are any errors
  const hasErrors = Object.keys(errors).length > 0;

  // Get all errors
  const getAllErrors = useCallback(() => {
    return errors;
  }, [errors]);

  // Retry wrapper for async operations
  const withRetry = useCallback(async <T>(
    operation: () => Promise<T>,
    key: string,
    maxRetries = 3,
    delay = 1000
  ): Promise<T> => {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        clearError(key);
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxRetries) {
          if (error instanceof TRPCClientError) {
            handleTRPCError(error, key, { logError: true });
          } else {
            handleError(error, key, { logError: true });
          }
          throw error;
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }

    throw lastError!;
  }, [clearError, handleTRPCError, handleError]);

  return {
    errors,
    hasErrors,
    getError,
    getAllErrors,
    clearError,
    clearAllErrors,
    handleTRPCError,
    handleError,
    withRetry,
  };
}

// Hook for handling form validation errors
export function useFormErrorHandling() {
  const [fieldErrors, setFieldErrors] = useState<Record<string, string[]>>({});
  const [formError, setFormError] = useState<string | null>(null);

  const setFieldError = useCallback((field: string, errors: string[]) => {
    setFieldErrors(prev => ({
      ...prev,
      [field]: errors,
    }));
  }, []);

  const clearFieldError = useCallback((field: string) => {
    setFieldErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const clearAllFieldErrors = useCallback(() => {
    setFieldErrors({});
  }, []);

  const handleValidationError = useCallback((error: TRPCClientError<AppRouter>) => {
    if (error.data?.zodError) {
      const zodError = error.data.zodError;
      
      if (zodError.fieldErrors) {
        setFieldErrors(zodError.fieldErrors as Record<string, string[]>);
      }
      
      if (zodError.formErrors?.length) {
        setFormError(zodError.formErrors.join(', '));
      }
    }
  }, []);

  const getFieldError = useCallback((field: string) => {
    return fieldErrors[field]?.[0];
  }, [fieldErrors]);

  const hasFieldError = useCallback((field: string) => {
    return Boolean(fieldErrors[field]?.length);
  }, [fieldErrors]);

  const clearAllErrors = useCallback(() => {
    setFieldErrors({});
    setFormError(null);
  }, []);

  return {
    fieldErrors,
    formError,
    setFieldError,
    clearFieldError,
    clearAllFieldErrors,
    setFormError,
    handleValidationError,
    getFieldError,
    hasFieldError,
    clearAllErrors,
  };
}
