'use client';

import { useSession } from "@/lib/auth-client";

export interface User {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
}

export interface Session {
  user: User;
}

export interface UseSessionReturn {
  data: Session | null;
  status: "loading" | "authenticated" | "unauthenticated";
}

export function useAuth(): UseSessionReturn {
  const { data: session, isPending } = useSession();

  if (isPending) {
    return {
      data: null,
      status: "loading",
    };
  }

  if (session?.user) {
    return {
      data: {
        user: {
          id: session.user.id,
          name: session.user.name || null,
          email: session.user.email || null,
          image: session.user.image || null,
        },
      },
      status: "authenticated",
    };
  }

  return {
    data: null,
    status: "unauthenticated",
  };
}
