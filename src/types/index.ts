import type { Node, Edge, Viewport } from 'reactflow';

// RBAC Enums (copied from Prisma schema to avoid client-side imports)
export enum WhiteboardRole {
  OWNER = 'OWNER',
  EDITOR = 'EDITOR',
  VIEWER = 'VIEWER',
  COMMENTER = 'COMMENTER',
}

export enum WhiteboardPermission {
  VIEW = 'VIEW',
  EDIT = 'EDIT',
  COMMENT = 'COMMENT',
  MANAGE_SETTINGS = 'MANAGE_SETTINGS',
  MANAGE_ACCESS = 'MANAGE_ACCESS',
  DELETE = 'DELETE',
  SHARE = 'SHARE',
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface WhiteboardData {
  id: string;
  title: string;
  content?: Record<string, unknown> | null; // React Flow data as JSON object (nodes, edges, viewport)
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  // Settings (now part of the main object)
  isPublic: boolean;
  allowComments: boolean;
  viewMode: string;
  backgroundColor: string;
  gridMode: boolean;
  snapToGrid: boolean;
  theme: string;
  // Viewport
  viewportX: number;
  viewportY: number;
  viewportZoom: number;
}

export interface WhiteboardSettings {
  isPublic: boolean;
  allowComments: boolean;
  viewMode: 'edit' | 'view';
  backgroundColor: string;
  gridMode: boolean;
  snapToGrid: boolean;
  theme: 'light' | 'dark';
}

// React Flow specific types
export interface ReactFlowData {
  nodes: Node[];
  edges: Edge[];
  viewport: Viewport;
}

// Custom node types for our whiteboard
export interface CustomNodeData {
  label: string;
  color?: string;
  backgroundColor?: string;
  fontSize?: number;
  width?: number;
  height?: number;
}

// Countdown Timer Node Data
export interface CountdownTimerNodeData {
  title: string;
  endDate: Date;
  style: {
    color: string;
    fontSize: number;
    backgroundColor?: string;
  };
  isActive: boolean;
}

// Goal Node Data
export interface GoalNodeData {
  title: string;
  description?: string;
  dueDate?: Date;
  priority: 'low' | 'medium' | 'high';
  status: 'not-started' | 'in-progress' | 'completed';
  tags: string[];
}

// Hill Chart Node Data
export interface HillChartNodeData {
  title: string;
  items: HillChartItem[];
  width: number;
  height: number;
}

// URL Node Data
export interface URLNodeData {
  title: string;
  url: string;
  favicon?: string;
  description?: string;
  backgroundColor?: string;
  textColor?: string;
}

// Todo List Node Data
export interface TodoItem {
  id: string;
  text: string;
  completed: boolean;
  createdAt: Date;
}

export interface TodoListNodeData {
  title: string;
  items: TodoItem[];
  backgroundColor?: string;
  textColor?: string;
  maxItems?: number;
}

export type CustomNode = Node<CustomNodeData>;
export type CountdownTimerNode = Node<CountdownTimerNodeData>;
export type GoalNode = Node<GoalNodeData>;
export type HillChartNode = Node<HillChartNodeData>;
export type URLNode = Node<URLNodeData>;
export type TodoListNode = Node<TodoListNodeData>;

// Custom edge types
export interface CustomEdgeData {
  label?: string;
  color?: string;
  strokeWidth?: number;
}

export type CustomEdge = Edge<CustomEdgeData>;

export interface CountdownTimer {
  id: string;
  whiteboardId: string;
  title: string;
  endDate: Date;
  position: { x: number; y: number };
  style: {
    color: string;
    fontSize: number;
    backgroundColor?: string;
  };
  isActive: boolean;
}

export interface Goal {
  id: string;
  whiteboardId: string;
  title: string;
  description?: string;
  dueDate?: Date;
  priority: 'low' | 'medium' | 'high';
  status: 'not-started' | 'in-progress' | 'completed';
  position: { x: number; y: number };
  tags: string[];
}

export interface HillChart {
  id: string;
  whiteboardId: string;
  title: string;
  items: HillChartItem[];
  position: { x: number; y: number };
  width: number;
  height: number;
}

export interface HillChartItem {
  id: string;
  name: string;
  position: number; // 0-100, representing position on the hill
  color: string;
  description?: string;
}

export interface AICommand {
  command: string;
  context?: string;
  elementId?: string;
  position?: { x: number; y: number };
}

export interface CollaborationEvent {
  type: 'user-joined' | 'user-left' | 'whiteboard-updated' | 'cursor-moved';
  userId: string;
  timestamp: Date;
  data?: unknown;
}
