import type { <PERSON>ada<PERSON> } from 'next';
import { Container, Flex, Text, Card } from '@radix-ui/themes';
import { Timer, Target, TrendingUp, Bot, Users, Zap, Shield, Globe } from 'lucide-react';
import Breadcrumbs from '@/components/seo/Breadcrumbs';

export const metadata: Metadata = {
  title: 'Features - AI-Powered Whiteboard Collaboration Tools',
  description: 'Discover powerful features including countdown timers, goal tracking, Shape Up hill charts, AI assistance, and real-time collaboration for teams.',
  keywords: [
    'whiteboard features',
    'countdown timer',
    'goal tracking',
    'hill charts',
    'AI assistant',
    'team collaboration',
    'visual planning',
    'project management'
  ],
  openGraph: {
    title: 'Features - AI-Powered Whiteboard Collaboration Tools',
    description: 'Discover powerful features for team collaboration and visual planning',
    type: 'website',
    images: ['/og-image.svg'],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Features - AI-Powered Whiteboard Collaboration Tools',
    description: 'Discover powerful features for team collaboration and visual planning',
    images: ['/twitter-image.svg'],
  },
  alternates: {
    canonical: '/features',
  },
};

export default function FeaturesPage() {
  const features = [
    {
      icon: Timer,
      title: 'Countdown Timers',
      description: 'Create visual countdown timers for deadlines, sprints, and important events. Set multiple timers and track progress in real-time.',
      benefits: ['Visual deadline tracking', 'Multiple timer support', 'Customizable alerts', 'Team synchronization']
    },
    {
      icon: Target,
      title: 'Goal Setting & Tracking',
      description: 'Set, prioritize, and track goals with advanced tagging and progress monitoring. Perfect for OKRs and personal objectives.',
      benefits: ['Priority levels', 'Progress tracking', 'Tag organization', 'Achievement analytics']
    },
    {
      icon: TrendingUp,
      title: 'Shape Up Hill Charts',
      description: 'Visualize project progress using the Shape Up methodology. Track confidence levels and identify bottlenecks early.',
      benefits: ['Progress visualization', 'Confidence tracking', 'Risk identification', 'Team alignment']
    },
    {
      icon: Bot,
      title: 'AI-Powered Assistant',
      description: 'Use natural language commands to create, edit, and organize content. Let AI help you work faster and more efficiently.',
      benefits: ['Natural language editing', 'Smart suggestions', 'Auto-organization', 'Content generation']
    },
    {
      icon: Users,
      title: 'Real-time Collaboration',
      description: 'Work together seamlessly with live cursors, instant updates, and collaborative editing. Perfect for remote teams.',
      benefits: ['Live collaboration', 'Instant sync', 'User presence', 'Conflict resolution']
    },
    {
      icon: Zap,
      title: 'Lightning Fast Performance',
      description: 'Optimized for speed with lazy loading, efficient rendering, and smart caching. Works smoothly even with large boards.',
      benefits: ['Fast loading', 'Smooth interactions', 'Large board support', 'Offline capabilities']
    },
    {
      icon: Shield,
      title: 'Enterprise Security',
      description: 'Bank-level security with end-to-end encryption, secure authentication, and compliance with industry standards.',
      benefits: ['End-to-end encryption', 'Secure authentication', 'Compliance ready', 'Data privacy']
    },
    {
      icon: Globe,
      title: 'Universal Access',
      description: 'Access from any device, anywhere in the world. No downloads required - works in any modern web browser.',
      benefits: ['Cross-platform', 'No installation', 'Mobile friendly', 'Global accessibility']
    }
  ];

  return (
    <>
      {/* Structured Data for Features */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "Features - AI-Powered Whiteboard Collaboration Tools",
            "description": "Discover powerful features including countdown timers, goal tracking, Shape Up hill charts, AI assistance, and real-time collaboration for teams.",
            "url": `${process.env.NEXT_PUBLIC_BASE_URL || "https://anchorboard.xyz"}/features`,
            "mainEntity": {
              "@type": "SoftwareApplication",
              "name": "Anchorboard App",
              "featureList": [
                "Countdown Timers - Visual deadline tracking with customizable alerts",
                "Goal Setting & Tracking - Priority levels, progress monitoring, and achievement analytics",
                "Shape Up Hill Charts - Progress visualization and confidence tracking",
                "AI-Powered Assistant - Natural language editing and smart suggestions",
                "Real-time Collaboration - Live cursors, instant sync, and user presence",
                "Lightning Fast Performance - Optimized for speed with lazy loading",
                "Enterprise Security - End-to-end encryption and compliance ready",
                "Universal Access - Cross-platform, mobile friendly, no installation required"
              ]
            }
          })
        }}
      />

      <Container size="4" className="min-h-screen py-12">
        <Flex direction="column" gap="8">
          {/* Breadcrumbs */}
          <Breadcrumbs
            items={[{ label: 'Features' }]}
            className="mb-4"
          />

          {/* Header */}
          <header className="text-center space-y-4">
          <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Powerful Features for Modern Teams
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Everything you need to collaborate, plan, and execute projects with your team. 
            From AI-powered assistance to real-time collaboration tools.
          </p>
        </header>

        {/* Features Grid */}
        <section className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="p-8 hover:shadow-lg transition-all duration-300">
              <Flex direction="column" gap="4">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <feature.icon className="w-6 h-6 text-white" />
                  </div>
                  <h2 className="text-2xl font-semibold">{feature.title}</h2>
                </div>
                
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
                
                <div className="space-y-2">
                  <h3 className="font-medium text-gray-800">Key Benefits:</h3>
                  <ul className="grid grid-cols-2 gap-1">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="text-sm text-gray-600 flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </div>
              </Flex>
            </Card>
          ))}
        </section>

        {/* CTA Section */}
        <section className="text-center bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-12">
          <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Join thousands of teams already using our whiteboard for better collaboration and productivity.
          </p>
          <div className="flex justify-center gap-4">
            <a 
              href="/auth/signup" 
              className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Start Free Trial
            </a>
            <a 
              href="/contact" 
              className="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
            >
              Contact Sales
            </a>
          </div>
        </section>
      </Flex>
    </Container>
    </>
  );
}
