import type { Metadata } from 'next';
import { Container, Flex, Text, Card } from '@radix-ui/themes';
import { Home, Search, ArrowLeft, HelpCircle } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: '404 - Page Not Found | Anchorboard App',
  description: 'The page you are looking for could not be found. Return to our AI-powered whiteboard collaboration platform.',
  robots: {
    index: false,
    follow: true,
  },
};

export default function NotFound() {
  const suggestions = [
    {
      icon: Home,
      title: 'Go Home',
      description: 'Return to our main page and start collaborating',
      href: '/',
      color: 'blue'
    },
    {
      icon: Search,
      title: 'Explore Features',
      description: 'Discover our AI-powered whiteboard features',
      href: '/features',
      color: 'green'
    },
    {
      icon: HelpCircle,
      title: 'Get Help',
      description: 'Contact our support team for assistance',
      href: '/contact',
      color: 'purple'
    }
  ];

  return (
    <>
      {/* Structured Data for 404 Page */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "404 - Page Not Found",
            "description": "The requested page could not be found on Anchorboard App",
            "url": `${process.env.NEXT_PUBLIC_BASE_URL || "https://anchorboard.xyz"}/404`,
            "mainEntity": {
              "@type": "Organization",
              "name": "Anchorboard App",
              "url": process.env.NEXT_PUBLIC_BASE_URL || "https://anchorboard.xyz"
            }
          })
        }}
      />

      <Container size="4" className="min-h-screen py-12">
        <Flex direction="column" gap="12" align="center">
          {/* Hero Section */}
          <div className="text-center space-y-6">
            <div className="text-8xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              404
            </div>
            <h1 className="text-4xl font-bold text-gray-800">
              Oops! Page Not Found
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              The page you're looking for seems to have wandered off our whiteboard. 
              Don't worry, we'll help you get back on track!
            </p>
          </div>

          {/* Suggestions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-4xl">
            {suggestions.map((suggestion, index) => (
              <Link key={index} href={suggestion.href}>
                <Card className="p-8 hover:shadow-lg transition-all duration-300 cursor-pointer h-full">
                  <Flex direction="column" gap="4" align="center" className="text-center">
                    <div className={`w-16 h-16 bg-gradient-to-br from-${suggestion.color}-500 to-${suggestion.color}-600 rounded-lg flex items-center justify-center`}>
                      <suggestion.icon className="w-8 h-8 text-white" />
                    </div>
                    <h2 className="text-xl font-semibold">{suggestion.title}</h2>
                    <p className="text-gray-600">{suggestion.description}</p>
                  </Flex>
                </Card>
              </Link>
            ))}
          </div>

          {/* Additional Help */}
          <Card className="p-8 bg-gradient-to-r from-blue-50 to-purple-50 w-full max-w-2xl">
            <div className="text-center space-y-4">
              <h2 className="text-2xl font-bold">Still Need Help?</h2>
              <p className="text-gray-600">
                If you believe this is an error or you were expecting to find something here, 
                please let us know so we can fix it.
              </p>
              <div className="flex justify-center gap-4">
                <Link 
                  href="/contact"
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                >
                  Report Issue
                </Link>
                <Link 
                  href="/"
                  className="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center gap-2"
                >
                  <ArrowLeft size={16} />
                  Back to Home
                </Link>
              </div>
            </div>
          </Card>

          {/* Popular Pages */}
          <div className="w-full max-w-4xl">
            <h2 className="text-2xl font-bold text-center mb-6">Popular Pages</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Link href="/" className="text-center p-4 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-blue-600">Home</div>
                <div className="text-sm text-gray-600">Start collaborating</div>
              </Link>
              <Link href="/features" className="text-center p-4 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-blue-600">Features</div>
                <div className="text-sm text-gray-600">Explore capabilities</div>
              </Link>
              <Link href="/pricing" className="text-center p-4 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-blue-600">Pricing</div>
                <div className="text-sm text-gray-600">View plans</div>
              </Link>
              <Link href="/about" className="text-center p-4 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-blue-600">About</div>
                <div className="text-sm text-gray-600">Our story</div>
              </Link>
            </div>
          </div>
        </Flex>
      </Container>
    </>
  );
}
