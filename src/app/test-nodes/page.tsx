'use client';

import { useState } from 'react';
import { ReactFlowProvider } from 'reactflow';
import { Button, Flex, Text } from '@radix-ui/themes';
import { Timer, Target, TrendingUp } from 'lucide-react';
import ReactFlowWrapper from '@/components/whiteboard/ReactFlowWrapper';
import { createCountdownTimerNode, createGoalNode, createHillChartNode } from '@/lib/whiteboard-utils';
import type { Node, Edge, Viewport } from 'reactflow';
import type { CountdownTimer, Goal, HillChart } from '@/types';

export default function TestNodesPage() {
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const [viewport, setViewport] = useState<Viewport>({ x: 0, y: 0, zoom: 1 });

  const handleAddCountdownTimer = () => {
    const defaultTimer: Omit<CountdownTimer, 'id' | 'whiteboardId'> = {
      title: 'Test Timer',
      endDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      position: { x: 0, y: 0 },
      style: {
        color: '#000000',
        fontSize: 16,
        backgroundColor: '#ffffff'
      },
      isActive: false
    };

    const position = {
      x: 100 + Math.random() * 200,
      y: 100 + Math.random() * 200
    };

    const newNode = createCountdownTimerNode(defaultTimer, position);
    setNodes(prev => [...prev, newNode]);
  };

  const handleAddGoal = () => {
    const defaultGoal: Omit<Goal, 'id' | 'whiteboardId'> = {
      title: 'Test Goal',
      description: 'This is a test goal',
      priority: 'medium',
      status: 'not-started',
      position: { x: 0, y: 0 },
      tags: ['test', 'demo']
    };

    const position = {
      x: 100 + Math.random() * 200,
      y: 100 + Math.random() * 200
    };

    const newNode = createGoalNode(defaultGoal, position);
    setNodes(prev => [...prev, newNode]);
  };

  const handleAddHillChart = () => {
    const defaultHillChart: Omit<HillChart, 'id' | 'whiteboardId'> = {
      title: 'Test Hill Chart',
      items: [
        {
          id: `item-${Date.now()}-1`,
          name: 'Sample Item',
          position: 25,
          color: '#3b82f6',
          description: 'Test item'
        }
      ],
      position: { x: 0, y: 0 },
      width: 400,
      height: 200
    };

    const position = {
      x: 100 + Math.random() * 200,
      y: 100 + Math.random() * 200
    };

    const newNode = createHillChartNode(defaultHillChart, position);
    setNodes(prev => [...prev, newNode]);
  };

  const handleReactFlowChange = (
    newNodes: Node[],
    newEdges: Edge[],
    newViewport: Viewport
  ) => {
    setNodes(newNodes);
    setEdges(newEdges);
    setViewport(newViewport);
  };

  return (
    <div className="h-screen w-full flex flex-col">
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <Text size="4" weight="bold">Test Custom Nodes</Text>
        <Flex gap="2">
          <Button size="2" variant="soft" onClick={handleAddCountdownTimer}>
            <Timer size={16} />
            Add Timer
          </Button>
          <Button size="2" variant="soft" onClick={handleAddGoal}>
            <Target size={16} />
            Add Goal
          </Button>
          <Button size="2" variant="soft" onClick={handleAddHillChart}>
            <TrendingUp size={16} />
            Add Hill Chart
          </Button>
        </Flex>
      </div>

      <div className="flex-1">
        <ReactFlowProvider>
          <ReactFlowWrapper
            whiteboardData={{
              id: 'test',
              title: 'Test',
              userId: 'test',
              content: { nodes, edges, viewport },
              isPublic: false,
              allowComments: true,
              viewMode: 'edit',
              backgroundColor: '#ffffff',
              gridMode: false,
              snapToGrid: false,
              theme: 'light',
              viewportX: viewport.x,
              viewportY: viewport.y,
              viewportZoom: viewport.zoom,
              createdAt: new Date(),
              updatedAt: new Date()
            }}
            onSave={handleReactFlowChange}
            settings={{
              isPublic: false,
              allowComments: true,
              viewMode: 'edit',
              backgroundColor: '#ffffff',
              gridMode: false,
              snapToGrid: false,
              theme: 'light'
            }}
          />
        </ReactFlowProvider>
      </div>
    </div>
  );
}
