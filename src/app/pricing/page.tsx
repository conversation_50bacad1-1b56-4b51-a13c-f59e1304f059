import type { <PERSON>ada<PERSON> } from 'next';
import { Container, Flex, Text, Card, Button } from '@radix-ui/themes';
import { Check, Star, Zap, Users, Crown, Shield } from 'lucide-react';
import Breadcrumbs from '@/components/seo/Breadcrumbs';
import { FAQSection } from '@/components/seo/FAQSchema';

export const metadata: Metadata = {
  title: 'Pricing Plans - Affordable AI-Powered Whiteboard Solutions',
  description: 'Choose the perfect plan for your team. Free forever plan available. Affordable pricing for AI-powered whiteboard collaboration, countdown timers, and goal tracking features.',
  keywords: [
    'whiteboard pricing',
    'collaboration software pricing',
    'AI whiteboard cost',
    'team collaboration plans',
    'whiteboard subscription',
    'free Anchorboard App',
    'affordable collaboration tools'
  ],
  openGraph: {
    title: 'Pricing Plans - Affordable AI-Powered Whiteboard Solutions',
    description: 'Choose the perfect plan for your team. Free forever plan available with premium features.',
    type: 'website',
    images: ['/og-image.svg'],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Pricing Plans - Affordable AI-Powered Whiteboard Solutions',
    description: 'Choose the perfect plan for your team. Free forever plan available with premium features.',
    images: ['/twitter-image.svg'],
  },
  alternates: {
    canonical: '/pricing',
  },
};

export default function PricingPage() {
  const plans = [
    {
      name: 'Free',
      price: '$0',
      period: 'forever',
      description: 'Perfect for individuals and small teams getting started',
      icon: Star,
      popular: false,
      features: [
        'Up to 3 whiteboards',
        'Basic countdown timers',
        'Goal setting (up to 10 goals)',
        'Real-time collaboration (up to 3 users)',
        'Export to PNG/PDF',
        'Community support',
        'Basic templates'
      ],
      cta: 'Get Started Free',
      ctaLink: '/auth/signup'
    },
    {
      name: 'Pro',
      price: '$12',
      period: 'per user/month',
      description: 'Ideal for growing teams and professional projects',
      icon: Zap,
      popular: true,
      features: [
        'Unlimited whiteboards',
        'Advanced countdown timers with alerts',
        'Unlimited goals with priority levels',
        'Real-time collaboration (up to 25 users)',
        'AI-powered editing assistance',
        'Shape Up hill charts',
        'Advanced export options',
        'Priority support',
        'Custom templates',
        'Version history',
        'Advanced analytics'
      ],
      cta: 'Start Free Trial',
      ctaLink: '/auth/signup?plan=pro'
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      period: 'contact sales',
      description: 'For large organizations with advanced security needs',
      icon: Crown,
      popular: false,
      features: [
        'Everything in Pro',
        'Unlimited users',
        'SSO integration',
        'Advanced security controls',
        'Custom integrations',
        'Dedicated account manager',
        'SLA guarantee',
        'On-premise deployment option',
        'Custom branding',
        'Advanced admin controls',
        'Compliance certifications'
      ],
      cta: 'Contact Sales',
      ctaLink: '/contact'
    }
  ];

  const faqs = [
    {
      question: 'Can I change plans anytime?',
      answer: 'Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, and we\'ll prorate any billing adjustments.'
    },
    {
      question: 'Is there a free trial for paid plans?',
      answer: 'Yes! All paid plans come with a 14-day free trial. No credit card required to start your trial.'
    },
    {
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit cards, PayPal, and bank transfers for Enterprise customers.'
    },
    {
      question: 'Can I cancel anytime?',
      answer: 'Absolutely. You can cancel your subscription at any time. Your account will remain active until the end of your billing period.'
    }
  ];

  return (
    <>
      {/* Structured Data for Pricing */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Product",
            "name": "Anchorboard App",
            "description": "AI-powered collaborative whiteboard platform",
            "offers": [
              {
                "@type": "Offer",
                "name": "Free Plan",
                "price": "0",
                "priceCurrency": "USD",
                "description": "Free forever plan for individuals and small teams"
              },
              {
                "@type": "Offer",
                "name": "Pro Plan",
                "price": "12",
                "priceCurrency": "USD",
                "billingIncrement": "Month",
                "description": "Professional plan for growing teams"
              }
            ]
          })
        }}
      />

      <Container size="4" className="min-h-screen py-12">
        <Flex direction="column" gap="12">
          {/* Breadcrumbs */}
          <Breadcrumbs
            items={[{ label: 'Pricing' }]}
            className="mb-4"
          />

          {/* Hero Section */}
          <header className="text-center space-y-6">
            <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Simple, Transparent Pricing
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Choose the perfect plan for your team. Start free and scale as you grow. 
              All plans include our core collaboration features with no hidden fees.
            </p>
          </header>

          {/* Pricing Cards */}
          <section className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {plans.map((plan, index) => (
              <Card 
                key={index} 
                className={`p-8 relative ${plan.popular ? 'ring-2 ring-blue-500 shadow-xl' : 'hover:shadow-lg'} transition-all`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <Flex direction="column" gap="6">
                  <div className="text-center space-y-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto">
                      <plan.icon className="w-6 h-6 text-white" />
                    </div>
                    <h2 className="text-2xl font-bold">{plan.name}</h2>
                    <div className="space-y-1">
                      <div className="text-4xl font-bold">{plan.price}</div>
                      <div className="text-gray-600">{plan.period}</div>
                    </div>
                    <p className="text-gray-600">{plan.description}</p>
                  </div>

                  <div className="space-y-3">
                    {plan.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center gap-3">
                        <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <a 
                    href={plan.ctaLink}
                    className={`w-full py-3 px-6 rounded-lg font-medium text-center transition-colors ${
                      plan.popular 
                        ? 'bg-blue-600 text-white hover:bg-blue-700' 
                        : 'border border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {plan.cta}
                  </a>
                </Flex>
              </Card>
            ))}
          </section>

          {/* Features Comparison */}
          <section className="space-y-8">
            <h2 className="text-3xl font-bold text-center">Why Choose Anchorboard App?</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <Card className="p-6 text-center">
                <Shield className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">Enterprise Security</h3>
                <p className="text-gray-600">Bank-level encryption and security controls to keep your data safe.</p>
              </Card>
              <Card className="p-6 text-center">
                <Users className="w-12 h-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">Real-time Collaboration</h3>
                <p className="text-gray-600">Work together seamlessly with live cursors and instant updates.</p>
              </Card>
              <Card className="p-6 text-center">
                <Zap className="w-12 h-12 text-purple-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">AI-Powered</h3>
                <p className="text-gray-600">Intelligent assistance to help you work faster and more efficiently.</p>
              </Card>
            </div>
          </section>

          {/* FAQ Section with Schema */}
          <FAQSection faqs={faqs} />

          {/* CTA Section */}
          <section className="text-center bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-12">
            <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
              Join thousands of teams already using Anchorboard App. Start with our free plan and upgrade when you're ready.
            </p>
            <div className="flex justify-center gap-4">
              <a 
                href="/auth/signup" 
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Start Free Today
              </a>
              <a 
                href="/contact" 
                className="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
              >
                Contact Sales
              </a>
            </div>
          </section>
        </Flex>
      </Container>
    </>
  );
}
