import { Con<PERSON>er, <PERSON>lex, <PERSON>, <PERSON><PERSON>, Card } from '@radix-ui/themes';
import { Timer, Target, TrendingUp, Bot, ArrowRight, Zap, Shield, Globe } from 'lucide-react';
import { TestimonialsSection } from '@/components/seo/ReviewSchema';
import Footer from '@/components/layout/Footer';
import Link from 'next/link';
import type { Metadata } from 'next';
import RecentPublicWhiteboards from '@/components/home/<USER>';
import AuthenticatedSection from '@/components/home/<USER>';

export const metadata: Metadata = {
  title: 'AI-Powered Collaborative Whiteboard | Anchorboard App',
  description: 'Transform your ideas into reality with our intelligent whiteboard. Create countdown timers, set and track goals, visualize progress with hill charts, and collaborate in real-time with AI-powered editing capabilities.',
  keywords: [
    'collaborative whiteboard',
    'AI-powered whiteboard',
    'team collaboration',
    'visual planning',
    'countdown timers',
    'goal tracking',
    'hill charts',
    'real-time collaboration',
    'brainstorming tool',
    'project management'
  ],
  openGraph: {
    title: 'AI-Powered Collaborative Whiteboard | Anchorboard App',
    description: 'Transform your ideas into reality with our intelligent whiteboard platform.',
    type: 'website',
    url: '/',
    images: ['/og-image.svg'],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AI-Powered Collaborative Whiteboard | Anchorboard App',
    description: 'Transform your ideas into reality with our intelligent whiteboard platform.',
    images: ['/twitter-image.svg'],
  },
  alternates: {
    canonical: '/',
  },
};

// Static testimonials data
const testimonials = [
  {
    author: "Sarah Chen",
    rating: 5,
    reviewBody: "Anchorboard App has transformed how our remote team collaborates. The AI features are incredibly helpful for brainstorming sessions.",
    datePublished: "2024-12-01",
    company: "TechStart Inc",
    position: "Product Manager"
  },
  {
    author: "Michael Rodriguez",
    rating: 5,
    reviewBody: "The countdown timers and goal tracking features keep our sprints on track. Best whiteboard tool we've used!",
    datePublished: "2024-11-28",
    company: "DevCorp",
    position: "Scrum Master"
  },
  {
    author: "Emily Johnson",
    rating: 4,
    reviewBody: "Love the Shape Up hill charts for visualizing project progress. Makes it easy to communicate status to stakeholders.",
    datePublished: "2024-11-25",
    company: "Design Studio",
    position: "Creative Director"
  }
];

export default function Home() {

  return (
    <>
      {/* Preload critical resources */}
      <link rel="preload" href="/whiteboard" as="document" />
      <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

      {/* SEO Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "SoftwareApplication",
            "name": "AI-Powered Anchorboard App",
            "description": "Create, collaborate, and visualize ideas with countdown timers, goal tracking, Shape Up hill charts, and AI-powered editing capabilities",
            "applicationCategory": "ProductivityApplication",
            "operatingSystem": "Web Browser",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            },
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.8",
              "ratingCount": "150"
            },
            "featureList": [
              "AI-powered whiteboard editing",
              "Countdown timers for deadlines",
              "Goal setting and tracking",
              "Shape Up hill charts",
              "Real-time collaboration",
              "Visual brainstorming tools",
              "Export to multiple formats"
            ]
          })
        }}
      />

      <Container size="4" className="min-h-screen py-8">
        <Flex direction="column" gap="8" align="center">
          {/* Hero Section */}
          <header className="text-center space-y-6">
            <h1 className="text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              AI-Powered Whiteboard
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Transform your ideas into reality with our intelligent whiteboard. Create countdown timers,
              set and track goals, visualize progress with hill charts, and collaborate in real-time
              with AI-powered editing capabilities.
            </p>
            <div className="flex flex-wrap justify-center gap-2 mt-6">
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">Collaboration</span>
              <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">AI-Powered</span>
              <span className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">Goal Tracking</span>
              <span className="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm font-medium">Visual Planning</span>
            </div>
          </header>

          {/* Authenticated User Section */}
          <AuthenticatedSection />

          {/* CTA Section */}
          <Card className="p-8 w-full max-w-md">
            <Flex direction="column" gap="6" align="center">
              <div className="text-center">
                <h2 className="text-2xl font-semibold mb-2">Get Started Today</h2>
                <p className="text-gray-600">
                  Join thousands of teams already using our whiteboard for better collaboration
                </p>
              </div>
              <Flex direction="row" gap="3" className="w-full">
                <Link href="/draw" className="flex-1">
                  <Button size="3" className="w-full">
                    <ArrowRight size={18} />
                    Start Creating
                  </Button>
                </Link>
                <Link href="/auth/signin" className="flex-1">
                  <Button size="3" variant="outline" className="w-full">
                    Sign In
                  </Button>
                </Link>
              </Flex>
              <p className="text-sm text-gray-500 text-center">
                Free forever • No credit card required • Start in 30 seconds
              </p>
            </Flex>
          </Card>

          {/* Features Grid */}
          <section className="w-full max-w-6xl">
            <h2 className="text-3xl font-bold text-center mb-8">Powerful Features for Modern Teams</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="p-6 hover:shadow-lg transition-shadow">
                <div className="text-center space-y-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto">
                    <Timer className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold">Countdown Timers</h3>
                  <p className="text-gray-600 text-sm">
                    Create visual countdown timers for deadlines, sprints, and important events.
                    Never miss a deadline again.
                  </p>
                </div>
              </Card>

              <Card className="p-6 hover:shadow-lg transition-shadow">
                <div className="text-center space-y-4">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto">
                    <Target className="w-6 h-6 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold">Goal Setting</h3>
                  <p className="text-gray-600 text-sm">
                    Set, track, and manage goals with priority levels, tags, and progress tracking.
                    Achieve more with structured planning.
                  </p>
                </div>
              </Card>

              <Card className="p-6 hover:shadow-lg transition-shadow">
                <div className="text-center space-y-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto">
                    <TrendingUp className="w-6 h-6 text-purple-600" />
                  </div>
                  <h3 className="text-lg font-semibold">Hill Charts</h3>
                  <p className="text-gray-600 text-sm">
                    Visualize project progress using Shape Up methodology.
                    Track confidence and progress in real-time.
                  </p>
                </div>
              </Card>

              <Card className="p-6 hover:shadow-lg transition-shadow">
                <div className="text-center space-y-4">
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto">
                    <Bot className="w-6 h-6 text-orange-600" />
                  </div>
                  <h3 className="text-lg font-semibold">AI Assistant</h3>
                  <p className="text-gray-600 text-sm">
                    Use natural language commands for editing and creating content.
                    Let AI help you work faster and smarter.
                  </p>
                </div>
              </Card>
            </div>
          </section>

          {/* Benefits Section */}
          <section className="w-full max-w-4xl text-center">
            <h2 className="text-3xl font-bold mb-6">Why Choose Our Whiteboard?</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="space-y-3">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                  <Zap className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold">Fast & Responsive</h3>
                <p className="text-gray-600">
                  Lightning-fast performance with optimized loading and smooth interactions
                </p>
              </div>
              <div className="space-y-3">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <Shield className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold">Secure & Private</h3>
                <p className="text-gray-600">
                  Your data is encrypted and secure. We never share your information
                </p>
              </div>
              <div className="space-y-3">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                  <Globe className="w-8 h-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold">Works Everywhere</h3>
                <p className="text-gray-600">
                  Access from any device, anywhere. No downloads required
                </p>
              </div>
            </div>
          </section>

          {/* Testimonials Section */}
          <TestimonialsSection
            reviews={testimonials}
            aggregateRating={{ ratingValue: 4.8, ratingCount: 150 }}
            className="w-full max-w-6xl"
          />

          {/* Recently Updated Public Whiteboards */}
          <RecentPublicWhiteboards />
        </Flex>
      </Container>

      {/* Footer */}
      <Footer />
    </>
  );
}
