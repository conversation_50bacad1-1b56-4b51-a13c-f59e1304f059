import type { Metadata } from 'next';
import { Container, Flex, Text, Card } from '@radix-ui/themes';
import { Shield, Eye, Lock, Users, Database, Globe } from 'lucide-react';
import Breadcrumbs from '@/components/seo/Breadcrumbs';

export const metadata: Metadata = {
  title: 'Privacy Policy | Anchorboard App - Data Protection & Privacy',
  description: 'Learn how Anchorboard App protects your privacy and handles your data. Comprehensive privacy policy covering data collection, usage, and your rights.',
  keywords: [
    'privacy policy',
    'data protection',
    'GDPR compliance',
    'user privacy',
    'data security',
    'whiteboard privacy',
    'collaboration privacy'
  ],
  openGraph: {
    title: 'Privacy Policy - Anchorboard App Data Protection',
    description: 'Comprehensive privacy policy explaining how we protect your data and respect your privacy.',
    type: 'website',
    url: '/privacy',
    images: ['/og-image.svg'],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Privacy Policy - Anchorboard App Data Protection',
    description: 'Learn how we protect your privacy and handle your data responsibly.',
    images: ['/twitter-image.svg'],
  },
  alternates: {
    canonical: '/privacy',
  },
};

export default function PrivacyPage() {
  const sections = [
    {
      icon: Database,
      title: 'Information We Collect',
      content: [
        'Account information (name, email address)',
        'Whiteboard content and collaboration data',
        'Usage analytics and performance metrics',
        'Device and browser information',
        'IP address and location data (anonymized)'
      ]
    },
    {
      icon: Eye,
      title: 'How We Use Your Information',
      content: [
        'Provide and improve our whiteboard services',
        'Enable real-time collaboration features',
        'Send important service updates and notifications',
        'Analyze usage patterns to enhance user experience',
        'Ensure security and prevent fraud'
      ]
    },
    {
      icon: Users,
      title: 'Information Sharing',
      content: [
        'We never sell your personal information',
        'Data is only shared with your explicit consent',
        'Service providers bound by strict confidentiality',
        'Legal compliance when required by law',
        'Anonymous, aggregated data for research purposes'
      ]
    },
    {
      icon: Lock,
      title: 'Data Security',
      content: [
        'End-to-end encryption for sensitive data',
        'Regular security audits and penetration testing',
        'SOC 2 Type II compliance',
        'Secure data centers with 24/7 monitoring',
        'Employee access controls and training'
      ]
    },
    {
      icon: Shield,
      title: 'Your Rights',
      content: [
        'Access and download your data anytime',
        'Correct or update your information',
        'Delete your account and associated data',
        'Opt-out of non-essential communications',
        'Data portability to other services'
      ]
    },
    {
      icon: Globe,
      title: 'International Transfers',
      content: [
        'Data processed in secure facilities worldwide',
        'GDPR-compliant data transfer mechanisms',
        'Adequate protection for EU residents',
        'Standard contractual clauses with processors',
        'Regular compliance monitoring and audits'
      ]
    }
  ];

  return (
    <>
      {/* Structured Data for Privacy Policy */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "Privacy Policy",
            "description": "Anchorboard App privacy policy and data protection information",
            "url": `${process.env.NEXT_PUBLIC_BASE_URL || "https://anchorboard.xyz"}/privacy`,
            "mainEntity": {
              "@type": "PrivacyPolicy",
              "name": "Anchorboard App Privacy Policy",
              "dateModified": "2025-07-07",
              "publisher": {
                "@type": "Organization",
                "name": "Anchorboard App"
              }
            }
          })
        }}
      />

      <Container size="4" className="py-8">
        <Flex direction="column" gap="8">
          {/* Breadcrumbs */}
          <Breadcrumbs 
            items={[{ label: 'Privacy Policy' }]}
            className="mb-4"
          />

          {/* Header */}
          <header className="text-center space-y-4">
            <h1 className="text-4xl font-bold">Privacy Policy</h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Your privacy is important to us. This policy explains how we collect, use, and protect 
              your information when you use Anchorboard App.
            </p>
            <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
              <Shield size={16} />
              <span>Last updated: July 7, 2025</span>
            </div>
          </header>

          {/* Quick Summary */}
          <Card className="p-6 bg-blue-50 border-blue-200">
            <h2 className="text-xl font-semibold mb-4 text-blue-900">Privacy at a Glance</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <Lock className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                <div className="font-medium">Secure by Design</div>
                <div className="text-gray-600">End-to-end encryption</div>
              </div>
              <div className="text-center">
                <Shield className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                <div className="font-medium">No Data Selling</div>
                <div className="text-gray-600">We never sell your data</div>
              </div>
              <div className="text-center">
                <Users className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                <div className="font-medium">You're in Control</div>
                <div className="text-gray-600">Full data ownership</div>
              </div>
            </div>
          </Card>

          {/* Privacy Sections */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {sections.map((section, index) => (
              <Card key={index} className="p-6 hover:shadow-lg transition-shadow">
                <Flex direction="column" gap="4">
                  <Flex align="center" gap="3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <section.icon className="w-5 h-5 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-semibold">{section.title}</h3>
                  </Flex>
                  <ul className="space-y-2">
                    {section.content.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start gap-2 text-sm text-gray-600">
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </Flex>
              </Card>
            ))}
          </div>

          {/* Contact Section */}
          <Card className="p-8 text-center bg-gray-50">
            <h2 className="text-2xl font-semibold mb-4">Questions About Privacy?</h2>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              We're committed to transparency. If you have any questions about this privacy policy 
              or how we handle your data, please don't hesitate to contact us.
            </p>
            <div className="flex justify-center gap-4">
              <a 
                href="/contact" 
                className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Contact Us
              </a>
              <a 
                href="mailto:<EMAIL>" 
                className="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
              >
                Email Privacy Team
              </a>
            </div>
          </Card>

          {/* Compliance Information */}
          <div className="text-center text-sm text-gray-500 space-y-2">
            <p>This privacy policy complies with GDPR, CCPA, and other applicable privacy laws.</p>
            <p>For EU residents: You have additional rights under GDPR. Contact us for more information.</p>
          </div>
        </Flex>
      </Container>
    </>
  );
}
