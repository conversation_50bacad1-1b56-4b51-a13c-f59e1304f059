import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import ClientBody from "./ClientBody";
import <PERSON>ript from "next/script";
import { Theme } from "@radix-ui/themes";
import "@radix-ui/themes/styles.css";
import "reactflow/dist/style.css";
import AuthSessionProvider from "@/components/providers/SessionProvider";
import { TRPCProvider } from "@/components/providers/TRPCProvider";
import ClientOnlyPerformanceMonitor from "@/components/performance/ClientOnlyPerformanceMonitor";
import SEOAnalytics from "@/components/analytics/SEOAnalytics";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  preload: true,
  display: 'swap',
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  preload: true,
  display: 'swap',
});

export const metadata: <PERSON><PERSON><PERSON> = {
  title: {
    default: "Anchorboard App - AI-Powered Collaborative Workspace",
    template: "%s | Anchorboard App"
  },
  description: "Create, collaborate, and visualize ideas with our AI-powered whiteboard. Features countdown timers, goal tracking, Shape Up hill charts, and real-time collaboration tools.",
  keywords: [
    "whiteboard",
    "collaboration",
    "AI-powered",
    "countdown timer",
    "goal setting",
    "hill charts",
    "Shape Up",
    "visual collaboration",
    "online whiteboard",
    "team collaboration",
    "project management",
    "brainstorming tool"
  ],
  authors: [{ name: "Anchorboard App Team" }],
  creator: "Anchorboard App",
  publisher: "Anchorboard App",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || 'https://anchorboard.xyz'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'Anchorboard App - AI-Powered Collaborative Workspace',
    description: 'Create, collaborate, and visualize ideas with our AI-powered whiteboard. Features countdown timers, goal tracking, and real-time collaboration.',
    siteName: 'Anchorboard App',
    images: [
      {
        url: '/og-image.svg',
        width: 1200,
        height: 630,
        alt: 'Anchorboard App - AI-Powered Collaborative Workspace',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Anchorboard App - AI-Powered Collaborative Workspace',
    description: 'Create, collaborate, and visualize ideas with our AI-powered whiteboard.',
    images: ['/twitter-image.svg'],
    creator: '@whiteboardapp',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${geistSans.variable} ${geistMono.variable}`}>
      <head>
        {/* Favicon and App Icons */}
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="icon" href="/favicon-32x32.svg" sizes="32x32" type="image/svg+xml" />
        <link rel="icon" href="/favicon-16x16.svg" sizes="16x16" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.svg" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#3B82F6" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Anchorboard App" />

        {/* Google Analytics */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-5CE5MRRH0Q"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-5CE5MRRH0Q');
          `}
        </Script>

        <Script
          crossOrigin="anonymous"
          src="//unpkg.com/same-runtime/dist/index.global.js"
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebApplication",
              "name": "Anchorboard App",
              "description": "AI-powered collaborative whiteboard with countdown timers, goal tracking, and Shape Up hill charts",
              "url": process.env.NEXT_PUBLIC_BASE_URL || "https://anchorboard.xyz",
              "applicationCategory": "ProductivityApplication",
              "operatingSystem": "Web Browser",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD"
              },
              "featureList": [
                "AI-powered whiteboard",
                "Countdown timers",
                "Goal setting and tracking",
                "Shape Up hill charts",
                "Real-time collaboration",
                "Visual brainstorming tools"
              ],
              "screenshot": `${process.env.NEXT_PUBLIC_BASE_URL || "https://anchorboard.xyz"}/screenshot.svg`,
              "author": {
                "@type": "Organization",
                "name": "Anchorboard App Team"
              }
            })
          }}
        />
      </head>
      <body suppressHydrationWarning className="antialiased">
        <TRPCProvider>
          <AuthSessionProvider>
            <Theme
              accentColor="gray"
              grayColor="gray"
              radius="medium"
              scaling="100%"
            >
              <ClientBody>{children}</ClientBody>
            </Theme>
          </AuthSessionProvider>
        </TRPCProvider>

        {/* 性能监控 - 仅在客户端渲染 */}
        <ClientOnlyPerformanceMonitor
          enableConsoleLog={process.env.NODE_ENV === 'development'}
        />

        {/* SEO Analytics */}
        <SEOAnalytics
          enableDebug={process.env.NODE_ENV === 'development'}
        />
      </body>
    </html>
  );
}
