
import type { MetadataRoute } from 'next'

// Static route configuration for better maintainability
const STATIC_ROUTES = [
  {
    path: '',
    changeFrequency: 'daily' as const,
    priority: 1.0,
    lastModified: '2025-07-07', // Update when homepage content changes
  },
  {
    path: '/features',
    changeFrequency: 'weekly' as const,
    priority: 0.9,
    lastModified: '2025-07-07', // Update when features change
  },
  {
    path: '/pricing',
    changeFrequency: 'weekly' as const,
    priority: 0.9,
    lastModified: '2025-07-07', // Update when pricing changes
  },
  {
    path: '/about',
    changeFrequency: 'monthly' as const,
    priority: 0.8,
    lastModified: '2025-07-07', // Update when about page changes
  },
  {
    path: '/contact',
    changeFrequency: 'monthly' as const,
    priority: 0.7,
    lastModified: '2025-07-07', // Update when contact info changes
  },
  {
    path: '/shangche',
    changeFrequency: 'weekly' as const,
    priority: 0.8,
    lastModified: '2025-07-07', // Update when shangche content changes
  },
] as const

export default function sitemap(): MetadataRoute.Sitemap {

  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://anchorboard.xyz'

  return STATIC_ROUTES.map(route => ({
    url: `${baseUrl}${route.path}`,
    lastModified: new Date(route.lastModified),
    changeFrequency: route.changeFrequency,
    priority: route.priority,
  }))
}

// Enable static generation for better performance
export const dynamic = 'force-static'
export const revalidate = 86400 // Revalidate once per day (24 hours)
