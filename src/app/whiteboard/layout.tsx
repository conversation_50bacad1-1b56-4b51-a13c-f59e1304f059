import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Whiteboard | Anchorboard App - AI-Powered Collaborative Drawing',
  description: 'Create and collaborate on whiteboards with AI-powered features, countdown timers, goal tracking, and real-time collaboration.',
  keywords: [
    'whiteboard app',
    'collaborative drawing',
    'AI whiteboard',
    'real-time collaboration',
    'digital whiteboard',
    'team collaboration',
    'visual brainstorming'
  ],
  openGraph: {
    title: 'Whiteboard - AI-Powered Collaborative Drawing',
    description: 'Create and collaborate on whiteboards with AI-powered features.',
    type: 'website',
    url: '/whiteboard',
    images: ['/og-image.svg'],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Whiteboard - AI-Powered Collaborative Drawing',
    description: 'Create and collaborate on whiteboards with AI-powered features.',
    images: ['/twitter-image.svg'],
  },
  robots: {
    index: false, // Don't index user whiteboard pages
    follow: true,
  },
};

export default function WhiteboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
