import type { Metadata } from 'next';

interface WhiteboardLayoutProps {
  children: React.ReactNode;
  params: Promise<{
    id: string;
  }>;
}

export async function generateMetadata({ params }: { params: Promise<{ id: string }> }): Promise<Metadata> {
  // You could fetch whiteboard data here to get the actual title
  // For now, we'll use a generic title with the ID
  const { id: whiteboardId } = await params;
  
  return {
    title: `Whiteboard ${whiteboardId} | Anchorboard App - AI-Powered Collaborative Drawing`,
    description: 'View and collaborate on this whiteboard with AI-powered features, countdown timers, goal tracking, and real-time collaboration.',
    keywords: [
      'whiteboard app',
      'collaborative drawing',
      'AI whiteboard',
      'real-time collaboration',
      'digital whiteboard',
      'team collaboration',
      'visual brainstorming'
    ],
    openGraph: {
      title: `Whiteboard ${whiteboardId} - AI-Powered Collaborative Drawing`,
      description: 'View and collaborate on this whiteboard with AI-powered features.',
      type: 'website',
      url: `/whiteboard/${whiteboardId}`,
      images: ['/og-image.svg'],
    },
    twitter: {
      card: 'summary_large_image',
      title: `Whiteboard ${whiteboardId} - AI-Powered Collaborative Drawing`,
      description: 'View and collaborate on this whiteboard with AI-powered features.',
      images: ['/twitter-image.svg'],
    },
    robots: {
      index: false, // Don't index individual whiteboard pages for privacy
      follow: true,
    },
  };
}

export default function WhiteboardLayout({ children }: WhiteboardLayoutProps) {
  return children;
}
