'use client';

import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import WhiteboardApp from '@/components/whiteboard/WhiteboardApp';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface WhiteboardPageProps {
  params: Promise<{
    id: string;
  }>;
}

function WhiteboardPageContent({ whiteboardId }: { whiteboardId: string }) {
  // Validate whiteboard ID format (basic validation)
  if (!whiteboardId || whiteboardId.length < 10) {
    notFound();
  }

  return (
    <WhiteboardApp
      whiteboardId={whiteboardId}
      isNewWhiteboard={false}
    />
  );
}

export default async function WhiteboardPage({ params }: WhiteboardPageProps) {
  const { id } = await params;

  return (
    <Suspense fallback={
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading whiteboard..." />
      </div>
    }>
      <WhiteboardPageContent whiteboardId={id} />
    </Suspense>
  );
}
