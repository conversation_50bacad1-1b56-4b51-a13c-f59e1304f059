'use client';

import { Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import WhiteboardApp from '@/components/whiteboard/WhiteboardApp';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

function WhiteboardPageContent() {
  const searchParams = useSearchParams();

  // Check if this is a request to create a new whiteboard
  const isNewWhiteboard = searchParams.get('new') === 'true';

  return (
    <WhiteboardApp
      whiteboardId={null} // No specific whiteboard ID - show dashboard
      isNewWhiteboard={isNewWhiteboard}
    />
  );
}

export default function WhiteboardPage() {
  return (
    <Suspense fallback={
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading whiteboard dashboard..." />
      </div>
    }>
      <WhiteboardPageContent />
    </Suspense>
  );
}
