'use client';

import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import { useShareLinkAccess } from '@/hooks/useWhiteboardAccess';
import Whiteboard from '@/components/whiteboard/Whiteboard';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { Box, Text, Button, Flex, Card } from '@radix-ui/themes';
import { AlertTriangleIcon, LockIcon } from 'lucide-react';

interface SharedWhiteboardPageProps {
  params: Promise<{
    token: string;
  }>;
}

function SharedWhiteboardContent({ token }: { token: string }) {
  const { data, isLoading, error } = useShareLinkAccess(token);

  if (isLoading) {
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading shared whiteboard..." />
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <Card style={{ maxWidth: '400px', padding: '24px' }}>
          <Flex direction="column" align="center" gap="4">
            <Box>
              {error?.data?.code === 'FORBIDDEN' ? (
                <LockIcon size={48} className="text-red-500" />
              ) : (
                <AlertTriangleIcon size={48} className="text-yellow-500" />
              )}
            </Box>
            
            <Box style={{ textAlign: 'center' }}>
              <Text size="4" weight="bold" mb="2">
                {error?.data?.code === 'FORBIDDEN' 
                  ? 'Access Denied' 
                  : 'Whiteboard Not Found'
                }
              </Text>
              <Text size="2" color="gray">
                {error?.message || 'The shared whiteboard link is invalid or has expired.'}
              </Text>
            </Box>

            <Button 
              onClick={() => window.location.href = '/'}
              variant="soft"
            >
              Go to Homepage
            </Button>
          </Flex>
        </Card>
      </div>
    );
  }

  const { whiteboard, access } = data;

  // Determine if user can edit based on share link permissions
  const canEdit = access.permissions.includes('EDIT');
  const isReadOnly = !canEdit;

  return (
    <div className="h-screen">
      {/* Access info banner */}
      <Box 
        style={{ 
          backgroundColor: 'var(--color-surface)', 
          borderBottom: '1px solid var(--color-border)',
          padding: '8px 16px'
        }}
      >
        <Flex justify="between" align="center">
          <Flex align="center" gap="2">
            <Text size="2" color="gray">
              Viewing shared whiteboard as
            </Text>
            <Text size="2" weight="medium">
              {access.role}
            </Text>
            {isReadOnly && (
              <Text size="1" color="orange">
                (Read-only)
              </Text>
            )}
          </Flex>
          
          <Text size="1" color="gray">
            Shared via link
          </Text>
        </Flex>
      </Box>

      {/* Whiteboard content */}
      <div style={{ height: 'calc(100vh - 41px)' }}>
        <Whiteboard
          whiteboardId={whiteboard.id}
          initialData={{
            id: whiteboard.id,
            title: whiteboard.title,
            content: whiteboard.content as Record<string, unknown> | null,
            userId: whiteboard.userId,
            createdAt: new Date(whiteboard.createdAt),
            updatedAt: new Date(whiteboard.updatedAt),
            isPublic: whiteboard.isPublic,
            allowComments: whiteboard.allowComments,
            viewMode: whiteboard.viewMode,
            backgroundColor: whiteboard.backgroundColor,
            gridMode: whiteboard.gridMode,
            snapToGrid: whiteboard.snapToGrid,
            theme: whiteboard.theme,
            viewportX: whiteboard.viewportX,
            viewportY: whiteboard.viewportY,
            viewportZoom: whiteboard.viewportZoom,
          }}
          isReadOnly={isReadOnly}
        />
      </div>
    </div>
  );
}

export default async function SharedWhiteboardPage({ params }: SharedWhiteboardPageProps) {
  const { token } = await params;

  // Basic token validation
  if (!token || token.length < 10) {
    notFound();
  }

  return (
    <Suspense fallback={
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading shared whiteboard..." />
      </div>
    }>
      <SharedWhiteboardContent token={token} />
    </Suspense>
  );
}
