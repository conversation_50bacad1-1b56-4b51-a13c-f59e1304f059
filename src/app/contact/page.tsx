import type { Metadata } from 'next';
import { Container, Flex, Text, Card } from '@radix-ui/themes';
import { Mail, MessageCircle, Phone, MapPin, Clock, Users } from 'lucide-react';
import Breadcrumbs from '@/components/seo/Breadcrumbs';
import { FAQSection } from '@/components/seo/FAQSchema';

export const metadata: Metadata = {
  title: 'Contact Us - Get Support for AI-Powered Anchorboard App',
  description: 'Get in touch with our team for support, sales inquiries, or feedback. Multiple ways to reach us including email, chat, and phone support for whiteboard collaboration questions.',
  keywords: [
    'contact Anchorboard App',
    'whiteboard support',
    'collaboration software help',
    'AI whiteboard contact',
    'customer support',
    'sales inquiry',
    'technical support'
  ],
  openGraph: {
    title: 'Contact Us - Get Support for AI-Powered Anchorboard App',
    description: 'Get in touch with our team for support, sales inquiries, or feedback about our whiteboard collaboration platform.',
    type: 'website',
    images: ['/og-image.svg'],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Contact Us - Get Support for AI-Powered Anchorboard App',
    description: 'Get in touch with our team for support, sales inquiries, or feedback about our whiteboard collaboration platform.',
    images: ['/twitter-image.svg'],
  },
  alternates: {
    canonical: '/contact',
  },
};

export default function ContactPage() {
  const contactMethods = [
    {
      icon: Mail,
      title: 'Email Support',
      description: 'Get help with technical issues or general questions',
      contact: '<EMAIL>',
      action: 'mailto:<EMAIL>',
      responseTime: 'Usually responds within 24 hours'
    },
    {
      icon: MessageCircle,
      title: 'Sales Inquiries',
      description: 'Questions about pricing, plans, or enterprise solutions',
      contact: '<EMAIL>',
      action: 'mailto:<EMAIL>',
      responseTime: 'Usually responds within 4 hours'
    },
    {
      icon: Phone,
      title: 'Phone Support',
      description: 'Urgent issues or enterprise customers',
      contact: '+****************',
      action: 'tel:+***********',
      responseTime: 'Available Mon-Fri, 9 AM - 6 PM PST'
    }
  ];

  const offices = [
    {
      city: 'San Francisco',
      address: '123 Innovation Drive\nSan Francisco, CA 94105\nUnited States',
      timezone: 'PST (UTC-8)'
    },
    {
      city: 'London',
      address: '456 Tech Street\nLondon EC2A 4DP\nUnited Kingdom',
      timezone: 'GMT (UTC+0)'
    },
    {
      city: 'Singapore',
      address: '789 Business Park\nSingapore 018956\nSingapore',
      timezone: 'SGT (UTC+8)'
    }
  ];

  const faqs = [
    {
      question: 'How quickly can I get started?',
      answer: 'You can sign up and start using Anchorboard App immediately. No setup required - just create an account and start collaborating!'
    },
    {
      question: 'Do you offer training or onboarding?',
      answer: 'Yes! We provide comprehensive onboarding for Pro and Enterprise customers, including training sessions and best practices guides.'
    },
    {
      question: 'Can you help with data migration?',
      answer: 'Absolutely. Our team can help you migrate from other whiteboard tools. Contact our sales team to discuss your specific needs.'
    },
    {
      question: 'What integrations do you support?',
      answer: 'We integrate with popular tools like Slack, Microsoft Teams, Google Workspace, and more. Check our documentation for the full list.'
    }
  ];

  return (
    <>
      {/* Structured Data for Contact */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "ContactPage",
            "mainEntity": {
              "@type": "Organization",
              "name": "Anchorboard App",
              "contactPoint": [
                {
                  "@type": "ContactPoint",
                  "contactType": "customer service",
                  "email": "<EMAIL>",
                  "availableLanguage": "English"
                },
                {
                  "@type": "ContactPoint",
                  "contactType": "sales",
                  "email": "<EMAIL>",
                  "availableLanguage": "English"
                }
              ]
            }
          })
        }}
      />

      <Container size="4" className="min-h-screen py-12">
        <Flex direction="column" gap="12">
          {/* Breadcrumbs */}
          <Breadcrumbs
            items={[{ label: 'Contact Us' }]}
            className="mb-4"
          />

          {/* Hero Section */}
          <header className="text-center space-y-6">
            <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Get in Touch
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Have questions about Anchorboard App? Need help getting started? Our team is here to help you succeed 
              with collaborative whiteboarding and visual project management.
            </p>
          </header>

          {/* Contact Methods */}
          <section className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {contactMethods.map((method, index) => (
              <Card key={index} className="p-8 hover:shadow-lg transition-shadow">
                <Flex direction="column" gap="4" align="center" className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <method.icon className="w-6 h-6 text-white" />
                  </div>
                  <h2 className="text-xl font-semibold">{method.title}</h2>
                  <p className="text-gray-600">{method.description}</p>
                  <a 
                    href={method.action}
                    className="text-blue-600 font-medium hover:text-blue-700 transition-colors"
                  >
                    {method.contact}
                  </a>
                  <p className="text-sm text-gray-500">{method.responseTime}</p>
                </Flex>
              </Card>
            ))}
          </section>

          {/* Contact Form */}
          <section className="space-y-8">
            <h2 className="text-3xl font-bold text-center">Send us a Message</h2>
            <Card className="p-8 max-w-2xl mx-auto">
              <form className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                      First Name *
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">
                    Company
                  </label>
                  <input
                    type="text"
                    id="company"
                    name="company"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                    Subject *
                  </label>
                  <select
                    id="subject"
                    name="subject"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select a topic</option>
                    <option value="general">General Question</option>
                    <option value="support">Technical Support</option>
                    <option value="sales">Sales Inquiry</option>
                    <option value="partnership">Partnership</option>
                    <option value="feedback">Feedback</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    rows={5}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Tell us how we can help you..."
                  />
                </div>

                <button
                  type="submit"
                  className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                >
                  Send Message
                </button>
              </form>
            </Card>
          </section>

          {/* Office Locations */}
          <section className="space-y-8">
            <h2 className="text-3xl font-bold text-center">Our Offices</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {offices.map((office, index) => (
                <Card key={index} className="p-6 text-center">
                  <MapPin className="w-8 h-8 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-3">{office.city}</h3>
                  <div className="text-gray-600 whitespace-pre-line mb-3">{office.address}</div>
                  <div className="text-sm text-gray-500">{office.timezone}</div>
                </Card>
              ))}
            </div>
          </section>

          {/* FAQ Section with Schema */}
          <FAQSection faqs={faqs} title="Common Questions" />

          {/* CTA Section */}
          <section className="text-center bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-12">
            <h2 className="text-3xl font-bold mb-4">Ready to Start Collaborating?</h2>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
              Don't wait - join thousands of teams already using Anchorboard App for better collaboration and productivity.
            </p>
            <div className="flex justify-center gap-4">
              <a 
                href="/auth/signup" 
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Get Started Free
              </a>
              <a 
                href="/features" 
                className="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
              >
                View Features
              </a>
            </div>
          </section>
        </Flex>
      </Container>
    </>
  );
}
