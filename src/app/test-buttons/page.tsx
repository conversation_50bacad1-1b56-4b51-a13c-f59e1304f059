'use client';

import { useState } from 'react';
import { Button, Flex, Text } from '@radix-ui/themes';
import { Timer, Target, TrendingUp } from 'lucide-react';

export default function TestButtonsPage() {
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const handleAddCountdownTimer = () => {
    addLog('handleAddCountdownTimer called');
    console.log('handleAddCountdownTimer called');
  };

  const handleAddGoal = () => {
    addLog('handleAddGoal called');
    console.log('handleAddGoal called');
  };

  const handleAddHillChart = () => {
    addLog('handleAddHillChart called');
    console.log('handleAddHillChart called');
  };

  return (
    <div className="h-screen w-full flex flex-col p-4">
      <Text size="4" weight="bold" className="mb-4">Test Button Handlers</Text>
      
      <Flex gap="2" className="mb-4">
        <Button size="2" variant="soft" onClick={handleAddCountdownTimer}>
          <Timer size={16} />
          Timer
        </Button>
        <Button size="2" variant="soft" onClick={handleAddGoal}>
          <Target size={16} />
          Goal
        </Button>
        <Button size="2" variant="soft" onClick={handleAddHillChart}>
          <TrendingUp size={16} />
          Hill Chart
        </Button>
      </Flex>

      <div className="flex-1 border rounded p-4 bg-gray-50">
        <Text size="3" weight="bold" className="mb-2">Event Log:</Text>
        <div className="space-y-1">
          {logs.map((log, index) => (
            <div key={index} className="text-sm font-mono">
              {log}
            </div>
          ))}
          {logs.length === 0 && (
            <div className="text-sm text-gray-500">No events yet. Click the buttons above to test.</div>
          )}
        </div>
      </div>
    </div>
  );
}
