'use client';

import { useState } from 'react';
import { ReactFlowProvider } from 'reactflow';
import Whiteboard from '@/components/whiteboard/Whiteboard';
import type { WhiteboardData } from '@/types';

export default function TestWhiteboardPage() {
  const [whiteboardData] = useState<WhiteboardData>({
    id: 'test-whiteboard',
    title: 'Test Whiteboard',
    userId: 'test-user',
    content: {
      nodes: [],
      edges: [],
      viewport: { x: 0, y: 0, zoom: 1 }
    },
    // Settings are now part of the main object
    isPublic: false,
    allowComments: true,
    viewMode: 'edit',
    backgroundColor: '#ffffff',
    gridMode: false,
    snapToGrid: false,
    theme: 'light',
    // Viewport
    viewportX: 0,
    viewportY: 0,
    viewportZoom: 1,
    createdAt: new Date(),
    updatedAt: new Date()
  });

  return (
    <div className="h-screen w-full">
      <ReactFlowProvider>
        <Whiteboard
          initialData={whiteboardData}
          whiteboardId={whiteboardData.id}
          isReadOnly={false}
        />
      </ReactFlowProvider>
    </div>
  );
}
