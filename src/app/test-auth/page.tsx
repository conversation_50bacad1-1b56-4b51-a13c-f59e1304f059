'use client';

import { useAuth } from '@/hooks/useAuth';
import { signOut, signInWithGoogle, linkGoogleAccount } from '@/lib/auth-client';
import { Container, Card, Flex, Text, Button, Badge } from '@radix-ui/themes';
import { useRouter } from 'next/navigation';

export default function TestAuth() {
  const { data: session, status } = useAuth();
  const router = useRouter();

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      await signInWithGoogle();
    } catch (error) {
      console.error('Google sign-in error:', error);
    }
  };

  const handleLinkGoogle = async () => {
    try {
      await linkGoogleAccount(['https://www.googleapis.com/auth/drive.file']);
    } catch (error) {
      console.error('Link Google account error:', error);
    }
  };

  if (status === 'loading') {
    return (
      <Container size="2" className="min-h-screen flex items-center justify-center">
        <Text>Loading...</Text>
      </Container>
    );
  }

  return (
    <Container size="2" className="min-h-screen py-8">
      <Flex direction="column" gap="6">
        <Text size="8" weight="bold" align="center">
          Authentication Test Page
        </Text>

        <Card className="p-6">
          <Flex direction="column" gap="4">
            <Flex align="center" gap="3">
              <Text size="4" weight="medium">Status:</Text>
              <Badge color={status === 'authenticated' ? 'green' : 'red'}>
                {status}
              </Badge>
            </Flex>

            {status === 'authenticated' && session?.user && (
              <Flex direction="column" gap="3">
                <Text size="3" weight="medium">User Information:</Text>
                <Flex direction="column" gap="2" className="ml-4">
                  <Text size="2">
                    <strong>ID:</strong> {session.user.id}
                  </Text>
                  <Text size="2">
                    <strong>Email:</strong> {session.user.email || 'Not provided'}
                  </Text>
                  <Text size="2">
                    <strong>Name:</strong> {session.user.name || 'Not provided'}
                  </Text>
                  <Text size="2">
                    <strong>Image:</strong> {session.user.image || 'Not provided'}
                  </Text>
                </Flex>
              </Flex>
            )}

            <Flex direction="column" gap="3">
              <Text size="3" weight="medium">Actions:</Text>
              <Flex gap="3" wrap="wrap">
                {status === 'authenticated' ? (
                  <>
                    <Button onClick={handleSignOut} color="red">
                      Sign Out
                    </Button>
                    <Button onClick={handleLinkGoogle} variant="outline">
                      Link Google Drive
                    </Button>
                  </>
                ) : (
                  <>
                    <Button onClick={handleGoogleSignIn}>
                      Sign In with Google
                    </Button>
                    <Button onClick={() => router.push('/auth/signin')} variant="outline">
                      Email Sign In
                    </Button>
                    <Button onClick={() => router.push('/auth/signup')} variant="outline">
                      Email Sign Up
                    </Button>
                  </>
                )}
              </Flex>
            </Flex>
          </Flex>
        </Card>

        <Card className="p-6">
          <Flex direction="column" gap="3">
            <Text size="3" weight="medium">Google OAuth Setup Instructions:</Text>
            <Flex direction="column" gap="2" className="ml-4">
              <Text size="2">
                1. Go to <a href="https://console.cloud.google.com/apis/dashboard" target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">Google Cloud Console</a>
              </Text>
              <Text size="2">
                2. Create a new project or select an existing one
              </Text>
              <Text size="2">
                3. Enable the Google+ API
              </Text>
              <Text size="2">
                4. Go to Credentials → Create Credentials → OAuth 2.0 Client IDs
              </Text>
              <Text size="2">
                5. Set authorized redirect URIs:
              </Text>
              <Text size="1" className="ml-4 font-mono bg-gray-100 p-2 rounded">
                http://localhost:3000/api/auth/callback/google (for development)
                <br />
                https://your-domain.com/api/auth/callback/google (for production)
              </Text>
              <Text size="2">
                6. Copy the Client ID and Client Secret to your environment variables:
              </Text>
              <Text size="1" className="ml-4 font-mono bg-gray-100 p-2 rounded">
                GOOGLE_CLIENT_ID=your_client_id_here
                <br />
                GOOGLE_CLIENT_SECRET=your_client_secret_here
              </Text>
            </Flex>
          </Flex>
        </Card>
      </Flex>
    </Container>
  );
}
