import { type NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { logAuthAttempt, logAuthSuccess, logAuthError, logDebug } from '@/lib/logger';

export async function GET(request: NextRequest) {
  const startTime = Date.now();

  try {
    logAuthAttempt('test-auth-endpoint', {
      userAgent: request.headers.get('user-agent'),
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
    });

    logDebug('Test Auth - Headers received', {
      headers: Object.fromEntries(request.headers.entries()),
    });

    // Try to get session using BetterAuth
    const response = await auth.handler(request);
    const sessionData = await response.json();

    logDebug('Test Auth - Session data retrieved', {
      sessionData,
      duration: Date.now() - startTime,
    });

    logAuthSuccess(
      sessionData?.user?.id || 'unknown',
      'test-auth-endpoint',
      {
        hasSession: !!sessionData?.session,
        duration: Date.now() - startTime,
      }
    );

    return NextResponse.json({
      success: true,
      session: sessionData,
      headers: Object.fromEntries(request.headers.entries()),
    });
  } catch (error) {
    logAuthError('test-auth-endpoint', error, {
      duration: Date.now() - startTime,
      userAgent: request.headers.get('user-agent'),
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
    });

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
