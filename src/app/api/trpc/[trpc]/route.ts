import { fetchRe<PERSON>Handler } from '@trpc/server/adapters/fetch';
import type { NextRequest } from 'next/server';

import { appRouter } from '@/server/routers/_app';
import { createTRPCContextAppRouter } from '@/server/trpc';
import { logApiError } from '@/lib/logger';

const handler = (req: NextRequest) =>
  fetchRequestHandler({
    endpoint: '/api/trpc',
    req,
    router: appRouter,
    createContext: (opts) => createTRPCContextAppRouter(opts),
    onError:
      process.env.NODE_ENV === 'development'
        ? ({ path, error }) => {
            logApiError(
              'tRPC',
              path ?? '<no-path>',
              error,
              { message: error.message }
            );
          }
        : undefined,
  });

export { handler as GET, handler as POST };
