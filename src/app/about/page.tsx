import type { Metada<PERSON> } from 'next';
import { Container, Flex, Text, Card } from '@radix-ui/themes';
import { Users, Target, Zap, Heart, Award, Globe } from 'lucide-react';
import Breadcrumbs from '@/components/seo/Breadcrumbs';

export const metadata: Metadata = {
  title: 'About Us - AI-Powered Whiteboard for Team Collaboration',
  description: 'Learn about our mission to revolutionize team collaboration with AI-powered whiteboard tools. Discover our story, values, and commitment to helping teams work better together.',
  keywords: [
    'about Anchorboard App',
    'team collaboration story',
    'AI whiteboard company',
    'visual collaboration mission',
    'Anchorboard App team',
    'collaboration software company'
  ],
  openGraph: {
    title: 'About Us - AI-Powered Whiteboard for Team Collaboration',
    description: 'Learn about our mission to revolutionize team collaboration with AI-powered whiteboard tools.',
    type: 'website',
    images: ['/og-image.svg'],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'About Us - AI-Powered Whiteboard for Team Collaboration',
    description: 'Learn about our mission to revolutionize team collaboration with AI-powered whiteboard tools.',
    images: ['/twitter-image.svg'],
  },
  alternates: {
    canonical: '/about',
  },
};

export default function AboutPage() {
  const values = [
    {
      id: 'collaboration',
      icon: Users,
      title: 'Collaboration First',
      description: 'We believe the best ideas come from teams working together. Our tools are designed to break down barriers and enable seamless collaboration.'
    },
    {
      id: 'purpose',
      icon: Target,
      title: 'Purpose-Driven',
      description: 'Every feature we build serves a clear purpose: helping teams visualize ideas, track progress, and achieve their goals more effectively.'
    },
    {
      id: 'innovation',
      icon: Zap,
      title: 'Innovation',
      description: 'We leverage cutting-edge AI technology to make whiteboarding more intuitive, powerful, and accessible to everyone.'
    },
    {
      id: 'user-centric',
      icon: Heart,
      title: 'User-Centric',
      description: 'Our users are at the heart of everything we do. We listen, learn, and continuously improve based on real feedback and needs.'
    }
  ];

  const stats = [
    { id: 'users', number: '10,000+', label: 'Active Users' },
    { id: 'whiteboards', number: '50,000+', label: 'Whiteboards Created' },
    { id: 'countries', number: '25+', label: 'Countries' },
    { id: 'uptime', number: '99.9%', label: 'Uptime' }
  ];

  return (
    <>
      {/* Structured Data for Organization */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "Anchorboard App",
            "description": "AI-powered collaborative whiteboard platform for teams",
            "url": process.env.NEXT_PUBLIC_BASE_URL || "https://anchorboard.xyz",
            "logo": `${process.env.NEXT_PUBLIC_BASE_URL || "https://anchorboard.xyz"}/logo.png`,
            "foundingDate": "2024",
            "sameAs": [
              "https://twitter.com/whiteboardapp",
              "https://linkedin.com/company/whiteboardapp"
            ],
            "contactPoint": {
              "@type": "ContactPoint",
              "contactType": "customer service",
              "email": "<EMAIL>"
            }
          })
        }}
      />

      <Container size="4" className="min-h-screen py-12">
        <Flex direction="column" gap="12">
          {/* Breadcrumbs */}
          <Breadcrumbs
            items={[{ label: 'About Us' }]}
            className="mb-4"
          />

          {/* Hero Section */}
          <header className="text-center space-y-6">
            <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              About Anchorboard App
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              We're on a mission to revolutionize how teams collaborate, visualize ideas, and bring projects to life. 
              Our AI-powered whiteboard platform combines the simplicity of traditional whiteboards with the power of modern technology.
            </p>
          </header>

          {/* Mission Section */}
          <section className="text-center space-y-6">
            <h2 className="text-3xl font-bold">Our Mission</h2>
            <Card className="p-8 bg-gradient-to-r from-blue-50 to-purple-50">
              <Text size="5" className="text-gray-700 leading-relaxed">
                "To empower every team with intuitive, AI-enhanced collaboration tools that transform ideas into action, 
                making remote and hybrid work as effective as being in the same room."
              </Text>
            </Card>
          </section>

          {/* Stats Section */}
          <section className="text-center space-y-8">
            <h2 className="text-3xl font-bold">Trusted by Teams Worldwide</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat) => (
                <Card key={stat.id} className="p-6 text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">{stat.number}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </Card>
              ))}
            </div>
          </section>

          {/* Values Section */}
          <section className="space-y-8">
            <h2 className="text-3xl font-bold text-center">Our Values</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {values.map((value) => (
                <Card key={value.id} className="p-8 hover:shadow-lg transition-shadow">
                  <Flex direction="column" gap="4">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <value.icon className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="text-xl font-semibold">{value.title}</h3>
                    </div>
                    <p className="text-gray-600 leading-relaxed">{value.description}</p>
                  </Flex>
                </Card>
              ))}
            </div>
          </section>

          {/* Story Section */}
          <section className="space-y-6">
            <h2 className="text-3xl font-bold text-center">Our Story</h2>
            <Card className="p-8">
              <div className="space-y-6 text-gray-700 leading-relaxed">
                <p>
                  Anchorboard App was born from a simple observation: despite having access to countless digital tools, 
                  teams still struggled to collaborate effectively, especially in remote and hybrid environments.
                </p>
                <p>
                  We noticed that the best brainstorming sessions happened around physical whiteboards, where ideas could 
                  flow freely and everyone could contribute visually. But traditional digital whiteboards felt clunky, 
                  limited, and disconnected from modern workflows.
                </p>
                <p>
                  That's when we decided to build something different. By combining the intuitive nature of physical 
                  whiteboards with AI-powered assistance, real-time collaboration, and purpose-built tools for modern 
                  teams, we created a platform that doesn't just replicate the whiteboard experience—it enhances it.
                </p>
                <p>
                  Today, thousands of teams use Anchorboard App to run more effective meetings, track project progress 
                  with hill charts, set and achieve goals, and bring their best ideas to life.
                </p>
              </div>
            </Card>
          </section>

          {/* CTA Section */}
          <section className="text-center bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-12">
            <h2 className="text-3xl font-bold mb-4">Join Our Journey</h2>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
              Be part of the future of team collaboration. Start using Anchorboard App today and experience 
              the difference AI-powered collaboration can make.
            </p>
            <div className="flex justify-center gap-4">
              <a 
                href="/auth/signup" 
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Get Started Free
              </a>
              <a 
                href="/contact" 
                className="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
              >
                Contact Us
              </a>
            </div>
          </section>
        </Flex>
      </Container>
    </>
  );
}
