import type { Metadata } from 'next';
import { Container, Flex, Text, Card } from '@radix-ui/themes';
import { FileText, Shield, Users, AlertTriangle, Scale, Globe } from 'lucide-react';
import Breadcrumbs from '@/components/seo/Breadcrumbs';

export const metadata: Metadata = {
  title: 'Terms of Service | Anchorboard App - User Agreement & Conditions',
  description: 'Read our terms of service and user agreement for Anchorboard App. Understand your rights and responsibilities when using our collaborative whiteboard platform.',
  keywords: [
    'terms of service',
    'user agreement',
    'terms and conditions',
    'legal terms',
    'service agreement',
    'whiteboard terms',
    'collaboration terms'
  ],
  openGraph: {
    title: 'Terms of Service - Anchorboard App User Agreement',
    description: 'Complete terms of service and user agreement for our collaborative whiteboard platform.',
    type: 'website',
    url: '/terms',
    images: ['/og-image.svg'],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Terms of Service - Anchorboard App User Agreement',
    description: 'Read our comprehensive terms of service and user agreement.',
    images: ['/twitter-image.svg'],
  },
  alternates: {
    canonical: '/terms',
  },
};

export default function TermsPage() {
  const sections = [
    {
      icon: Users,
      title: 'Account Terms',
      content: [
        'You must be 13 years or older to use this service',
        'Provide accurate and complete registration information',
        'Maintain the security of your account credentials',
        'You are responsible for all activity under your account',
        'One person or legal entity per account'
      ]
    },
    {
      icon: FileText,
      title: 'Acceptable Use',
      content: [
        'Use the service for lawful purposes only',
        'Respect intellectual property rights',
        'No harassment, abuse, or harmful content',
        'No spam, malware, or security violations',
        'Comply with all applicable laws and regulations'
      ]
    },
    {
      icon: Shield,
      title: 'Content & Ownership',
      content: [
        'You retain ownership of your content',
        'Grant us license to provide the service',
        'We may remove content that violates these terms',
        'Backup your important data regularly',
        'Content may be deleted if account is terminated'
      ]
    },
    {
      icon: Scale,
      title: 'Service Availability',
      content: [
        'We strive for 99.9% uptime but cannot guarantee it',
        'Scheduled maintenance will be announced in advance',
        'We may modify or discontinue features with notice',
        'Free accounts have usage limitations',
        'Premium features require active subscription'
      ]
    },
    {
      icon: AlertTriangle,
      title: 'Limitation of Liability',
      content: [
        'Service provided "as is" without warranties',
        'We are not liable for indirect or consequential damages',
        'Maximum liability limited to amount paid for service',
        'You assume responsibility for your use of the service',
        'Some jurisdictions may not allow these limitations'
      ]
    },
    {
      icon: Globe,
      title: 'General Terms',
      content: [
        'These terms are governed by California law',
        'Disputes resolved through binding arbitration',
        'If any provision is invalid, others remain in effect',
        'We may update these terms with 30 days notice',
        'Continued use constitutes acceptance of changes'
      ]
    }
  ];

  return (
    <>
      {/* Structured Data for Terms of Service */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "Terms of Service",
            "description": "Anchorboard App terms of service and user agreement",
            "url": `${process.env.NEXT_PUBLIC_BASE_URL || "https://anchorboard.xyz"}/terms`,
            "mainEntity": {
              "@type": "TermsOfService",
              "name": "Anchorboard App Terms of Service",
              "dateModified": "2025-07-07",
              "publisher": {
                "@type": "Organization",
                "name": "Anchorboard App"
              }
            }
          })
        }}
      />

      <Container size="4" className="py-8">
        <Flex direction="column" gap="8">
          {/* Breadcrumbs */}
          <Breadcrumbs 
            items={[{ label: 'Terms of Service' }]}
            className="mb-4"
          />

          {/* Header */}
          <header className="text-center space-y-4">
            <h1 className="text-4xl font-bold">Terms of Service</h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              These terms govern your use of Anchorboard App. By using our service, you agree to these terms.
              Please read them carefully.
            </p>
            <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
              <FileText size={16} />
              <span>Last updated: July 7, 2025</span>
            </div>
          </header>

          {/* Quick Summary */}
          <Card className="p-6 bg-green-50 border-green-200">
            <h2 className="text-xl font-semibold mb-4 text-green-900">Terms Summary</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <Users className="w-8 h-8 text-green-600 mx-auto mb-2" />
                <div className="font-medium">Be Respectful</div>
                <div className="text-gray-600">Use service responsibly</div>
              </div>
              <div className="text-center">
                <Shield className="w-8 h-8 text-green-600 mx-auto mb-2" />
                <div className="font-medium">Your Content</div>
                <div className="text-gray-600">You own what you create</div>
              </div>
              <div className="text-center">
                <Scale className="w-8 h-8 text-green-600 mx-auto mb-2" />
                <div className="font-medium">Fair Use</div>
                <div className="text-gray-600">Follow our guidelines</div>
              </div>
            </div>
          </Card>

          {/* Terms Sections */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {sections.map((section, index) => (
              <Card key={index} className="p-6 hover:shadow-lg transition-shadow">
                <Flex direction="column" gap="4">
                  <Flex align="center" gap="3">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <section.icon className="w-5 h-5 text-green-600" />
                    </div>
                    <h3 className="text-lg font-semibold">{section.title}</h3>
                  </Flex>
                  <ul className="space-y-2">
                    {section.content.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start gap-2 text-sm text-gray-600">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </Flex>
              </Card>
            ))}
          </div>

          {/* Important Notice */}
          <Card className="p-6 bg-yellow-50 border-yellow-200">
            <Flex align="center" gap="3" className="mb-4">
              <AlertTriangle className="w-6 h-6 text-yellow-600" />
              <h3 className="text-lg font-semibold text-yellow-900">Important Notice</h3>
            </Flex>
            <div className="text-sm text-yellow-800 space-y-2">
              <p>
                <strong>Termination:</strong> We may suspend or terminate your account if you violate these terms.
                You may close your account at any time.
              </p>
              <p>
                <strong>Changes:</strong> We may update these terms from time to time. We'll notify you of significant changes
                via email or through the service.
              </p>
              <p>
                <strong>Contact:</strong> If you have questions about these terms, please contact our legal team.
              </p>
            </div>
          </Card>

          {/* Contact Section */}
          <Card className="p-8 text-center bg-gray-50">
            <h2 className="text-2xl font-semibold mb-4">Questions About These Terms?</h2>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              If you have any questions about these terms of service or need clarification on any point,
              our legal team is here to help.
            </p>
            <div className="flex justify-center gap-4">
              <a 
                href="/contact" 
                className="bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors"
              >
                Contact Us
              </a>
              <a 
                href="mailto:<EMAIL>" 
                className="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
              >
                Email Legal Team
              </a>
            </div>
          </Card>

          {/* Legal Information */}
          <div className="text-center text-sm text-gray-500 space-y-2">
            <p>These terms are effective as of July 7, 2025 and replace all previous versions.</p>
            <p>Anchorboard App is operated by Anchorboard Inc., a Delaware corporation.</p>
          </div>
        </Flex>
      </Container>
    </>
  );
}
