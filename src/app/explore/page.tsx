'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Container, Flex, Text, Button, Card } from '@radix-ui/themes';
import { FileText, Clock, User, ArrowLeft, Users } from 'lucide-react';
import { useRecentPublicWhiteboards } from '@/hooks/useWhiteboardData';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

export default function ExplorePage() {
  const [limit] = useState(50);
  const { data, isLoading, error } = useRecentPublicWhiteboards(limit);

  if (isLoading) {
    return (
      <Container size="4" className="py-8">
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" text="Loading public whiteboards..." />
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="4" className="py-8">
        <div className="text-center py-12">
          <Text size="5" weight="medium" className="mb-2">
            Error loading whiteboards
          </Text>
          <Text size="3" color="gray">
            Please try again later.
          </Text>
        </div>
      </Container>
    );
  }

  return (
    <Container size="4" className="py-8">
      <Flex direction="column" gap="6">
        <Flex justify="between" align="center">
          <Link href="/">
            <Button variant="soft" size="2">
              <ArrowLeft size={16} />
              Back to Home
            </Button>
          </Link>
          <Text size="6" weight="bold">Explore Public Whiteboards</Text>
          <div className="w-24" /> {/* Spacer for centering */}
        </Flex>

        {data?.whiteboards && data.whiteboards.length > 0 ? (
          <>
            <Text size="3" color="gray" className="text-center">
              Discover and explore whiteboards shared by the community
            </Text>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {data.whiteboards.map((whiteboard) => (
                <Link
                  key={whiteboard.id}
                  href={`/whiteboard?id=${whiteboard.id}`}
                  className="no-underline"
                >
                  <Card className="p-4 hover:shadow-lg transition-all duration-200 hover:-translate-y-1 h-full">
                    <Flex direction="column" gap="3" className="h-full">
                      <Flex align="center" gap="2">
                        <FileText size={20} className="text-blue-600 flex-shrink-0" />
                        <Text size="3" weight="medium" className="line-clamp-2">
                          {whiteboard.title}
                        </Text>
                      </Flex>
                      
                      <Flex direction="column" gap="2" className="flex-grow">
                        <Flex align="center" gap="2">
                          <User size={14} className="text-gray-500 flex-shrink-0" />
                          <Text size="2" color="gray" className="truncate">
                            {whiteboard.user.name || whiteboard.user.email.split('@')[0]}
                          </Text>
                        </Flex>
                        
                        <Flex align="center" gap="2">
                          <Clock size={14} className="text-gray-500 flex-shrink-0" />
                          <Text size="2" color="gray">
                            {formatTimeAgo(new Date(whiteboard.updatedAt))}
                          </Text>
                        </Flex>
                      </Flex>
                      
                      <Flex justify="between" align="center">
                        <Flex align="center" gap="1">
                          <Users size={14} className="text-blue-600" />
                          <Text size="1" color="blue">Public</Text>
                        </Flex>
                        <Text size="2" color="blue" weight="medium">
                          View →
                        </Text>
                      </Flex>
                    </Flex>
                  </Card>
                </Link>
              ))}
            </div>
          </>
        ) : (
          <Card className="p-8">
            <Flex direction="column" gap="4" align="center">
              <FileText size={48} color="gray" />
              <Text size="4" weight="medium" color="gray">
                No public whiteboards yet
              </Text>
              <Text size="3" color="gray" align="center">
                Be the first to share a whiteboard with the community!
              </Text>
              <Link href="/whiteboard">
                <Button size="3">
                  Create a Whiteboard
                </Button>
              </Link>
            </Flex>
          </Card>
        )}
      </Flex>
    </Container>
  );
}

function formatTimeAgo(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} ${days === 1 ? 'day' : 'days'} ago`;
  } else {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  }
}