import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest'
import type { Pool } from 'pg'
import { createTestDbConnection, cleanupTestData, setupTestUser, testUsers } from './utils'

// Mock the auth module to test the actual database operations
vi.mock('@/lib/auth', async () => {
  const { Kysely, PostgresDialect } = await import('kysely')
  const { Pool } = await import('pg')
  
  const db = new Kysely({
    dialect: new PostgresDialect({
      pool: new Pool({
        connectionString: process.env.DATABASE_URL || '****************************************************************************/postgres',
        ssl: { rejectUnauthorized: false },
      }),
    }),
  })

  return {
    auth: {
      api: {
        signUpEmail: vi.fn(),
        signInEmail: vi.fn(),
        signOut: vi.fn(),
      },
      handler: {
        GET: vi.fn(),
        POST: vi.fn(),
      },
    },
    db,
  }
})

describe('Authentication Database Operations', () => {
  let pool: Pool

  beforeAll(async () => {
    pool = createTestDbConnection()
  })

  afterAll(async () => {
    if (pool) {
      await cleanupTestData(pool)
      await pool.end()
    }
  })

  beforeEach(async () => {
    // Clean up before each test
    await cleanupTestData(pool)
  })

  describe('User Registration', () => {
    it('should create a new user successfully', async () => {
      const { email, name } = testUsers.validUser

      // Simulate user creation
      const userResult = await pool.query(
        `INSERT INTO "user" (id, email, "emailVerified", name) 
         VALUES (gen_random_uuid(), $1, false, $2) 
         RETURNING id, email, name, "emailVerified"`,
        [email, name]
      )

      expect(userResult.rows).toHaveLength(1)
      expect(userResult.rows[0].email).toBe(email)
      expect(userResult.rows[0].name).toBe(name)
      expect(userResult.rows[0].emailVerified).toBe(false)
    })

    it('should not allow duplicate email registration', async () => {
      const { email, name } = testUsers.validUser

      // Create first user
      await pool.query(
        `INSERT INTO "user" (id, email, "emailVerified", name) 
         VALUES (gen_random_uuid(), $1, false, $2)`,
        [email, name]
      )

      // Try to create second user with same email
      await expect(
        pool.query(
          `INSERT INTO "user" (id, email, "emailVerified", name) 
           VALUES (gen_random_uuid(), $1, false, $2)`,
          [email, 'Another Name']
        )
      ).rejects.toThrow()
    })

    it('should create account record with password', async () => {
      const { email, password, name } = testUsers.validUser

      // Create user
      const userResult = await pool.query(
        `INSERT INTO "user" (id, email, "emailVerified", name) 
         VALUES (gen_random_uuid(), $1, false, $2) 
         RETURNING id`,
        [email, name]
      )
      const userId = userResult.rows[0].id

      // Create account
      const accountResult = await pool.query(
        `INSERT INTO "account" (id, "userId", "accountId", "providerId", password) 
         VALUES (gen_random_uuid(), $1, $2, 'credential', $3) 
         RETURNING id, "accountId", "providerId"`,
        [userId, email, password]
      )

      expect(accountResult.rows).toHaveLength(1)
      expect(accountResult.rows[0].accountId).toBe(email)
      expect(accountResult.rows[0].providerId).toBe('credential')
    })
  })

  describe('User Authentication', () => {
    beforeEach(async () => {
      // Setup existing user for login tests
      await setupTestUser(pool, testUsers.existingUser)
    })

    it('should authenticate existing user with correct credentials', async () => {
      const { email, password } = testUsers.existingUser

      // Simulate login verification
      const result = await pool.query(`
        SELECT u.id, u.email, u.name, a.password
        FROM "user" u
        JOIN "account" a ON u.id = a."userId"
        WHERE u.email = $1 AND a."providerId" = 'credential'
      `, [email])

      expect(result.rows).toHaveLength(1)
      expect(result.rows[0].email).toBe(email)
      expect(result.rows[0].password).toBe(password)
    })

    it('should not authenticate with wrong email', async () => {
      const result = await pool.query(`
        SELECT u.id, u.email, u.name, a.password
        FROM "user" u
        JOIN "account" a ON u.id = a."userId"
        WHERE u.email = $1 AND a."providerId" = 'credential'
      `, ['<EMAIL>'])

      expect(result.rows).toHaveLength(0)
    })

    it('should not authenticate with wrong password', async () => {
      const { email } = testUsers.existingUser

      const result = await pool.query(`
        SELECT u.id, u.email, u.name, a.password
        FROM "user" u
        JOIN "account" a ON u.id = a."userId"
        WHERE u.email = $1 AND a."providerId" = 'credential' AND a.password = $2
      `, [email, 'wrongpassword'])

      expect(result.rows).toHaveLength(0)
    })
  })

  describe('Session Management', () => {
    let userId: string

    beforeEach(async () => {
      userId = await setupTestUser(pool, testUsers.existingUser)
    })

    it('should create session for authenticated user', async () => {
      const sessionToken = 'test-session-token-' + Date.now()
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days

      const sessionResult = await pool.query(
        `INSERT INTO "session" (id, "userId", "expiresAt", token) 
         VALUES (gen_random_uuid(), $1, $2, $3) 
         RETURNING id, "userId", token`,
        [userId, expiresAt, sessionToken]
      )

      expect(sessionResult.rows).toHaveLength(1)
      expect(sessionResult.rows[0].userId).toBe(userId)
      expect(sessionResult.rows[0].token).toBe(sessionToken)
    })

    it('should retrieve user by session token', async () => {
      const sessionToken = 'test-session-token-' + Date.now()
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)

      // Create session
      await pool.query(
        `INSERT INTO "session" (id, "userId", "expiresAt", token) 
         VALUES (gen_random_uuid(), $1, $2, $3)`,
        [userId, expiresAt, sessionToken]
      )

      // Retrieve user by session
      const result = await pool.query(`
        SELECT u.id, u.email, u.name, s.token, s."expiresAt"
        FROM "user" u
        JOIN "session" s ON u.id = s."userId"
        WHERE s.token = $1 AND s."expiresAt" > NOW()
      `, [sessionToken])

      expect(result.rows).toHaveLength(1)
      expect(result.rows[0].email).toBe(testUsers.existingUser.email)
      expect(result.rows[0].token).toBe(sessionToken)
    })

    it('should not retrieve user with expired session', async () => {
      const sessionToken = 'expired-session-token-' + Date.now()
      const expiresAt = new Date(Date.now() - 1000) // Expired 1 second ago

      // Create expired session
      await pool.query(
        `INSERT INTO "session" (id, "userId", "expiresAt", token) 
         VALUES (gen_random_uuid(), $1, $2, $3)`,
        [userId, expiresAt, sessionToken]
      )

      // Try to retrieve user by expired session
      const result = await pool.query(`
        SELECT u.id, u.email, u.name, s.token, s."expiresAt"
        FROM "user" u
        JOIN "session" s ON u.id = s."userId"
        WHERE s.token = $1 AND s."expiresAt" > NOW()
      `, [sessionToken])

      expect(result.rows).toHaveLength(0)
    })

    it('should clean up sessions on user deletion', async () => {
      const sessionToken = 'cleanup-test-token-' + Date.now()
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)

      // Create session
      await pool.query(
        `INSERT INTO "session" (id, "userId", "expiresAt", token) 
         VALUES (gen_random_uuid(), $1, $2, $3)`,
        [userId, expiresAt, sessionToken]
      )

      // Verify session exists
      let sessionResult = await pool.query(
        `SELECT id FROM "session" WHERE token = $1`,
        [sessionToken]
      )
      expect(sessionResult.rows).toHaveLength(1)

      // Delete user (should cascade to sessions)
      await pool.query(`DELETE FROM "account" WHERE "userId" = $1`, [userId])
      await pool.query(`DELETE FROM "user" WHERE id = $1`, [userId])

      // Verify session is deleted
      sessionResult = await pool.query(
        `SELECT id FROM "session" WHERE token = $1`,
        [sessionToken]
      )
      expect(sessionResult.rows).toHaveLength(0)
    })
  })
})
