import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ReactFlowProvider } from 'reactflow';
import Whiteboard from '@/components/whiteboard/Whiteboard';
import { createTodoListNode } from '@/lib/whiteboard-utils';
import type { WhiteboardData, TodoItem } from '@/types';

// Mock React Flow
vi.mock('reactflow', async () => {
  const actual = await vi.importActual('reactflow');
  return {
    ...actual,
    default: vi.fn(({ children, onInit, onNodesChange, onEdgesChange, onConnect }) => {
      const mockInstance = {
        screenToFlowPosition: vi.fn(() => ({ x: 100, y: 100 })),
        getViewport: vi.fn(() => ({ x: 0, y: 0, zoom: 1 })),
      };
      
      if (onInit) {
        setTimeout(() => onInit(mockInstance), 0);
      }
      
      return (
        <div data-testid="react-flow">
          {children}
          <button 
            data-testid="mock-node-change" 
            onClick={() => onNodesChange && onNodesChange([])}
          >
            Trigger Node Change
          </button>
        </div>
      );
    }),
    Controls: () => <div data-testid="react-flow-controls">Controls</div>,
    applyNodeChanges: vi.fn((changes, nodes) => nodes),
    applyEdgeChanges: vi.fn((changes, edges) => edges),
    addEdge: vi.fn((connection, edges) => [...edges, { id: 'new-edge', ...connection }]),
    useReactFlow: () => ({
      setNodes: vi.fn(),
      setEdges: vi.fn(),
      getNodes: vi.fn(() => []),
      getEdges: vi.fn(() => []),
    }),
    Handle: ({ children, ...props }: any) => <div data-testid="handle" {...props}>{children}</div>,
    Position: {
      Top: 'top',
      Bottom: 'bottom',
      Left: 'left',
      Right: 'right',
    },
  };
});

// Mock hooks
vi.mock('@/hooks/useWhiteboardData', () => ({
  useAutoSaveWhiteboard: () => ({
    autoSave: vi.fn(),
    isAutoSaving: false,
  }),
  useUpdateWhiteboard: () => ({
    mutate: vi.fn(),
  }),
}));

vi.mock('@/hooks/useNetworkStatus', () => ({
  useNetworkStatus: () => ({
    isOnline: true,
    isSlowConnection: false,
  }),
}));

const mockWhiteboardData: WhiteboardData = {
  id: 'test-whiteboard',
  title: 'Test Whiteboard',
  content: {
    nodes: [],
    edges: [],
    viewport: { x: 0, y: 0, zoom: 1 },
  },
  isPublic: false,
  allowComments: true,
  viewMode: 'edit',
  backgroundColor: '#ffffff',
  gridMode: false,
  snapToGrid: false,
  theme: 'light',
  viewportX: 0,
  viewportY: 0,
  viewportZoom: 1,
  createdAt: new Date(),
  updatedAt: new Date(),
  userId: 'test-user',
};

describe('TodoList Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Toolbar Integration', () => {
    it('should render TodoList button in toolbar', () => {
      render(
        <ReactFlowProvider>
          <Whiteboard 
            initialData={mockWhiteboardData}
            whiteboardId="test-whiteboard"
            isReadOnly={false}
          />
        </ReactFlowProvider>
      );

      expect(screen.getByRole('button', { name: /todo list/i })).toBeInTheDocument();
    });

    it('should not show TodoList button in read-only mode', () => {
      render(
        <ReactFlowProvider>
          <Whiteboard 
            initialData={mockWhiteboardData}
            whiteboardId="test-whiteboard"
            isReadOnly={true}
          />
        </ReactFlowProvider>
      );

      expect(screen.queryByRole('button', { name: /todo list/i })).not.toBeInTheDocument();
    });

    it('should have TodoList button with correct icon and styling', () => {
      render(
        <ReactFlowProvider>
          <Whiteboard 
            initialData={mockWhiteboardData}
            whiteboardId="test-whiteboard"
            isReadOnly={false}
          />
        </ReactFlowProvider>
      );

      const todoButton = screen.getByRole('button', { name: /todo list/i });
      expect(todoButton).toHaveClass('rt-Button');
      expect(todoButton).toHaveAttribute('data-size', '2');
      expect(todoButton).toHaveAttribute('data-variant', 'soft');
    });

    it('should be positioned correctly among other toolbar buttons', () => {
      render(
        <ReactFlowProvider>
          <Whiteboard 
            initialData={mockWhiteboardData}
            whiteboardId="test-whiteboard"
            isReadOnly={false}
          />
        </ReactFlowProvider>
      );

      const buttons = screen.getAllByRole('button');
      const buttonTexts = buttons.map(btn => btn.textContent);
      
      expect(buttonTexts).toContain('Timer');
      expect(buttonTexts).toContain('Goal');
      expect(buttonTexts).toContain('Hill Chart');
      expect(buttonTexts).toContain('Link');
      expect(buttonTexts).toContain('Todo List');
      expect(buttonTexts).toContain('AI Assistant');
    });
  });

  describe('Utility Functions', () => {
    describe('createTodoListNode', () => {
      it('should create todo list node with default values', () => {
        const todoData = {
          title: 'My Todo List',
        };
        const position = { x: 100, y: 200 };

        const node = createTodoListNode(todoData, position);

        expect(node.type).toBe('todoList');
        expect(node.position).toEqual(position);
        expect(node.data.title).toBe('My Todo List');
        expect(node.data.items).toEqual([]);
        expect(node.data.backgroundColor).toBe('#ffffff');
        expect(node.data.textColor).toBe('#1f2937');
        expect(node.data.maxItems).toBe(20);
        expect(node.id).toMatch(/^todoList-/);
      });

      it('should create todo list node with custom values', () => {
        const todoData = {
          title: 'Custom Todo',
          items: [
            {
              id: 'task-1',
              text: 'Test task',
              completed: false,
              createdAt: new Date('2024-01-01'),
            },
          ] as TodoItem[],
          backgroundColor: '#f0f0f0',
          textColor: '#333333',
          maxItems: 10,
        };
        const position = { x: 50, y: 75 };

        const node = createTodoListNode(todoData, position);

        expect(node.type).toBe('todoList');
        expect(node.position).toEqual(position);
        expect(node.data.title).toBe('Custom Todo');
        expect(node.data.items).toHaveLength(1);
        expect(node.data.items[0].text).toBe('Test task');
        expect(node.data.backgroundColor).toBe('#f0f0f0');
        expect(node.data.textColor).toBe('#333333');
        expect(node.data.maxItems).toBe(10);
      });

      it('should generate unique IDs for multiple nodes', () => {
        const todoData = { title: 'Test' };
        const position = { x: 0, y: 0 };

        const node1 = createTodoListNode(todoData, position);
        const node2 = createTodoListNode(todoData, position);

        expect(node1.id).not.toBe(node2.id);
        expect(node1.id).toMatch(/^todoList-/);
        expect(node2.id).toMatch(/^todoList-/);
      });

      it('should handle empty items array', () => {
        const todoData = {
          title: 'Empty List',
          items: [],
        };
        const position = { x: 0, y: 0 };

        const node = createTodoListNode(todoData, position);

        expect(node.data.items).toEqual([]);
        expect(Array.isArray(node.data.items)).toBe(true);
      });

      it('should preserve item properties correctly', () => {
        const testDate = new Date('2024-01-15T10:30:00Z');
        const todoData = {
          title: 'Test List',
          items: [
            {
              id: 'unique-task-id',
              text: 'Important task',
              completed: true,
              createdAt: testDate,
            },
          ] as TodoItem[],
        };
        const position = { x: 0, y: 0 };

        const node = createTodoListNode(todoData, position);

        expect(node.data.items[0].id).toBe('unique-task-id');
        expect(node.data.items[0].text).toBe('Important task');
        expect(node.data.items[0].completed).toBe(true);
        expect(node.data.items[0].createdAt).toEqual(testDate);
      });
    });
  });

  describe('Node Type Registration', () => {
    it('should include todoList in available node types', () => {
      // This test verifies that the todoList node type is properly registered
      // We can't directly test the nodeTypes object, but we can verify the node renders
      render(
        <ReactFlowProvider>
          <Whiteboard 
            initialData={{
              ...mockWhiteboardData,
              content: {
                nodes: [
                  createTodoListNode({ title: 'Test Todo' }, { x: 0, y: 0 })
                ],
                edges: [],
                viewport: { x: 0, y: 0, zoom: 1 },
              }
            }}
            whiteboardId="test-whiteboard"
            isReadOnly={false}
          />
        </ReactFlowProvider>
      );

      // The React Flow component should render without errors
      expect(screen.getByTestId('react-flow')).toBeInTheDocument();
    });
  });

  describe('Data Persistence', () => {
    it('should handle todo list data in whiteboard content', () => {
      const todoNode = createTodoListNode({
        title: 'Persistent Todo',
        items: [
          {
            id: 'task-1',
            text: 'Save this task',
            completed: false,
            createdAt: new Date(),
          },
        ],
      }, { x: 100, y: 100 });

      const whiteboardWithTodo = {
        ...mockWhiteboardData,
        content: {
          nodes: [todoNode],
          edges: [],
          viewport: { x: 0, y: 0, zoom: 1 },
        },
      };

      render(
        <ReactFlowProvider>
          <Whiteboard 
            initialData={whiteboardWithTodo}
            whiteboardId="test-whiteboard"
            isReadOnly={false}
          />
        </ReactFlowProvider>
      );

      // Should render without errors and include the todo data
      expect(screen.getByTestId('react-flow')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed todo data gracefully', () => {
      const malformedNode = {
        id: 'malformed-todo',
        type: 'todoList',
        position: { x: 0, y: 0 },
        data: {
          title: 'Malformed Todo',
          items: null, // Invalid items
        },
      };

      const whiteboardWithMalformed = {
        ...mockWhiteboardData,
        content: {
          nodes: [malformedNode],
          edges: [],
          viewport: { x: 0, y: 0, zoom: 1 },
        },
      };

      // Should not throw an error
      expect(() => {
        render(
          <ReactFlowProvider>
            <Whiteboard 
              initialData={whiteboardWithMalformed}
              whiteboardId="test-whiteboard"
              isReadOnly={false}
            />
          </ReactFlowProvider>
        );
      }).not.toThrow();
    });
  });
});
