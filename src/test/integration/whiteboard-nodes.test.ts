import { describe, it, expect } from 'vitest';
import type { Edge } from 'reactflow';
import { 
  createCountdownTimerNode, 
  createGoalNode, 
  createHillChartNode,
  transformStoredDataToReactFlow,
  transformReactFlowDataForStorage
} from '@/lib/whiteboard-utils';
import type { CountdownTimer, Goal, HillChart } from '@/types';

describe('Whiteboard Node Integration', () => {
  describe('Node Creation', () => {
    it('should create countdown timer node correctly', () => {
      const timerData: Omit<CountdownTimer, 'id' | 'whiteboardId'> = {
        title: 'Test Timer',
        endDate: new Date('2024-12-31T23:59:59'),
        position: { x: 100, y: 100 },
        style: {
          color: '#000000',
          fontSize: 16,
          backgroundColor: '#ffffff',
        },
        isActive: false,
      };

      const node = createCountdownTimerNode(timerData, { x: 100, y: 100 });

      expect(node.type).toBe('countdownTimer');
      expect(node.position).toEqual({ x: 100, y: 100 });
      expect(node.data.title).toBe('Test Timer');
      expect(node.data.isActive).toBe(false);
      expect(node.id).toMatch(/^countdownTimer-/);
    });

    it('should create goal node correctly', () => {
      const goalData: Omit<Goal, 'id' | 'whiteboardId'> = {
        title: 'Test Goal',
        description: 'Test description',
        priority: 'high',
        status: 'not-started',
        position: { x: 200, y: 200 },
        tags: ['test', 'goal'],
      };

      const node = createGoalNode(goalData, { x: 200, y: 200 });

      expect(node.type).toBe('goal');
      expect(node.position).toEqual({ x: 200, y: 200 });
      expect(node.data.title).toBe('Test Goal');
      expect(node.data.priority).toBe('high');
      expect(node.data.status).toBe('not-started');
      expect(node.data.tags).toEqual(['test', 'goal']);
      expect(node.id).toMatch(/^goal-/);
    });

    it('should create hill chart node correctly', () => {
      const hillChartData: Omit<HillChart, 'id' | 'whiteboardId'> = {
        title: 'Test Hill Chart',
        items: [
          {
            id: 'item-1',
            name: 'Item 1',
            position: 25,
            color: '#ff0000',
            description: 'Test item',
          },
        ],
        position: { x: 300, y: 300 },
        width: 400,
        height: 200,
      };

      const node = createHillChartNode(hillChartData, { x: 300, y: 300 });

      expect(node.type).toBe('hillChart');
      expect(node.position).toEqual({ x: 300, y: 300 });
      expect(node.data.title).toBe('Test Hill Chart');
      expect(node.data.width).toBe(400);
      expect(node.data.height).toBe(200);
      expect(node.data.items).toHaveLength(1);
      expect(node.id).toMatch(/^hillChart-/);
    });
  });

  describe('Data Transformation', () => {
    it('should transform legacy data to React Flow nodes', () => {
      const legacyData = {
        nodes: [],
        edges: [],
        viewport: { x: 0, y: 0, zoom: 1 },
        countdownTimers: [
          {
            id: 'timer-1',
            whiteboardId: 'wb-1',
            title: 'Legacy Timer',
            endDate: new Date('2024-12-31T23:59:59'),
            position: { x: 100, y: 100 },
            style: { color: '#000000', fontSize: 16 },
            isActive: false,
          },
        ],
        goals: [
          {
            id: 'goal-1',
            whiteboardId: 'wb-1',
            title: 'Legacy Goal',
            priority: 'medium',
            status: 'in-progress',
            position: { x: 200, y: 200 },
            tags: ['legacy'],
          },
        ],
        hillCharts: [
          {
            id: 'chart-1',
            whiteboardId: 'wb-1',
            title: 'Legacy Chart',
            items: [],
            position: { x: 300, y: 300 },
            width: 400,
            height: 200,
          },
        ],
      };

      const result = transformStoredDataToReactFlow(JSON.stringify(legacyData));

      expect(result.nodes).toHaveLength(3);
      expect(result.nodes[0].type).toBe('countdownTimer');
      expect(result.nodes[1].type).toBe('goal');
      expect(result.nodes[2].type).toBe('hillChart');
      
      // Check that legacy arrays are empty (converted to nodes)
      expect(result.additionalData.countdownTimers).toEqual([]);
      expect(result.additionalData.goals).toEqual([]);
      expect(result.additionalData.hillCharts).toEqual([]);
    });

    it('should handle storage format correctly', () => {
      const nodes = [
        createCountdownTimerNode({
          title: 'Test Timer',
          endDate: new Date('2024-12-31T23:59:59'),
          position: { x: 100, y: 100 },
          style: { color: '#000000', fontSize: 16 },
          isActive: false,
        }, { x: 100, y: 100 }),
      ];

      const edges: Edge[] = [];
      const viewport = { x: 0, y: 0, zoom: 1 };

      const stored = transformReactFlowDataForStorage(nodes, edges, viewport);
      const parsed = JSON.parse(stored);

      expect(parsed.nodes).toHaveLength(1);
      expect(parsed.nodes[0].type).toBe('countdownTimer');
      expect(parsed.edges).toEqual([]);
      expect(parsed.viewport).toEqual(viewport);
      
      // Should not include empty arrays
      expect(parsed.countdownTimers).toBeUndefined();
      expect(parsed.goals).toBeUndefined();
      expect(parsed.hillCharts).toBeUndefined();
    });
  });
});
