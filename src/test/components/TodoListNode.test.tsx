import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ReactFlowProvider } from 'reactflow';
import TodoListNode from '@/components/whiteboard/nodes/TodoListNode';
import type { TodoListNodeData } from '@/types';

// Mock React Flow
vi.mock('reactflow', async () => {
  const actual = await vi.importActual('reactflow');
  return {
    ...actual,
    useReactFlow: () => ({
      setNodes: vi.fn(),
    }),
    Handle: ({ children, ...props }: any) => <div data-testid="handle" {...props}>{children}</div>,
    Position: {
      Top: 'top',
      Bottom: 'bottom',
      Left: 'left',
      Right: 'right',
    },
  };
});

const mockSetNodes = vi.fn();
vi.mocked(require('reactflow').useReactFlow).mockReturnValue({
  setNodes: mockSetNodes,
});

const defaultTodoData: TodoListNodeData = {
  title: 'Test Todo List',
  items: [
    {
      id: 'task-1',
      text: 'Complete project',
      completed: false,
      createdAt: new Date('2024-01-01'),
    },
    {
      id: 'task-2',
      text: 'Review code',
      completed: true,
      createdAt: new Date('2024-01-02'),
    },
  ],
  backgroundColor: '#ffffff',
  textColor: '#1f2937',
  maxItems: 20,
};

const renderTodoListNode = (data: TodoListNodeData = defaultTodoData, selected = false) => {
  return render(
    <ReactFlowProvider>
      <TodoListNode
        id="test-node"
        data={data}
        selected={selected}
        type="todoList"
        position={{ x: 0, y: 0 }}
        positionAbsolute={{ x: 0, y: 0 }}
        dragging={false}
        isConnectable={true}
        zIndex={1}
        xPos={0}
        yPos={0}
      />
    </ReactFlowProvider>
  );
};

describe('TodoListNode', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render todo list with title and items', () => {
      renderTodoListNode();

      expect(screen.getByText('Test Todo List')).toBeInTheDocument();
      expect(screen.getByText('Complete project')).toBeInTheDocument();
      expect(screen.getByText('Review code')).toBeInTheDocument();
      expect(screen.getByText('1/2')).toBeInTheDocument(); // Progress indicator
    });

    it('should render empty state when no items', () => {
      const emptyData = { ...defaultTodoData, items: [] };
      renderTodoListNode(emptyData);

      expect(screen.getByText('Test Todo List')).toBeInTheDocument();
      expect(screen.getByText('No tasks yet. Add one above!')).toBeInTheDocument();
      expect(screen.getByText('0/0')).toBeInTheDocument();
    });

    it('should show completion celebration when all tasks done', () => {
      const completedData = {
        ...defaultTodoData,
        items: [
          {
            id: 'task-1',
            text: 'Task 1',
            completed: true,
            createdAt: new Date(),
          },
          {
            id: 'task-2',
            text: 'Task 2',
            completed: true,
            createdAt: new Date(),
          },
        ],
      };
      renderTodoListNode(completedData);

      expect(screen.getByText('🎉 All tasks completed!')).toBeInTheDocument();
      expect(screen.getByText('2/2')).toBeInTheDocument();
    });

    it('should apply custom styling', () => {
      const styledData = {
        ...defaultTodoData,
        backgroundColor: '#f0f0f0',
        textColor: '#333333',
      };
      renderTodoListNode(styledData);

      const card = screen.getByText('Test Todo List').closest('.rt-Card');
      expect(card).toHaveStyle({ backgroundColor: '#f0f0f0' });
    });

    it('should show edit and delete buttons when hovered or selected', async () => {
      const user = userEvent.setup();
      renderTodoListNode(defaultTodoData, true);

      // Should show buttons when selected
      expect(screen.getByRole('button', { name: /edit/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /delete/i })).toBeInTheDocument();
    });
  });

  describe('Adding Tasks', () => {
    it('should add new task when typing and pressing Enter', async () => {
      const user = userEvent.setup();
      renderTodoListNode();

      const input = screen.getByPlaceholderText('Add a new task...');
      await user.type(input, 'New task');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(mockSetNodes).toHaveBeenCalledWith(expect.any(Function));
      });

      // Verify the function passed to setNodes would add the new task
      const setNodesCall = mockSetNodes.mock.calls[0][0];
      const mockNodes = [
        {
          id: 'test-node',
          data: defaultTodoData,
        },
      ];
      const result = setNodesCall(mockNodes);
      
      expect(result[0].data.items).toHaveLength(3);
      expect(result[0].data.items[2].text).toBe('New task');
      expect(result[0].data.items[2].completed).toBe(false);
    });

    it('should add new task when clicking plus button', async () => {
      const user = userEvent.setup();
      renderTodoListNode();

      const input = screen.getByPlaceholderText('Add a new task...');
      const addButton = screen.getByRole('button', { name: /plus/i });

      await user.type(input, 'Another task');
      await user.click(addButton);

      await waitFor(() => {
        expect(mockSetNodes).toHaveBeenCalledWith(expect.any(Function));
      });
    });

    it('should not add empty task', async () => {
      const user = userEvent.setup();
      renderTodoListNode();

      const input = screen.getByPlaceholderText('Add a new task...');
      await user.type(input, '   '); // Only whitespace
      await user.keyboard('{Enter}');

      expect(mockSetNodes).not.toHaveBeenCalled();
    });

    it('should clear input after adding task', async () => {
      const user = userEvent.setup();
      renderTodoListNode();

      const input = screen.getByPlaceholderText('Add a new task...');
      await user.type(input, 'Test task');
      await user.keyboard('{Enter}');

      // Input should be cleared (this is handled by the component state)
      expect(input).toHaveValue('');
    });
  });

  describe('Task Management', () => {
    it('should toggle task completion when clicking checkbox', async () => {
      const user = userEvent.setup();
      renderTodoListNode();

      const checkboxes = screen.getAllByRole('checkbox');
      await user.click(checkboxes[0]); // Click first task checkbox

      await waitFor(() => {
        expect(mockSetNodes).toHaveBeenCalledWith(expect.any(Function));
      });

      // Verify the task completion was toggled
      const setNodesCall = mockSetNodes.mock.calls[0][0];
      const mockNodes = [{ id: 'test-node', data: defaultTodoData }];
      const result = setNodesCall(mockNodes);
      
      expect(result[0].data.items[0].completed).toBe(true); // Was false, now true
    });

    it('should delete task when clicking delete button', async () => {
      const user = userEvent.setup();
      renderTodoListNode();

      const deleteButtons = screen.getAllByRole('button', { name: /trash/i });
      await user.click(deleteButtons[0]); // Delete first task

      await waitFor(() => {
        expect(mockSetNodes).toHaveBeenCalledWith(expect.any(Function));
      });

      // Verify the task was deleted
      const setNodesCall = mockSetNodes.mock.calls[0][0];
      const mockNodes = [{ id: 'test-node', data: defaultTodoData }];
      const result = setNodesCall(mockNodes);
      
      expect(result[0].data.items).toHaveLength(1);
      expect(result[0].data.items[0].id).toBe('task-2'); // First task removed
    });

    it('should show completed tasks with strikethrough styling', () => {
      renderTodoListNode();

      const completedTask = screen.getByText('Review code');
      expect(completedTask).toHaveStyle({ textDecoration: 'line-through' });
      
      const incompleteTask = screen.getByText('Complete project');
      expect(incompleteTask).toHaveStyle({ textDecoration: 'none' });
    });
  });

  describe('Editing Mode', () => {
    it('should enter edit mode when clicking edit button', async () => {
      const user = userEvent.setup();
      renderTodoListNode(defaultTodoData, true);

      const editButton = screen.getByRole('button', { name: /edit/i });
      await user.click(editButton);

      expect(screen.getByText('Edit Todo List')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test Todo List')).toBeInTheDocument();
    });

    it('should save changes when clicking save button', async () => {
      const user = userEvent.setup();
      renderTodoListNode(defaultTodoData, true);

      // Enter edit mode
      const editButton = screen.getByRole('button', { name: /edit/i });
      await user.click(editButton);

      // Change title
      const titleInput = screen.getByDisplayValue('Test Todo List');
      await user.clear(titleInput);
      await user.type(titleInput, 'Updated Title');

      // Save changes
      const saveButton = screen.getByRole('button', { name: /check/i });
      await user.click(saveButton);

      await waitFor(() => {
        expect(mockSetNodes).toHaveBeenCalledWith(expect.any(Function));
      });

      // Verify the title was updated
      const setNodesCall = mockSetNodes.mock.calls[0][0];
      const mockNodes = [{ id: 'test-node', data: defaultTodoData }];
      const result = setNodesCall(mockNodes);
      
      expect(result[0].data.title).toBe('Updated Title');
    });

    it('should cancel changes when clicking cancel button', async () => {
      const user = userEvent.setup();
      renderTodoListNode(defaultTodoData, true);

      // Enter edit mode
      const editButton = screen.getByRole('button', { name: /edit/i });
      await user.click(editButton);

      // Change title
      const titleInput = screen.getByDisplayValue('Test Todo List');
      await user.clear(titleInput);
      await user.type(titleInput, 'Changed Title');

      // Cancel changes
      const cancelButton = screen.getByRole('button', { name: /x/i });
      await user.click(cancelButton);

      // Should exit edit mode without saving
      expect(screen.queryByText('Edit Todo List')).not.toBeInTheDocument();
      expect(screen.getByText('Test Todo List')).toBeInTheDocument();
      expect(mockSetNodes).not.toHaveBeenCalled();
    });
  });

  describe('Node Deletion', () => {
    it('should delete entire node when clicking delete button', async () => {
      const user = userEvent.setup();
      renderTodoListNode(defaultTodoData, true);

      const deleteButton = screen.getByRole('button', { name: /trash/i });
      await user.click(deleteButton);

      await waitFor(() => {
        expect(mockSetNodes).toHaveBeenCalledWith(expect.any(Function));
      });

      // Verify the node was removed
      const setNodesCall = mockSetNodes.mock.calls[0][0];
      const mockNodes = [
        { id: 'test-node', data: defaultTodoData },
        { id: 'other-node', data: {} },
      ];
      const result = setNodesCall(mockNodes);
      
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('other-node');
    });
  });

  describe('Progress Indicator', () => {
    it('should show correct progress percentage', () => {
      renderTodoListNode(); // 1 of 2 completed = 50%

      expect(screen.getByText('50% complete')).toBeInTheDocument();
    });

    it('should show progress bar with correct width', () => {
      renderTodoListNode(); // 1 of 2 completed = 50%

      const progressBar = document.querySelector('.bg-blue-500');
      expect(progressBar).toHaveStyle({ width: '50%' });
    });
  });
});
