import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import type { Pool } from 'pg'
import { createTestDbConnection, cleanupTestData } from './utils'

describe('Database Connectivity', () => {
  let pool: Pool

  beforeAll(async () => {
    pool = createTestDbConnection()
  })

  afterAll(async () => {
    if (pool) {
      await cleanupTestData(pool)
      await pool.end()
    }
  })

  it('should connect to the database successfully', async () => {
    const result = await pool.query('SELECT version();')
    expect(result.rows).toHaveLength(1)
    expect(result.rows[0].version).toContain('PostgreSQL')
  })

  it('should have the required better-auth tables', async () => {
    // Check if user table exists
    const userTableResult = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'user'
      );
    `)
    expect(userTableResult.rows[0].exists).toBe(true)

    // Check if account table exists
    const accountTableResult = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'account'
      );
    `)
    expect(accountTableResult.rows[0].exists).toBe(true)

    // Check if session table exists
    const sessionTableResult = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'session'
      );
    `)
    expect(sessionTableResult.rows[0].exists).toBe(true)
  })

  it('should be able to insert and retrieve user data', async () => {
    const testEmail = '<EMAIL>'
    const testName = 'DB Test User'

    try {
      // Insert test user
      const insertResult = await pool.query(
        `INSERT INTO "user" (id, email, "emailVerified", name) 
         VALUES (gen_random_uuid(), $1, true, $2) 
         RETURNING id, email, name`,
        [testEmail, testName]
      )

      expect(insertResult.rows).toHaveLength(1)
      expect(insertResult.rows[0].email).toBe(testEmail)
      expect(insertResult.rows[0].name).toBe(testName)

      // Retrieve the user
      const selectResult = await pool.query(
        `SELECT id, email, name FROM "user" WHERE email = $1`,
        [testEmail]
      )

      expect(selectResult.rows).toHaveLength(1)
      expect(selectResult.rows[0].email).toBe(testEmail)
      expect(selectResult.rows[0].name).toBe(testName)

      // Clean up
      await pool.query(`DELETE FROM "user" WHERE email = $1`, [testEmail])
    } catch (error) {
      // Clean up in case of error
      await pool.query(`DELETE FROM "user" WHERE email = $1`, [testEmail])
      throw error
    }
  })

  it('should enforce unique email constraint', async () => {
    const testEmail = '<EMAIL>'

    try {
      // Insert first user
      await pool.query(
        `INSERT INTO "user" (id, email, "emailVerified", name) 
         VALUES (gen_random_uuid(), $1, true, 'First User')`,
        [testEmail]
      )

      // Try to insert second user with same email
      await expect(
        pool.query(
          `INSERT INTO "user" (id, email, "emailVerified", name) 
           VALUES (gen_random_uuid(), $1, true, 'Second User')`,
          [testEmail]
        )
      ).rejects.toThrow()

      // Clean up
      await pool.query(`DELETE FROM "user" WHERE email = $1`, [testEmail])
    } catch (error) {
      // Clean up in case of error
      await pool.query(`DELETE FROM "user" WHERE email = $1`, [testEmail])
      throw error
    }
  })

  it('should handle foreign key constraints between user and account tables', async () => {
    const testEmail = '<EMAIL>'
    let userId: string | undefined

    try {
      // Insert user
      const userResult = await pool.query(
        `INSERT INTO "user" (id, email, "emailVerified", name)
         VALUES (gen_random_uuid(), $1, true, 'FK Test User')
         RETURNING id`,
        [testEmail]
      )
      userId = userResult.rows[0].id

      // Insert account
      const accountResult = await pool.query(
        `INSERT INTO "account" (id, "userId", "accountId", "providerId", password)
         VALUES (gen_random_uuid(), $1, $2, 'credential', 'testpassword')
         RETURNING id`,
        [userId, testEmail]
      )

      expect(accountResult.rows).toHaveLength(1)

      // Verify the relationship
      const joinResult = await pool.query(`
        SELECT u.email, a."accountId", a."providerId"
        FROM "user" u
        JOIN "account" a ON u.id = a."userId"
        WHERE u.email = $1
      `, [testEmail])

      expect(joinResult.rows).toHaveLength(1)
      expect(joinResult.rows[0].email).toBe(testEmail)
      expect(joinResult.rows[0].accountId).toBe(testEmail)
      expect(joinResult.rows[0].providerId).toBe('credential')

      // Clean up
      await pool.query(`DELETE FROM "account" WHERE "userId" = $1`, [userId])
      await pool.query(`DELETE FROM "user" WHERE id = $1`, [userId])
    } catch (error) {
      // Clean up in case of error
      if (userId) {
        await pool.query(`DELETE FROM "account" WHERE "userId" = $1`, [userId])
        await pool.query(`DELETE FROM "user" WHERE id = $1`, [userId])
      }
      throw error
    }
  })
})
