import { describe, it, expect, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import Whiteboard from '@/components/whiteboard/Whiteboard';
import type { WhiteboardData, WhiteboardSettings } from '@/types';

// Mock the ReactFlowWrapper component
vi.mock('@/components/whiteboard/ReactFlowWrapper', () => ({
  default: vi.fn(({ onSave, onExport, settings }) => (
    <div data-testid="react-flow-wrapper">
      <div data-testid="settings-theme">{settings?.theme}</div>
      <div data-testid="settings-view-mode">{settings?.viewMode}</div>
      <button 
        data-testid="trigger-save" 
        onClick={() => onSave && onSave([], [], { x: 0, y: 0, zoom: 1 })}
      >
        Trigger Save
      </button>
      <button 
        data-testid="trigger-export" 
        onClick={() => onExport && onExport('png')}
      >
        Trigger Export
      </button>
    </div>
  ))
}));

// Mock other components
vi.mock('@/components/whiteboard/WhiteboardSettings', () => ({
  default: vi.fn(({ onClose, onSettingsChange }) => (
    <div data-testid="whiteboard-settings">
      <button onClick={onClose}>Close Settings</button>
      <button 
        onClick={() => onSettingsChange({ 
          isPublic: false, 
          allowComments: true, 
          viewMode: 'edit', 
          backgroundColor: '#ffffff', 
          gridMode: true, 
          snapToGrid: true, 
          theme: 'dark' 
        })}
      >
        Change Settings
      </button>
    </div>
  ))
}));

vi.mock('@/components/features/countdown/CountdownEditor', () => ({
  default: vi.fn(() => <div data-testid="countdown-editor">Countdown Editor</div>)
}));

vi.mock('@/components/features/goals/GoalEditor', () => ({
  default: vi.fn(() => <div data-testid="goal-editor">Goal Editor</div>)
}));

vi.mock('@/components/features/hill-charts/HillChartEditor', () => ({
  default: vi.fn(() => <div data-testid="hill-chart-editor">Hill Chart Editor</div>)
}));

describe('Whiteboard', () => {
  const mockOnSave = vi.fn();
  
  const defaultSettings: WhiteboardSettings = {
    isPublic: false,
    allowComments: true,
    viewMode: 'edit',
    backgroundColor: '#ffffff',
    gridMode: false,
    snapToGrid: false,
    theme: 'light'
  };

  const sampleWhiteboardData: WhiteboardData = {
    id: 'test-whiteboard',
    name: 'Test Whiteboard',
    userId: 'test-user',
    data: JSON.stringify({
      nodes: [
        {
          id: 'node-1',
          type: 'textNode',
          position: { x: 100, y: 100 },
          data: { label: 'Test Node' }
        }
      ],
      edges: [],
      viewport: { x: 0, y: 0, zoom: 1 },
      countdownTimers: [],
      goals: [],
      hillCharts: []
    }),
    settings: defaultSettings,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<Whiteboard onSave={mockOnSave} />);
    
    expect(screen.getByTestId('react-flow-wrapper')).toBeInTheDocument();
  });

  it('loads initial data correctly', () => {
    render(
      <Whiteboard 
        initialData={sampleWhiteboardData}
        onSave={mockOnSave} 
      />
    );
    
    expect(screen.getByTestId('react-flow-wrapper')).toBeInTheDocument();
    expect(screen.getByTestId('settings-theme')).toHaveTextContent('light');
    expect(screen.getByTestId('settings-view-mode')).toHaveTextContent('edit');
  });

  it('handles read-only mode correctly', () => {
    render(
      <Whiteboard 
        initialData={sampleWhiteboardData}
        onSave={mockOnSave}
        isReadOnly={true}
      />
    );
    
    expect(screen.getByTestId('settings-view-mode')).toHaveTextContent('view');
  });

  it('opens and closes settings dialog', async () => {
    render(<Whiteboard onSave={mockOnSave} />);
    
    const settingsButton = screen.getByLabelText(/settings/i);
    fireEvent.click(settingsButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('whiteboard-settings')).toBeInTheDocument();
    });

    const closeButton = screen.getByText('Close Settings');
    fireEvent.click(closeButton);
    
    await waitFor(() => {
      expect(screen.queryByTestId('whiteboard-settings')).not.toBeInTheDocument();
    });
  });

  it('updates settings when changed', async () => {
    render(<Whiteboard onSave={mockOnSave} />);
    
    const settingsButton = screen.getByLabelText(/settings/i);
    fireEvent.click(settingsButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('whiteboard-settings')).toBeInTheDocument();
    });

    const changeSettingsButton = screen.getByText('Change Settings');
    fireEvent.click(changeSettingsButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('settings-theme')).toHaveTextContent('dark');
    });
  });

  it('handles save functionality', async () => {
    render(<Whiteboard onSave={mockOnSave} />);
    
    const triggerSaveButton = screen.getByTestId('trigger-save');
    fireEvent.click(triggerSaveButton);
    
    // The component should handle the React Flow change
    expect(screen.getByTestId('react-flow-wrapper')).toBeInTheDocument();
  });

  it('handles export functionality', async () => {
    render(<Whiteboard onSave={mockOnSave} />);
    
    const exportButton = screen.getByText(/export/i);
    fireEvent.click(exportButton);
    
    // Export should be triggered
    expect(screen.getByTestId('react-flow-wrapper')).toBeInTheDocument();
  });

  it('opens countdown timer editor', async () => {
    render(<Whiteboard onSave={mockOnSave} />);
    
    const timerButton = screen.getByLabelText(/timer/i);
    fireEvent.click(timerButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('countdown-editor')).toBeInTheDocument();
    });
  });

  it('opens goal editor', async () => {
    render(<Whiteboard onSave={mockOnSave} />);
    
    const goalButton = screen.getByLabelText(/goal/i);
    fireEvent.click(goalButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('goal-editor')).toBeInTheDocument();
    });
  });

  it('opens hill chart editor', async () => {
    render(<Whiteboard onSave={mockOnSave} />);
    
    const hillChartButton = screen.getByLabelText(/hill chart/i);
    fireEvent.click(hillChartButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('hill-chart-editor')).toBeInTheDocument();
    });
  });

  it('calls onSave when save button is clicked', async () => {
    render(<Whiteboard onSave={mockOnSave} />);
    
    const saveButton = screen.getByText(/save/i);
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalled();
    });
  });

  it('handles invalid initial data gracefully', () => {
    const invalidData: WhiteboardData = {
      ...sampleWhiteboardData,
      data: 'invalid json'
    };

    expect(() => {
      render(
        <Whiteboard 
          initialData={invalidData}
          onSave={mockOnSave} 
        />
      );
    }).not.toThrow();
    
    expect(screen.getByTestId('react-flow-wrapper')).toBeInTheDocument();
  });
});
