import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Theme } from '@radix-ui/themes';
import VisibilityToggle from '@/components/whiteboard/VisibilityToggle';
import { useUpdateWhiteboard } from '@/hooks/useWhiteboardData';

// Mock the useUpdateWhiteboard hook
vi.mock('@/hooks/useWhiteboardData', () => ({
  useUpdateWhiteboard: vi.fn(),
}));

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const mockMutateAsync = vi.fn();
const mockUpdateWhiteboard = {
  mutateAsync: mockMutateAsync,
  isPending: false,
  error: null,
};

describe('VisibilityToggle', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    vi.clearAllMocks();
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
    
    (useUpdateWhiteboard as any).mockReturnValue(mockUpdateWhiteboard);
  });

  const renderComponent = (props: any) => {
    return render(
      <QueryClientProvider client={queryClient}>
        <Theme>
          <VisibilityToggle {...props} />
        </Theme>
      </QueryClientProvider>
    );
  };

  it('renders with private state correctly', () => {
    renderComponent({
      whiteboardId: 'test-id',
      isPublic: false,
    });

    expect(screen.getByText('Private')).toBeInTheDocument();
    expect(screen.getByRole('switch')).not.toBeChecked();
  });

  it('renders with public state correctly', () => {
    renderComponent({
      whiteboardId: 'test-id',
      isPublic: true,
    });

    expect(screen.getByText('Public')).toBeInTheDocument();
    expect(screen.getByRole('switch')).toBeChecked();
  });

  it('calls updateWhiteboard when toggled', async () => {
    mockMutateAsync.mockResolvedValue({});

    renderComponent({
      whiteboardId: 'test-id',
      isPublic: false,
    });

    const toggle = screen.getByRole('switch');
    fireEvent.click(toggle);

    await waitFor(() => {
      expect(mockMutateAsync).toHaveBeenCalledWith({
        id: 'test-id',
        isPublic: true,
      });
    });
  });

  it('shows loading state when toggling', async () => {
    let resolvePromise: (value: any) => void;
    const promise = new Promise((resolve) => {
      resolvePromise = resolve;
    });
    mockMutateAsync.mockReturnValue(promise);

    renderComponent({
      whiteboardId: 'test-id',
      isPublic: false,
    });

    const toggle = screen.getByRole('switch');
    fireEvent.click(toggle);

    // Should show loading spinner
    await waitFor(() => {
      expect(screen.getByTestId('loading-spinner') || document.querySelector('.animate-spin')).toBeInTheDocument();
    });

    // Resolve the promise
    resolvePromise!({});
  });

  it('is disabled when disabled prop is true', () => {
    renderComponent({
      whiteboardId: 'test-id',
      isPublic: false,
      disabled: true,
    });

    const toggle = screen.getByRole('switch');
    expect(toggle).toBeDisabled();
  });
});
