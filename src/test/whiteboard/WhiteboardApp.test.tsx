import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import WhiteboardApp from '@/components/whiteboard/WhiteboardApp';
import { useAuth } from '@/hooks/useAuth';
import { useCreateWhiteboard } from '@/hooks/useWhiteboardData';

// Mock the hooks
vi.mock('@/hooks/useAuth');
vi.mock('@/hooks/useWhiteboardData');
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}));

// Mock dynamic imports
vi.mock('next/dynamic', () => ({
  default: (importFn: any, options?: any) => {
    const Component = () => <div data-testid="mocked-component">Mocked Component</div>;
    return Component;
  },
}));

// Mock other dependencies
vi.mock('@/lib/auth-client', () => ({
  signOut: vi.fn(),
}));

vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
  },
  Toaster: () => <div data-testid="toaster" />,
}));

const mockUseAuth = useAuth as any;
const mockUseCreateWhiteboard = useCreateWhiteboard as any;

describe('WhiteboardApp - Infinite Loop Fix', () => {
  let queryClient: QueryClient;
  let mockMutate: any;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    mockMutate = vi.fn();

    // Mock useAuth to return authenticated user
    mockUseAuth.mockReturnValue({
      data: {
        user: { id: 'user-1', name: 'Test User' },
      },
      status: 'authenticated',
    });

    // Mock useCreateWhiteboard
    mockUseCreateWhiteboard.mockReturnValue({
      mutate: mockMutate,
      isPending: false,
      error: null,
    });

    // Mock other whiteboard hooks
    vi.doMock('@/hooks/useWhiteboardData', () => ({
      useWhiteboards: () => ({
        data: { whiteboards: [], total: 0 },
        isLoading: false,
        error: null,
      }),
      useWhiteboard: () => ({
        data: null,
        isLoading: false,
        error: null,
      }),
      useSearchWhiteboards: () => ({
        data: { whiteboards: [] },
        isLoading: false,
      }),
      useCreateWhiteboard: mockUseCreateWhiteboard,
      useDeleteWhiteboard: () => ({
        mutate: vi.fn(),
      }),
    }));
  });

  it('should only call createWhiteboard once when isNewWhiteboard is true', async () => {
    const TestWrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    );

    // Render with isNewWhiteboard=true and no whiteboardId
    render(
      <TestWrapper>
        <WhiteboardApp isNewWhiteboard={true} whiteboardId={null} />
      </TestWrapper>
    );

    // Wait for any effects to run
    await waitFor(() => {
      expect(mockMutate).toHaveBeenCalledTimes(1);
    }, { timeout: 1000 });

    // Wait a bit more to ensure no additional calls are made
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Verify that createWhiteboard was called exactly once
    expect(mockMutate).toHaveBeenCalledTimes(1);
    
    // Verify the call was made with correct data
    expect(mockMutate).toHaveBeenCalledWith(
      expect.objectContaining({
        title: expect.stringContaining('Whiteboard'),
        content: { nodes: [], edges: [], viewport: { x: 0, y: 0, zoom: 1 } },
        isPublic: false,
        allowComments: true,
        viewMode: 'edit',
        backgroundColor: '#ffffff',
        gridMode: false,
        snapToGrid: false,
        theme: 'light',
      }),
      expect.objectContaining({
        onSuccess: expect.any(Function),
        onError: expect.any(Function),
      })
    );
  });

  it('should not call createWhiteboard when isNewWhiteboard is false', async () => {
    const TestWrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    );

    render(
      <TestWrapper>
        <WhiteboardApp isNewWhiteboard={false} whiteboardId={null} />
      </TestWrapper>
    );

    // Wait for any effects to run
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Verify that createWhiteboard was not called
    expect(mockMutate).not.toHaveBeenCalled();
  });

  it('should not call createWhiteboard when whiteboardId is provided', async () => {
    const TestWrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    );

    render(
      <TestWrapper>
        <WhiteboardApp isNewWhiteboard={true} whiteboardId="existing-id" />
      </TestWrapper>
    );

    // Wait for any effects to run
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Verify that createWhiteboard was not called
    expect(mockMutate).not.toHaveBeenCalled();
  });

  it('should not call createWhiteboard when user is not authenticated', async () => {
    // Mock unauthenticated state
    mockUseAuth.mockReturnValue({
      data: null,
      status: 'unauthenticated',
    });

    const TestWrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    );

    render(
      <TestWrapper>
        <WhiteboardApp isNewWhiteboard={true} whiteboardId={null} />
      </TestWrapper>
    );

    // Wait for any effects to run
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Verify that createWhiteboard was not called
    expect(mockMutate).not.toHaveBeenCalled();
  });
});
