import { describe, it, expect, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ReactFlowWrapper from '@/components/whiteboard/ReactFlowWrapper';
import type { WhiteboardData, WhiteboardSettings } from '@/types';

// Mock React Flow
vi.mock('reactflow', () => ({
  default: vi.fn(({ children, onInit, onNodesChange, onEdgesChange, onConnect }) => {
    // Simulate React Flow initialization
    const mockInstance = {
      screenToFlowPosition: vi.fn(() => ({ x: 100, y: 100 })),
      getViewport: vi.fn(() => ({ x: 0, y: 0, zoom: 1 })),
    };
    
    // Call onInit if provided
    if (onInit) {
      setTimeout(() => onInit(mockInstance), 0);
    }
    
    return (
      <div data-testid="react-flow">
        {children}
        <button 
          data-testid="mock-node-change" 
          onClick={() => onNodesChange && onNodesChange([])}
        >
          Trigger Node Change
        </button>
        <button 
          data-testid="mock-edge-change" 
          onClick={() => onEdgesChange && onEdgesChange([])}
        >
          Trigger Edge Change
        </button>
        <button 
          data-testid="mock-connect" 
          onClick={() => onConnect && onConnect({ source: '1', target: '2' })}
        >
          Trigger Connect
        </button>
      </div>
    );
  }),
  Controls: () => <div data-testid="react-flow-controls">Controls</div>,
  applyNodeChanges: vi.fn((changes, nodes) => nodes),
  applyEdgeChanges: vi.fn((changes, edges) => edges),
  addEdge: vi.fn((connection, edges) => [...edges, { id: 'new-edge', ...connection }]),
}));

vi.mock('@reactflow/minimap', () => ({
  MiniMap: () => <div data-testid="react-flow-minimap">MiniMap</div>,
}));

vi.mock('@reactflow/background', () => ({
  Background: () => <div data-testid="react-flow-background">Background</div>,
}));

vi.mock('next/dynamic', () => {
  return vi.fn((importFn) => {
    const Component = vi.fn((props) => {
      if (typeof importFn === 'function') {
        return importFn().then((mod: { default: React.ComponentType }) => {
          const ActualComponent = mod.default || mod;
          return <ActualComponent {...props} />;
        });
      }
      return <div>Dynamic Component</div>;
    });
    return Component;
  });
});

describe('ReactFlowWrapper', () => {
  const mockOnSave = vi.fn();
  const mockOnExport = vi.fn();
  
  const defaultSettings: WhiteboardSettings = {
    isPublic: false,
    allowComments: true,
    viewMode: 'edit',
    backgroundColor: '#ffffff',
    gridMode: false,
    snapToGrid: false,
    theme: 'light'
  };

  const sampleWhiteboardData: WhiteboardData = {
    id: 'test-whiteboard',
    name: 'Test Whiteboard',
    userId: 'test-user',
    data: JSON.stringify({
      nodes: [
        {
          id: 'node-1',
          type: 'textNode',
          position: { x: 100, y: 100 },
          data: { label: 'Test Node' }
        }
      ],
      edges: [],
      viewport: { x: 0, y: 0, zoom: 1 }
    }),
    settings: defaultSettings,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(
      <ReactFlowWrapper
        settings={defaultSettings}
        onSave={mockOnSave}
      />
    );
    
    expect(screen.getByTestId('react-flow')).toBeInTheDocument();
  });

  it('loads initial whiteboard data correctly', async () => {
    render(
      <ReactFlowWrapper
        whiteboardData={sampleWhiteboardData}
        settings={defaultSettings}
        onSave={mockOnSave}
      />
    );

    await waitFor(() => {
      expect(screen.getByTestId('react-flow')).toBeInTheDocument();
    });
  });

  it('shows node creation toolbar in edit mode', () => {
    render(
      <ReactFlowWrapper
        settings={{ ...defaultSettings, viewMode: 'edit' }}
        onSave={mockOnSave}
      />
    );

    expect(screen.getByText('Text')).toBeInTheDocument();
    expect(screen.getByText('Rectangle')).toBeInTheDocument();
    expect(screen.getByText('Circle')).toBeInTheDocument();
  });

  it('hides node creation toolbar in view mode', () => {
    render(
      <ReactFlowWrapper
        settings={{ ...defaultSettings, viewMode: 'view' }}
        onSave={mockOnSave}
      />
    );

    expect(screen.queryByText('Text')).not.toBeInTheDocument();
    expect(screen.queryByText('Rectangle')).not.toBeInTheDocument();
    expect(screen.queryByText('Circle')).not.toBeInTheDocument();
  });

  it('calls onSave when nodes change', async () => {
    render(
      <ReactFlowWrapper
        settings={defaultSettings}
        onSave={mockOnSave}
      />
    );

    const triggerButton = screen.getByTestId('mock-node-change');
    fireEvent.click(triggerButton);

    // Wait for debounced save
    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalled();
    }, { timeout: 2000 });
  });

  it('handles invalid whiteboard data gracefully', () => {
    const invalidData: WhiteboardData = {
      ...sampleWhiteboardData,
      data: 'invalid json'
    };

    expect(() => {
      render(
        <ReactFlowWrapper
          whiteboardData={invalidData}
          settings={defaultSettings}
          onSave={mockOnSave}
        />
      );
    }).not.toThrow();
  });

  it('applies theme settings correctly', () => {
    const darkSettings: WhiteboardSettings = {
      ...defaultSettings,
      theme: 'dark'
    };

    render(
      <ReactFlowWrapper
        settings={darkSettings}
        onSave={mockOnSave}
      />
    );

    const reactFlow = screen.getByTestId('react-flow');
    expect(reactFlow).toBeInTheDocument();
  });

  it('shows performance metrics in development mode', () => {
    const originalEnv = process.env.NODE_ENV;
    (process.env as NodeJS.ProcessEnv).NODE_ENV = 'development';

    render(
      <ReactFlowWrapper
        settings={defaultSettings}
        onSave={mockOnSave}
      />
    );

    // Performance indicator should be shown after load
    // Note: This test might need adjustment based on actual implementation

    (process.env as NodeJS.ProcessEnv).NODE_ENV = originalEnv;
  });

  it('creates nodes when toolbar buttons are clicked', async () => {
    render(
      <ReactFlowWrapper
        settings={defaultSettings}
        onSave={mockOnSave}
      />
    );

    const textButton = screen.getByText('Text');
    fireEvent.click(textButton);

    // Should trigger save after node creation
    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalled();
    }, { timeout: 2000 });
  });

  it('handles export functionality', () => {
    render(
      <ReactFlowWrapper
        settings={defaultSettings}
        onSave={mockOnSave}
        onExport={mockOnExport}
      />
    );

    // Export functionality would be tested through the parent component
    expect(mockOnExport).not.toHaveBeenCalled();
  });

  it('handles edge connections', async () => {
    render(
      <ReactFlowWrapper
        settings={defaultSettings}
        onSave={mockOnSave}
      />
    );

    const connectButton = screen.getByTestId('mock-connect');
    fireEvent.click(connectButton);

    // Should trigger save after edge creation
    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalled();
    }, { timeout: 2000 });
  });
});
