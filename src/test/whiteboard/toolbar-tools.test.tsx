import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { ReactFlowProvider } from 'reactflow';
import ReactFlowWrapper from '@/components/whiteboard/ReactFlowWrapper';
import type { WhiteboardSettings } from '@/types';

// Mock the dynamic imports
vi.mock('next/dynamic', () => ({
  default: (fn: () => Promise<any>, options?: any) => {
    const Component = vi.fn(() => <div data-testid="mocked-component" />);
    Component.displayName = 'MockedDynamicComponent';
    return Component;
  }
}));

// Mock React Flow components
vi.mock('reactflow', async () => {
  const actual = await vi.importActual('reactflow');
  return {
    ...actual,
    default: vi.fn(({ children, ...props }) => (
      <div data-testid="react-flow" {...props}>
        {children}
      </div>
    )),
    ReactFlowProvider: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="react-flow-provider">{children}</div>
    ),
    useReactFlow: () => ({
      setNodes: vi.fn(),
      setEdges: vi.fn(),
      getViewport: () => ({ x: 0, y: 0, zoom: 1 }),
      fitView: vi.fn(),
      getNodes: () => [],
      getEdges: () => []
    }),
    Handle: ({ children, ...props }: any) => <div data-testid="handle" {...props}>{children}</div>,
    Position: {
      Top: 'top',
      Bottom: 'bottom',
      Left: 'left',
      Right: 'right'
    }
  };
});

// Mock whiteboard utils
vi.mock('@/lib/whiteboard-utils', () => ({
  transformStoredDataToReactFlow: vi.fn(() => ({ nodes: [], edges: [], viewport: { x: 0, y: 0, zoom: 1 } })),
  createDefaultNode: vi.fn((type, position, data) => ({
    id: `${type}-${Date.now()}`,
    type,
    position,
    data
  })),
  validateReactFlowData: vi.fn(() => true),
  exportReactFlowData: vi.fn()
}));

// Mock the node components
vi.mock('@/components/whiteboard/nodes', () => ({
  CountdownTimerNode: vi.fn(() => <div data-testid="countdown-timer-node" />),
  GoalNode: vi.fn(() => <div data-testid="goal-node" />),
  HillChartNode: vi.fn(() => <div data-testid="hill-chart-node" />),
  URLNode: vi.fn(() => <div data-testid="url-node" />),
  TodoListNode: vi.fn(() => <div data-testid="todo-list-node" />)
}));

describe('ReactFlowWrapper Toolbar Tools', () => {
  const defaultSettings: WhiteboardSettings = {
    viewMode: 'edit',
    backgroundColor: '#ffffff',
    gridMode: false,
    snapToGrid: false,
    theme: 'light'
  };

  const mockOnSave = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders all toolbar tools when not in read-only mode', () => {
    render(
      <ReactFlowProvider>
        <ReactFlowWrapper
          settings={defaultSettings}
          onSave={mockOnSave}
          isReadOnly={false}
        />
      </ReactFlowProvider>
    );

    // Check that all tool buttons are present
    expect(screen.getByTitle('Add Text Node')).toBeInTheDocument();
    expect(screen.getByTitle('Add Rectangle Node')).toBeInTheDocument();
    expect(screen.getByTitle('Add Circle Node')).toBeInTheDocument();
    expect(screen.getByTitle('Add Countdown Timer')).toBeInTheDocument();
    expect(screen.getByTitle('Add Goal')).toBeInTheDocument();
    expect(screen.getByTitle('Add Hill Chart')).toBeInTheDocument();
    expect(screen.getByTitle('Add URL Link')).toBeInTheDocument();
    expect(screen.getByTitle('Add Todo List')).toBeInTheDocument();
  });

  it('hides toolbar when in read-only mode', () => {
    render(
      <ReactFlowProvider>
        <ReactFlowWrapper
          settings={defaultSettings}
          onSave={mockOnSave}
          isReadOnly={true}
        />
      </ReactFlowProvider>
    );

    // Check that toolbar is not present
    expect(screen.queryByTitle('Add Text Node')).not.toBeInTheDocument();
    expect(screen.queryByTitle('Add Countdown Timer')).not.toBeInTheDocument();
  });

  it('hides toolbar when in view mode', () => {
    const viewSettings = { ...defaultSettings, viewMode: 'view' as const };
    
    render(
      <ReactFlowProvider>
        <ReactFlowWrapper
          settings={viewSettings}
          onSave={mockOnSave}
          isReadOnly={false}
        />
      </ReactFlowProvider>
    );

    // Check that toolbar is not present
    expect(screen.queryByTitle('Add Text Node')).not.toBeInTheDocument();
    expect(screen.queryByTitle('Add Countdown Timer')).not.toBeInTheDocument();
  });

  it('displays correct button text for all tools', () => {
    render(
      <ReactFlowProvider>
        <ReactFlowWrapper
          settings={defaultSettings}
          onSave={mockOnSave}
          isReadOnly={false}
        />
      </ReactFlowProvider>
    );

    // Check button text
    expect(screen.getByText('Text')).toBeInTheDocument();
    expect(screen.getByText('Rectangle')).toBeInTheDocument();
    expect(screen.getByText('Circle')).toBeInTheDocument();
    expect(screen.getByText('Timer')).toBeInTheDocument();
    expect(screen.getByText('Goal')).toBeInTheDocument();
    expect(screen.getByText('Hill Chart')).toBeInTheDocument();
    expect(screen.getByText('URL')).toBeInTheDocument();
    expect(screen.getByText('Todo')).toBeInTheDocument();
  });

  it('has correct styling classes for each tool button', () => {
    render(
      <ReactFlowProvider>
        <ReactFlowWrapper
          settings={defaultSettings}
          onSave={mockOnSave}
          isReadOnly={false}
        />
      </ReactFlowProvider>
    );

    // Check that buttons have correct color classes
    expect(screen.getByText('Text')).toHaveClass('bg-blue-500');
    expect(screen.getByText('Rectangle')).toHaveClass('bg-green-500');
    expect(screen.getByText('Circle')).toHaveClass('bg-purple-500');
    expect(screen.getByText('Timer')).toHaveClass('bg-orange-500');
    expect(screen.getByText('Goal')).toHaveClass('bg-indigo-500');
    expect(screen.getByText('Hill Chart')).toHaveClass('bg-emerald-500');
    expect(screen.getByText('URL')).toHaveClass('bg-red-500');
    expect(screen.getByText('Todo')).toHaveClass('bg-slate-500');
  });
});
