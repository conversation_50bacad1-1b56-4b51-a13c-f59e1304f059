import { describe, it, expect } from 'vitest';
import {
  UserSchema,
  CreateUserSchema,
  WhiteboardSchema,
  CreateWhiteboardSchema,
  UpdateWhiteboardSchema,
  WhiteboardNodeSchema,
  CreateWhiteboardNodeSchema,
  WhiteboardEdgeSchema,
  CreateWhiteboardEdgeSchema,
  CountdownTimerSchema,
  CreateCountdownTimerSchema,
  GoalSchema,
  CreateGoalSchema,
  HillChartSchema,
  CreateHillChartSchema,
  HillChartItemDbSchema,
  CreateHillChartItemSchema,
  ReactFlowNodeSchema,
  ReactFlowEdgeSchema,
  ReactFlowDataSchema,
  PaginationSchema,
} from '@/lib/schemas';

describe('User Schemas', () => {
  it('should validate a valid user', () => {
    const validUser = {
      id: 'clx1234567890',
      name: '<PERSON>',
      email: '<EMAIL>',
      avatar: 'https://example.com/avatar.jpg',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    expect(() => UserSchema.parse(validUser)).not.toThrow();
  });

  it('should reject invalid email', () => {
    const invalidUser = {
      id: 'clx1234567890',
      name: 'John Doe',
      email: 'invalid-email',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    expect(() => UserSchema.parse(invalidUser)).toThrow();
  });

  it('should validate create user schema', () => {
    const validCreateUser = {
      name: 'Jane Smith',
      email: '<EMAIL>',
    };

    expect(() => CreateUserSchema.parse(validCreateUser)).not.toThrow();
  });

  it('should reject empty name in create user', () => {
    const invalidCreateUser = {
      name: '',
      email: '<EMAIL>',
    };

    expect(() => CreateUserSchema.parse(invalidCreateUser)).toThrow();
  });
});

describe('Whiteboard Schemas', () => {
  it('should validate a valid whiteboard', () => {
    const validWhiteboard = {
      id: 'clx1234567890',
      name: 'Test Whiteboard',
      userId: 'clx0987654321',
      isPublic: false,
      allowComments: true,
      viewMode: 'edit' as const,
      backgroundColor: '#ffffff',
      gridMode: false,
      snapToGrid: false,
      theme: 'light' as const,
      viewportX: 0,
      viewportY: 0,
      viewportZoom: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    expect(() => WhiteboardSchema.parse(validWhiteboard)).not.toThrow();
  });

  it('should reject invalid background color', () => {
    const invalidWhiteboard = {
      id: 'clx1234567890',
      name: 'Test Whiteboard',
      userId: 'clx0987654321',
      backgroundColor: 'invalid-color',
      viewportZoom: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    expect(() => WhiteboardSchema.parse(invalidWhiteboard)).toThrow();
  });

  it('should reject invalid zoom level', () => {
    const invalidWhiteboard = {
      id: 'clx1234567890',
      name: 'Test Whiteboard',
      userId: 'clx0987654321',
      backgroundColor: '#ffffff',
      viewportZoom: 15, // Too high
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    expect(() => WhiteboardSchema.parse(invalidWhiteboard)).toThrow();
  });

  it('should validate create whiteboard schema', () => {
    const validCreateWhiteboard = {
      name: 'New Whiteboard',
      userId: 'clx0987654321',
    };

    expect(() => CreateWhiteboardSchema.parse(validCreateWhiteboard)).not.toThrow();
  });

  it('should validate update whiteboard schema with partial data', () => {
    const validUpdateWhiteboard = {
      name: 'Updated Name',
      backgroundColor: '#f0f0f0',
    };

    expect(() => UpdateWhiteboardSchema.parse(validUpdateWhiteboard)).not.toThrow();
  });
});

describe('Node Schemas', () => {
  it('should validate a valid whiteboard node', () => {
    const validNode = {
      id: 'clx1234567890',
      whiteboardId: 'clx0987654321',
      nodeId: 'node-1',
      type: 'textNode',
      positionX: 100,
      positionY: 200,
      width: 150,
      height: 80,
      data: { label: 'Test Node' },
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    expect(() => WhiteboardNodeSchema.parse(validNode)).not.toThrow();
  });

  it('should validate create node schema', () => {
    const validCreateNode = {
      whiteboardId: 'clx0987654321',
      nodeId: 'node-1',
      type: 'textNode',
      positionX: 100,
      positionY: 200,
      data: { label: 'Test Node' },
    };

    expect(() => CreateWhiteboardNodeSchema.parse(validCreateNode)).not.toThrow();
  });

  it('should reject negative width', () => {
    const invalidNode = {
      whiteboardId: 'clx0987654321',
      nodeId: 'node-1',
      type: 'textNode',
      positionX: 100,
      positionY: 200,
      width: -50, // Invalid
      data: { label: 'Test Node' },
    };

    expect(() => CreateWhiteboardNodeSchema.parse(invalidNode)).toThrow();
  });
});

describe('Edge Schemas', () => {
  it('should validate a valid whiteboard edge', () => {
    const validEdge = {
      id: 'clx1234567890',
      whiteboardId: 'clx0987654321',
      edgeId: 'edge-1',
      source: 'node-1',
      target: 'node-2',
      type: 'smoothstep',
      data: { label: 'connects to' },
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    expect(() => WhiteboardEdgeSchema.parse(validEdge)).not.toThrow();
  });

  it('should validate create edge schema', () => {
    const validCreateEdge = {
      whiteboardId: 'clx0987654321',
      edgeId: 'edge-1',
      source: 'node-1',
      target: 'node-2',
    };

    expect(() => CreateWhiteboardEdgeSchema.parse(validCreateEdge)).not.toThrow();
  });
});

describe('Countdown Timer Schemas', () => {
  it('should validate a valid countdown timer', () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 7);

    const validTimer = {
      id: 'clx1234567890',
      whiteboardId: 'clx0987654321',
      title: 'Sprint End',
      endDate: futureDate,
      positionX: 100,
      positionY: 200,
      isActive: true,
      color: '#ff0000',
      fontSize: 16,
      backgroundColor: '#ffffff',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    expect(() => CountdownTimerSchema.parse(validTimer)).not.toThrow();
  });

  it('should reject empty title', () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 7);

    const invalidTimer = {
      whiteboardId: 'clx0987654321',
      title: '', // Empty title
      endDate: futureDate,
      positionX: 100,
      positionY: 200,
    };

    expect(() => CreateCountdownTimerSchema.parse(invalidTimer)).toThrow();
  });

  it('should reject negative font size', () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 7);

    const invalidTimer = {
      whiteboardId: 'clx0987654321',
      title: 'Test Timer',
      endDate: futureDate,
      positionX: 100,
      positionY: 200,
      fontSize: -5, // Invalid
    };

    expect(() => CreateCountdownTimerSchema.parse(invalidTimer)).toThrow();
  });
});

describe('Goal Schemas', () => {
  it('should validate a valid goal', () => {
    const validGoal = {
      id: 'clx1234567890',
      whiteboardId: 'clx0987654321',
      title: 'Complete Feature',
      description: 'Implement the new feature',
      dueDate: new Date(),
      priority: 'high' as const,
      status: 'in-progress' as const,
      positionX: 100,
      positionY: 200,
      tags: 'feature,development',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    expect(() => GoalSchema.parse(validGoal)).not.toThrow();
  });

  it('should validate create goal schema', () => {
    const validCreateGoal = {
      whiteboardId: 'clx0987654321',
      title: 'New Goal',
      positionX: 100,
      positionY: 200,
    };

    expect(() => CreateGoalSchema.parse(validCreateGoal)).not.toThrow();
  });

  it('should reject invalid priority', () => {
    const invalidGoal = {
      whiteboardId: 'clx0987654321',
      title: 'Test Goal',
      priority: 'invalid' as unknown as string, // Invalid priority
      positionX: 100,
      positionY: 200,
    };

    expect(() => CreateGoalSchema.parse(invalidGoal)).toThrow();
  });
});

describe('Hill Chart Schemas', () => {
  it('should validate a valid hill chart', () => {
    const validHillChart = {
      id: 'clx1234567890',
      whiteboardId: 'clx0987654321',
      title: 'Project Progress',
      positionX: 100,
      positionY: 200,
      width: 400,
      height: 200,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    expect(() => HillChartSchema.parse(validHillChart)).not.toThrow();
  });

  it('should validate hill chart item', () => {
    const validItem = {
      id: 'clx1234567890',
      hillChartId: 'clx0987654321',
      name: 'Feature A',
      position: 75,
      color: '#3b82f6',
      description: 'Feature description',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    expect(() => HillChartItemDbSchema.parse(validItem)).not.toThrow();
  });

  it('should reject invalid position range', () => {
    const invalidItem = {
      hillChartId: 'clx0987654321',
      name: 'Feature A',
      position: 150, // Out of range (0-100)
      color: '#3b82f6',
    };

    expect(() => CreateHillChartItemSchema.parse(invalidItem)).toThrow();
  });

  it('should reject invalid color format', () => {
    const invalidItem = {
      hillChartId: 'clx0987654321',
      name: 'Feature A',
      position: 50,
      color: 'blue', // Invalid hex format
    };

    expect(() => CreateHillChartItemSchema.parse(invalidItem)).toThrow();
  });
});

describe('React Flow Schemas', () => {
  it('should validate React Flow node', () => {
    const validReactFlowNode = {
      id: 'node-1',
      type: 'textNode',
      position: { x: 100, y: 200 },
      data: { label: 'Test Node' },
      width: 150,
      height: 80,
    };

    expect(() => ReactFlowNodeSchema.parse(validReactFlowNode)).not.toThrow();
  });

  it('should validate React Flow edge', () => {
    const validReactFlowEdge = {
      id: 'edge-1',
      source: 'node-1',
      target: 'node-2',
      type: 'smoothstep',
      animated: true,
    };

    expect(() => ReactFlowEdgeSchema.parse(validReactFlowEdge)).not.toThrow();
  });

  it('should validate React Flow data', () => {
    const validReactFlowData = {
      nodes: [
        {
          id: 'node-1',
          position: { x: 100, y: 200 },
          data: { label: 'Node 1' },
        },
      ],
      edges: [
        {
          id: 'edge-1',
          source: 'node-1',
          target: 'node-2',
        },
      ],
      viewport: { x: 0, y: 0, zoom: 1 },
    };

    expect(() => ReactFlowDataSchema.parse(validReactFlowData)).not.toThrow();
  });
});

describe('Pagination Schema', () => {
  it('should validate valid pagination', () => {
    const validPagination = {
      page: 1,
      limit: 10,
    };

    expect(() => PaginationSchema.parse(validPagination)).not.toThrow();
  });

  it('should use default values', () => {
    const result = PaginationSchema.parse({});
    expect(result.page).toBe(1);
    expect(result.limit).toBe(10);
  });

  it('should reject invalid page number', () => {
    const invalidPagination = {
      page: 0, // Must be positive
      limit: 10,
    };

    expect(() => PaginationSchema.parse(invalidPagination)).toThrow();
  });

  it('should reject limit too high', () => {
    const invalidPagination = {
      page: 1,
      limit: 200, // Max is 100
    };

    expect(() => PaginationSchema.parse(invalidPagination)).toThrow();
  });
});
