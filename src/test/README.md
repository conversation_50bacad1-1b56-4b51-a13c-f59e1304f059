# Testing Setup

This directory contains comprehensive tests for the Anchorboard Application, with a focus on authentication functionality and real database connectivity.

## Test Framework

We use **Vitest** as our test framework, which provides:
- Fast execution with native ES modules support
- Built-in TypeScript support
- Jest-compatible API
- Excellent developer experience with hot reload

## Test Structure

### Database Tests (`database.test.ts`)
- Tests real database connectivity
- Verifies database schema and constraints
- Tests CRUD operations on auth tables
- Validates foreign key relationships

### Authentication Tests (`auth.test.ts`)
- Tests user registration process
- Tests user authentication flow
- Tests session management
- Tests database operations for auth

### Component Tests (`auth-components.test.tsx`)
- Tests React components (SignIn, SignUp pages)
- Tests user interactions and form validation
- Tests error handling and loading states
- Uses React Testing Library for DOM testing

### Integration Tests (`auth-integration.test.ts`)
- Tests complete authentication flows
- Tests edge cases and error scenarios
- Tests concurrent operations
- Tests data consistency

## Database Configuration

The tests use a real PostgreSQL database (Supabase) to ensure:
- Authentic database behavior
- Proper constraint validation
- Real-world performance characteristics
- Integration with better-auth

### Database Connection
- **Primary**: Supabase PostgreSQL
- **Connection**: `****************************************************************************/postgres`
- **SSL**: Required with `rejectUnauthorized: false`

## Running Tests

### All Tests
```bash
bun test
```

### Watch Mode
```bash
bun test --watch
```

### UI Mode
```bash
bun test:ui
```

### Specific Test Files
```bash
# Database connectivity tests
bun test database.test.ts

# Authentication logic tests
bun test auth.test.ts

# Component tests
bun test auth-components.test.tsx

# Integration tests
bun test auth-integration.test.ts
```

### Coverage
```bash
bun test:coverage
```

## Test Data Management

### Test Users
The tests use predefined test users:
- `validUser`: For testing successful operations
- `existingUser`: For testing login scenarios
- `invalidUser`: For testing validation

### Cleanup
- Tests automatically clean up test data after execution
- Each test runs in isolation with fresh data
- Database state is reset between test suites

## Environment Variables

Test-specific environment variables are defined in `.env.test`:
- `TEST_DATABASE_URL`: Database connection for tests
- `BETTER_AUTH_SECRET`: Test-only auth secret
- `BETTER_AUTH_URL`: Test application URL

## Mocking Strategy

### External Dependencies
- Next.js router is mocked for component tests
- better-auth client is mocked for unit tests
- Real database connections are used for integration tests

### Why Real Database?
We use real database connections because:
1. **Authenticity**: Tests real database behavior and constraints
2. **Integration**: Validates better-auth database integration
3. **Performance**: Tests actual query performance
4. **Reliability**: Catches database-specific issues

## Test Best Practices

### Database Tests
- Always clean up test data
- Use transactions where possible
- Test both success and failure scenarios
- Validate constraints and relationships

### Component Tests
- Test user interactions, not implementation details
- Use semantic queries (getByRole, getByLabelText)
- Test accessibility and error states
- Mock external dependencies appropriately

### Integration Tests
- Test complete user workflows
- Include edge cases and error scenarios
- Test concurrent operations
- Validate data consistency across operations

## Troubleshooting

### Database Connection Issues
1. Verify database URL in `.env.test`
2. Check network connectivity
3. Ensure database tables exist (run setup script)
4. Verify SSL configuration

### Test Failures
1. Check test isolation (data cleanup)
2. Verify mock configurations
3. Check environment variables
4. Review database constraints

### Performance Issues
1. Use database transactions for faster cleanup
2. Limit concurrent test execution
3. Optimize database queries in tests
4. Consider test data seeding strategies

## Contributing

When adding new tests:
1. Follow existing naming conventions
2. Include both positive and negative test cases
3. Clean up test data properly
4. Document complex test scenarios
5. Update this README if adding new test categories
