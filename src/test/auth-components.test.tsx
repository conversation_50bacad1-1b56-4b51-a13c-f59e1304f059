import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Theme } from '@radix-ui/themes'
import SignIn from '@/app/auth/signin/page'
import SignUp from '@/app/auth/signup/page'

// Mock the auth client
const mockSignIn = vi.fn()
const mockSignUp = vi.fn()
const mockPush = vi.fn()

vi.mock('@/lib/auth-client', () => ({
  signIn: {
    email: mockSignIn,
  },
  signUp: {
    email: mockSignUp,
  },
}))

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

// Wrapper component for Radix UI Theme
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <Theme>{children}</Theme>
)

describe('Authentication Components', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('SignIn Component', () => {
    it('should render sign in form', () => {
      render(
        <TestWrapper>
          <SignIn />
        </TestWrapper>
      )

      expect(screen.getByText('Sign In to Whiteboard')).toBeInTheDocument()
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /try demo account/i })).toBeInTheDocument()
    })

    it('should show demo credentials', () => {
      render(
        <TestWrapper>
          <SignIn />
        </TestWrapper>
      )

      expect(screen.getByText(/<EMAIL> \/ demo123/)).toBeInTheDocument()
    })

    it('should handle successful sign in', async () => {
      const user = userEvent.setup()
      mockSignIn.mockResolvedValue({ error: null })

      render(
        <TestWrapper>
          <SignIn />
        </TestWrapper>
      )

      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/password/i), 'password123')
      await user.click(screen.getByRole('button', { name: /sign in/i }))

      await waitFor(() => {
        expect(mockSignIn).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        })
        expect(mockPush).toHaveBeenCalledWith('/')
      })
    })

    it('should handle sign in error', async () => {
      const user = userEvent.setup()
      mockSignIn.mockResolvedValue({ error: { message: 'Invalid credentials' } })

      render(
        <TestWrapper>
          <SignIn />
        </TestWrapper>
      )

      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/password/i), 'wrongpassword')
      await user.click(screen.getByRole('button', { name: /sign in/i }))

      await waitFor(() => {
        expect(screen.getByText('Invalid credentials')).toBeInTheDocument()
      })
    })

    it('should handle demo login', async () => {
      const user = userEvent.setup()
      mockSignIn.mockResolvedValue({ error: null })

      render(
        <TestWrapper>
          <SignIn />
        </TestWrapper>
      )

      await user.click(screen.getByRole('button', { name: /try demo account/i }))

      await waitFor(() => {
        expect(mockSignIn).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'demo123',
        })
        expect(mockPush).toHaveBeenCalledWith('/')
      })
    })

    it('should navigate to sign up page', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <SignIn />
        </TestWrapper>
      )

      await user.click(screen.getByText(/sign up here/i))

      expect(mockPush).toHaveBeenCalledWith('/auth/signup')
    })

    it('should disable form during loading', async () => {
      const user = userEvent.setup()
      mockSignIn.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)))

      render(
        <TestWrapper>
          <SignIn />
        </TestWrapper>
      )

      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/password/i), 'password123')
      
      const signInButton = screen.getByRole('button', { name: /sign in/i })
      await user.click(signInButton)

      expect(signInButton).toBeDisabled()
      expect(screen.getByRole('button', { name: /try demo account/i })).toBeDisabled()
    })
  })

  describe('SignUp Component', () => {
    it('should render sign up form', () => {
      render(
        <TestWrapper>
          <SignUp />
        </TestWrapper>
      )

      expect(screen.getByText('Sign Up for Whiteboard')).toBeInTheDocument()
      expect(screen.getByLabelText(/name/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/^password/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument()
    })

    it('should handle successful sign up', async () => {
      const user = userEvent.setup()
      mockSignUp.mockResolvedValue({ error: null })

      render(
        <TestWrapper>
          <SignUp />
        </TestWrapper>
      )

      await user.type(screen.getByLabelText(/name/i), 'Test User')
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/^password/i), 'password123')
      await user.type(screen.getByLabelText(/confirm password/i), 'password123')
      await user.click(screen.getByRole('button', { name: /create account/i }))

      await waitFor(() => {
        expect(mockSignUp).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
          name: 'Test User',
        })
        expect(screen.getByText(/account created successfully/i)).toBeInTheDocument()
      })

      // Should redirect after success
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/auth/signin')
      }, { timeout: 3000 })
    })

    it('should validate password confirmation', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <SignUp />
        </TestWrapper>
      )

      await user.type(screen.getByLabelText(/name/i), 'Test User')
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/^password/i), 'password123')
      await user.type(screen.getByLabelText(/confirm password/i), 'differentpassword')
      await user.click(screen.getByRole('button', { name: /create account/i }))

      await waitFor(() => {
        expect(screen.getByText('Passwords do not match')).toBeInTheDocument()
      })

      expect(mockSignUp).not.toHaveBeenCalled()
    })

    it('should validate required fields', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <SignUp />
        </TestWrapper>
      )

      await user.click(screen.getByRole('button', { name: /create account/i }))

      await waitFor(() => {
        expect(screen.getByText('Please fill in all fields')).toBeInTheDocument()
      })

      expect(mockSignUp).not.toHaveBeenCalled()
    })

    it('should handle sign up error', async () => {
      const user = userEvent.setup()
      mockSignUp.mockResolvedValue({ 
        error: { message: 'Email already exists' } 
      })

      render(
        <TestWrapper>
          <SignUp />
        </TestWrapper>
      )

      await user.type(screen.getByLabelText(/name/i), 'Test User')
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/^password/i), 'password123')
      await user.type(screen.getByLabelText(/confirm password/i), 'password123')
      await user.click(screen.getByRole('button', { name: /create account/i }))

      await waitFor(() => {
        expect(screen.getByText('Email already exists')).toBeInTheDocument()
      })
    })

    it('should navigate to sign in page', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <SignUp />
        </TestWrapper>
      )

      await user.click(screen.getByText(/sign in here/i))

      expect(mockPush).toHaveBeenCalledWith('/auth/signin')
    })

    it('should disable form during loading', async () => {
      const user = userEvent.setup()
      mockSignUp.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)))

      render(
        <TestWrapper>
          <SignUp />
        </TestWrapper>
      )

      await user.type(screen.getByLabelText(/name/i), 'Test User')
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/^password/i), 'password123')
      await user.type(screen.getByLabelText(/confirm password/i), 'password123')
      
      const createButton = screen.getByRole('button', { name: /create account/i })
      await user.click(createButton)

      expect(createButton).toBeDisabled()
    })
  })
})
