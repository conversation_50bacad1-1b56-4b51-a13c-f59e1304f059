import { Pool } from 'pg'

// Database connection for testing
export const createTestDbConnection = () => {
  return new Pool({
    connectionString: process.env.TEST_DATABASE_URL || '****************************************************************************/postgres',
    ssl: { rejectUnauthorized: false },
  })
}

// Test user data
export const testUsers = {
  validUser: {
    email: '<EMAIL>',
    password: 'testpassword123',
    name: 'Test User',
  },
  existingUser: {
    email: '<EMAIL>',
    password: 'existingpassword123',
    name: 'Existing User',
  },
  invalidUser: {
    email: 'invalid-email',
    password: '123',
    name: '',
  },
}

// Database cleanup utilities
export const cleanupTestData = async (pool: Pool) => {
  try {
    // Clean up test users
    await pool.query(`DELETE FROM "account" WHERE "accountId" IN ($1, $2)`, [
      testUsers.validUser.email,
      testUsers.existingUser.email,
    ])
    await pool.query(`DELETE FROM "user" WHERE "email" IN ($1, $2)`, [
      testUsers.validUser.email,
      testUsers.existingUser.email,
    ])
  } catch (error) {
    console.warn('Cleanup warning:', error)
  }
}

// Setup test user in database
export const setupTestUser = async (pool: Pool, user: typeof testUsers.validUser) => {
  try {
    // Insert user
    const userResult = await pool.query(
      `INSERT INTO "user" (id, email, "emailVerified", name) 
       VALUES (gen_random_uuid(), $1, true, $2) 
       RETURNING id`,
      [user.email, user.name]
    )
    
    const userId = userResult.rows[0].id
    
    // Insert account with password
    await pool.query(
      `INSERT INTO "account" (id, "userId", "accountId", "providerId", password) 
       VALUES (gen_random_uuid(), $1, $2, 'credential', $3)`,
      [userId, user.email, user.password]
    )
    
    return userId
  } catch (error) {
    console.error('Setup test user error:', error)
    throw error
  }
}
