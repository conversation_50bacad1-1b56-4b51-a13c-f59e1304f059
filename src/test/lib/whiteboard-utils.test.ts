import { describe, it, expect } from 'vitest';
import {
  transformReactFlowDataForStorage,
  transformStoredDataToReactFlow,
  createDefaultNode,
  validateReactFlowData,
  migrateExcalidrawToReactFlow,
  exportReactFlowData
} from '@/lib/whiteboard-utils';
import type { Node, Edge, Viewport } from 'reactflow';

describe('whiteboard-utils', () => {
  const sampleNodes: Node[] = [
    {
      id: 'node-1',
      type: 'textNode',
      position: { x: 100, y: 100 },
      data: { label: 'Test Node', color: '#000000' }
    },
    {
      id: 'node-2',
      type: 'rectangleNode',
      position: { x: 200, y: 200 },
      data: { label: 'Rectangle', backgroundColor: '#f0f0f0' }
    }
  ];

  const sampleEdges: Edge[] = [
    {
      id: 'edge-1',
      source: 'node-1',
      target: 'node-2',
      type: 'default'
    }
  ];

  const sampleViewport: Viewport = { x: 0, y: 0, zoom: 1 };

  describe('transformReactFlowDataForStorage', () => {
    it('should transform React Flow data to storage format', () => {
      const result = transformReactFlowDataForStorage(
        sampleNodes,
        sampleEdges,
        sampleViewport,
        { countdownTimers: [], goals: [] }
      );

      const parsed = JSON.parse(result);
      expect(parsed.nodes).toEqual(sampleNodes);
      expect(parsed.edges).toEqual(sampleEdges);
      expect(parsed.viewport).toEqual(sampleViewport);
      expect(parsed.countdownTimers).toEqual([]);
      expect(parsed.goals).toEqual([]);
    });

    it('should handle empty additional data', () => {
      const result = transformReactFlowDataForStorage(
        sampleNodes,
        sampleEdges,
        sampleViewport
      );

      const parsed = JSON.parse(result);
      expect(parsed.nodes).toEqual(sampleNodes);
      expect(parsed.edges).toEqual(sampleEdges);
      expect(parsed.viewport).toEqual(sampleViewport);
    });
  });

  describe('transformStoredDataToReactFlow', () => {
    it('should transform stored data back to React Flow format', () => {
      const storedData = JSON.stringify({
        nodes: sampleNodes,
        edges: sampleEdges,
        viewport: sampleViewport,
        countdownTimers: [{ id: 'timer-1', title: 'Test Timer' }],
        goals: [{ id: 'goal-1', title: 'Test Goal' }]
      });

      const result = transformStoredDataToReactFlow(storedData);

      expect(result.nodes).toEqual(sampleNodes);
      expect(result.edges).toEqual(sampleEdges);
      expect(result.viewport).toEqual(sampleViewport);
      expect(result.additionalData.countdownTimers).toHaveLength(1);
      expect(result.additionalData.goals).toHaveLength(1);
    });

    it('should handle invalid JSON gracefully', () => {
      const result = transformStoredDataToReactFlow('invalid json');

      expect(result.nodes).toEqual([]);
      expect(result.edges).toEqual([]);
      expect(result.viewport).toEqual({ x: 0, y: 0, zoom: 1 });
      expect(result.additionalData.countdownTimers).toEqual([]);
    });

    it('should provide defaults for missing data', () => {
      const storedData = JSON.stringify({});
      const result = transformStoredDataToReactFlow(storedData);

      expect(result.nodes).toEqual([]);
      expect(result.edges).toEqual([]);
      expect(result.viewport).toEqual({ x: 0, y: 0, zoom: 1 });
    });
  });

  describe('createDefaultNode', () => {
    it('should create a text node with default properties', () => {
      const node = createDefaultNode('textNode', { x: 50, y: 50 }, 'Test Label');

      expect(node.type).toBe('textNode');
      expect(node.position).toEqual({ x: 50, y: 50 });
      expect(node.data.label).toBe('Test Label');
      expect(node.data.backgroundColor).toBe('#ffffff');
      expect(node.id).toMatch(/^textNode-/);
    });

    it('should create a circle node with appropriate defaults', () => {
      const node = createDefaultNode('circleNode', { x: 100, y: 100 }, 'Circle');

      expect(node.type).toBe('circleNode');
      expect(node.data.backgroundColor).toBe('#dbeafe');
      expect(node.data.width).toBe(100);
      expect(node.data.height).toBe(100);
    });

    it('should create a rectangle node with appropriate defaults', () => {
      const node = createDefaultNode('rectangleNode', { x: 150, y: 150 }, 'Rectangle');

      expect(node.type).toBe('rectangleNode');
      expect(node.data.backgroundColor).toBe('#f3f4f6');
      expect(node.data.width).toBe(150);
      expect(node.data.height).toBe(80);
    });

    it('should merge custom data with defaults', () => {
      const customData = { color: '#ff0000', fontSize: 18 };
      const node = createDefaultNode('textNode', { x: 0, y: 0 }, 'Custom', customData);

      expect(node.data.color).toBe('#ff0000');
      expect(node.data.fontSize).toBe(18);
      expect(node.data.label).toBe('Custom');
    });
  });

  describe('validateReactFlowData', () => {
    it('should validate correct React Flow data', () => {
      const validData = {
        nodes: sampleNodes,
        edges: sampleEdges,
        viewport: sampleViewport
      };

      expect(validateReactFlowData(validData)).toBe(true);
    });

    it('should reject data with missing nodes array', () => {
      const invalidData = {
        edges: sampleEdges,
        viewport: sampleViewport
      };

      expect(validateReactFlowData(invalidData)).toBe(false);
    });

    it('should reject data with invalid viewport', () => {
      const invalidData = {
        nodes: sampleNodes,
        edges: sampleEdges,
        viewport: { x: 0, y: 0 } // missing zoom
      };

      expect(validateReactFlowData(invalidData)).toBe(false);
    });

    it('should reject nodes with missing required properties', () => {
      const invalidData = {
        nodes: [{ id: 'node-1' }], // missing position
        edges: [],
        viewport: sampleViewport
      };

      expect(validateReactFlowData(invalidData)).toBe(false);
    });

    it('should reject edges with missing required properties', () => {
      const invalidData = {
        nodes: sampleNodes,
        edges: [{ id: 'edge-1', source: 'node-1' }], // missing target
        viewport: sampleViewport
      };

      expect(validateReactFlowData(invalidData)).toBe(false);
    });
  });

  describe('migrateExcalidrawToReactFlow', () => {
    it('should return existing React Flow data unchanged', () => {
      const reactFlowData = {
        nodes: sampleNodes,
        edges: sampleEdges,
        viewport: sampleViewport
      };

      const result = migrateExcalidrawToReactFlow(JSON.stringify(reactFlowData));

      expect(result).toEqual(reactFlowData);
    });

    it('should migrate Excalidraw elements to React Flow nodes', () => {
      const excalidrawData = {
        elements: [
          {
            id: 'element-1',
            type: 'rectangle',
            x: 100,
            y: 100,
            width: 150,
            height: 80,
            text: 'Rectangle Element',
            strokeColor: '#000000',
            backgroundColor: '#ffffff'
          },
          {
            id: 'element-2',
            type: 'ellipse',
            x: 200,
            y: 200,
            width: 100,
            height: 100,
            text: 'Circle Element'
          }
        ]
      };

      const result = migrateExcalidrawToReactFlow(JSON.stringify(excalidrawData));

      expect(result.nodes).toHaveLength(2);
      expect(result.nodes[0].type).toBe('rectangleNode');
      expect(result.nodes[1].type).toBe('circleNode');
      expect(result.edges).toEqual([]);
      expect(result.viewport).toEqual({ x: 0, y: 0, zoom: 1 });
    });

    it('should handle invalid Excalidraw data gracefully', () => {
      const result = migrateExcalidrawToReactFlow('invalid json');

      expect(result.nodes).toEqual([]);
      expect(result.edges).toEqual([]);
      expect(result.viewport).toEqual({ x: 0, y: 0, zoom: 1 });
    });
  });

  describe('exportReactFlowData', () => {
    it('should export as JSON by default', () => {
      const result = exportReactFlowData(sampleNodes, sampleEdges, sampleViewport);
      const parsed = JSON.parse(result);

      expect(parsed.nodes).toEqual(sampleNodes);
      expect(parsed.edges).toEqual(sampleEdges);
      expect(parsed.viewport).toEqual(sampleViewport);
    });

    it('should export as CSV format', () => {
      const result = exportReactFlowData(sampleNodes, sampleEdges, sampleViewport, 'csv');

      expect(result).toContain('id,type,label,x,y,width,height');
      expect(result).toContain('node-1,textNode,Test Node,100,100');
      expect(result).toContain('node-2,rectangleNode,Rectangle,200,200');
    });
  });
});
