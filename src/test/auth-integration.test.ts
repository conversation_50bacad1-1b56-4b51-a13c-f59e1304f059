import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import type { Pool } from 'pg'
import { createTestDbConnection, cleanupTestData, testUsers } from './utils'

describe('Authentication Integration Tests', () => {
  let pool: Pool

  beforeAll(async () => {
    pool = createTestDbConnection()
  })

  afterAll(async () => {
    if (pool) {
      await cleanupTestData(pool)
      await pool.end()
    }
  })

  beforeEach(async () => {
    await cleanupTestData(pool)
  })

  describe('Complete User Registration Flow', () => {
    it('should complete full user registration process', async () => {
      const { email, password, name } = testUsers.validUser

      // Step 1: Create user record
      const userResult = await pool.query(
        `INSERT INTO "user" (id, email, "emailVerified", name) 
         VALUES (gen_random_uuid(), $1, false, $2) 
         RETURNING id, email, name, "emailVerified", "createdAt"`,
        [email, name]
      )

      const user = userResult.rows[0]
      expect(user.email).toBe(email)
      expect(user.name).toBe(name)
      expect(user.emailVerified).toBe(false)
      expect(user.createdAt).toBeInstanceOf(Date)

      // Step 2: Create account with credentials
      const accountResult = await pool.query(
        `INSERT INTO "account" (id, "userId", "accountId", "providerId", password) 
         VALUES (gen_random_uuid(), $1, $2, 'credential', $3) 
         RETURNING id, "accountId", "providerId"`,
        [user.id, email, password]
      )

      const account = accountResult.rows[0]
      expect(account.accountId).toBe(email)
      expect(account.providerId).toBe('credential')

      // Step 3: Verify user can be found with credentials
      const authResult = await pool.query(`
        SELECT u.id, u.email, u.name, u."emailVerified", a.password
        FROM "user" u
        JOIN "account" a ON u.id = a."userId"
        WHERE u.email = $1 AND a."providerId" = 'credential'
      `, [email])

      expect(authResult.rows).toHaveLength(1)
      const authUser = authResult.rows[0]
      expect(authUser.email).toBe(email)
      expect(authUser.password).toBe(password)
    })

    it('should handle email verification process', async () => {
      const { email, name } = testUsers.validUser

      // Create unverified user
      const userResult = await pool.query(
        `INSERT INTO "user" (id, email, "emailVerified", name) 
         VALUES (gen_random_uuid(), $1, false, $2) 
         RETURNING id`,
        [email, name]
      )
      const userId = userResult.rows[0].id

      // Create verification record
      const verificationToken = 'verification-token-' + Date.now()
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours

      await pool.query(
        `INSERT INTO "verification" (id, identifier, value, "expiresAt") 
         VALUES (gen_random_uuid(), $1, $2, $3)`,
        [email, verificationToken, expiresAt]
      )

      // Verify token exists
      const verificationResult = await pool.query(
        `SELECT identifier, value FROM "verification" 
         WHERE identifier = $1 AND value = $2 AND "expiresAt" > NOW()`,
        [email, verificationToken]
      )

      expect(verificationResult.rows).toHaveLength(1)

      // Simulate email verification
      await pool.query(
        `UPDATE "user" SET "emailVerified" = true WHERE id = $1`,
        [userId]
      )

      // Clean up verification record
      await pool.query(
        `DELETE FROM "verification" WHERE identifier = $1 AND value = $2`,
        [email, verificationToken]
      )

      // Verify user is now verified
      const verifiedUserResult = await pool.query(
        `SELECT "emailVerified" FROM "user" WHERE id = $1`,
        [userId]
      )

      expect(verifiedUserResult.rows[0].emailVerified).toBe(true)
    })
  })

  describe('Complete User Login Flow', () => {
    let userId: string

    beforeEach(async () => {
      // Setup verified user for login tests
      const { email, password, name } = testUsers.existingUser

      const userResult = await pool.query(
        `INSERT INTO "user" (id, email, "emailVerified", name) 
         VALUES (gen_random_uuid(), $1, true, $2) 
         RETURNING id`,
        [email, name]
      )
      userId = userResult.rows[0].id

      await pool.query(
        `INSERT INTO "account" (id, "userId", "accountId", "providerId", password) 
         VALUES (gen_random_uuid(), $1, $2, 'credential', $3)`,
        [userId, email, password]
      )
    })

    it('should complete full login and session creation process', async () => {
      const { email, password } = testUsers.existingUser

      // Step 1: Authenticate user
      const authResult = await pool.query(`
        SELECT u.id, u.email, u.name, u."emailVerified"
        FROM "user" u
        JOIN "account" a ON u.id = a."userId"
        WHERE u.email = $1 AND a.password = $2 AND a."providerId" = 'credential'
      `, [email, password])

      expect(authResult.rows).toHaveLength(1)
      const user = authResult.rows[0]
      expect(user.emailVerified).toBe(true)

      // Step 2: Create session
      const sessionToken = 'session-token-' + Date.now()
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days

      const sessionResult = await pool.query(
        `INSERT INTO "session" (id, "userId", "expiresAt", token) 
         VALUES (gen_random_uuid(), $1, $2, $3) 
         RETURNING id, token, "expiresAt"`,
        [user.id, expiresAt, sessionToken]
      )

      const session = sessionResult.rows[0]
      expect(session.token).toBe(sessionToken)
      expect(session.expiresAt).toBeInstanceOf(Date)

      // Step 3: Verify session can retrieve user
      const sessionUserResult = await pool.query(`
        SELECT u.id, u.email, u.name, s.token
        FROM "user" u
        JOIN "session" s ON u.id = s."userId"
        WHERE s.token = $1 AND s."expiresAt" > NOW()
      `, [sessionToken])

      expect(sessionUserResult.rows).toHaveLength(1)
      expect(sessionUserResult.rows[0].email).toBe(email)
      expect(sessionUserResult.rows[0].token).toBe(sessionToken)
    })

    it('should handle session expiration', async () => {
      const sessionToken = 'expired-session-' + Date.now()
      const expiresAt = new Date(Date.now() - 1000) // Expired

      // Create expired session
      await pool.query(
        `INSERT INTO "session" (id, "userId", "expiresAt", token) 
         VALUES (gen_random_uuid(), $1, $2, $3)`,
        [userId, expiresAt, sessionToken]
      )

      // Try to use expired session
      const sessionResult = await pool.query(`
        SELECT u.id, u.email
        FROM "user" u
        JOIN "session" s ON u.id = s."userId"
        WHERE s.token = $1 AND s."expiresAt" > NOW()
      `, [sessionToken])

      expect(sessionResult.rows).toHaveLength(0)
    })

    it('should handle logout by removing session', async () => {
      const sessionToken = 'logout-session-' + Date.now()
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)

      // Create session
      await pool.query(
        `INSERT INTO "session" (id, "userId", "expiresAt", token) 
         VALUES (gen_random_uuid(), $1, $2, $3)`,
        [userId, expiresAt, sessionToken]
      )

      // Verify session exists
      let sessionCheck = await pool.query(
        `SELECT id FROM "session" WHERE token = $1`,
        [sessionToken]
      )
      expect(sessionCheck.rows).toHaveLength(1)

      // Simulate logout by deleting session
      await pool.query(
        `DELETE FROM "session" WHERE token = $1`,
        [sessionToken]
      )

      // Verify session is gone
      sessionCheck = await pool.query(
        `SELECT id FROM "session" WHERE token = $1`,
        [sessionToken]
      )
      expect(sessionCheck.rows).toHaveLength(0)
    })
  })

  describe('Error Handling and Edge Cases', () => {
    it('should handle concurrent registration attempts', async () => {
      const { email, name } = testUsers.validUser

      // Simulate concurrent registration attempts
      const promises = [
        pool.query(
          `INSERT INTO "user" (id, email, "emailVerified", name)
           VALUES (gen_random_uuid(), $1, false, $2)`,
          [email, name + ' 1']
        ),
        pool.query(
          `INSERT INTO "user" (id, email, "emailVerified", name)
           VALUES (gen_random_uuid(), $1, false, $2)`,
          [email, name + ' 2']
        ),
      ]

      // One should succeed, one should fail
      const results = await Promise.allSettled(promises)
      const successes = results.filter(r => r.status === 'fulfilled')
      const failures = results.filter(r => r.status === 'rejected')

      expect(successes).toHaveLength(1)
      expect(failures).toHaveLength(1)
    })

    it('should handle orphaned sessions cleanup', async () => {
      const { email, password, name } = testUsers.validUser

      // Create user and session
      const userResult = await pool.query(
        `INSERT INTO "user" (id, email, "emailVerified", name)
         VALUES (gen_random_uuid(), $1, true, $2)
         RETURNING id`,
        [email, name]
      )
      const userId = userResult.rows[0].id

      const sessionToken = 'orphan-session-' + Date.now()
      await pool.query(
        `INSERT INTO "session" (id, "userId", "expiresAt", token)
         VALUES (gen_random_uuid(), $1, $2, $3)`,
        [userId, new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), sessionToken]
      )

      // Delete user (should cascade to sessions due to foreign key)
      await pool.query(`DELETE FROM "user" WHERE id = $1`, [userId])

      // Verify session is also deleted
      const sessionResult = await pool.query(
        `SELECT id FROM "session" WHERE token = $1`,
        [sessionToken]
      )
      expect(sessionResult.rows).toHaveLength(0)
    })
  })
})
