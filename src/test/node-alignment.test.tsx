import { describe, it, expect } from 'vitest';
import { render } from '@testing-library/react';
import { ReactFlowProvider } from 'reactflow';
import { CountdownTimerNode, GoalNode, TodoListNode } from '@/components/whiteboard/nodes';
import type { CountdownTimerNodeData, GoalNodeData, TodoListNodeData } from '@/types';

// Mock data for testing
const mockCountdownData: CountdownTimerNodeData = {
  title: 'Test Timer',
  endDate: new Date('2024-12-31'),
  style: {
    color: '#000000',
    fontSize: 16,
    backgroundColor: '#ffffff'
  },
  isActive: false
};

const mockGoalData: GoalNodeData = {
  title: 'Test Goal',
  description: 'Test description',
  priority: 'medium',
  status: 'not-started',
  tags: ['test']
};

const mockTodoData: TodoListNodeData = {
  title: 'Test Todo',
  items: [],
  backgroundColor: '#ffffff',
  textColor: '#000000'
};

describe('Node Alignment Consistency', () => {
  it('should render CountdownTimerNode with consistent styling', () => {
    const { container } = render(
      <ReactFlowProvider>
        <CountdownTimerNode
          id="test-countdown"
          data={mockCountdownData}
          selected={false}
        />
      </ReactFlowProvider>
    );

    const card = container.querySelector('.transition-all');
    expect(card).toBeTruthy();
    expect(card).toHaveClass('duration-200');
    expect(card).toHaveClass('shadow-md');
  });

  it('should render GoalNode with consistent styling', () => {
    const { container } = render(
      <ReactFlowProvider>
        <GoalNode
          id="test-goal"
          data={mockGoalData}
          selected={false}
        />
      </ReactFlowProvider>
    );

    const card = container.querySelector('.transition-all');
    expect(card).toBeTruthy();
    expect(card).toHaveClass('duration-200');
    expect(card).toHaveClass('shadow-md');
  });

  it('should render TodoListNode with consistent styling', () => {
    const { container } = render(
      <ReactFlowProvider>
        <TodoListNode
          id="test-todo"
          data={mockTodoData}
          selected={false}
        />
      </ReactFlowProvider>
    );

    const card = container.querySelector('.transition-all');
    expect(card).toBeTruthy();
    expect(card).toHaveClass('duration-200');
    expect(card).toHaveClass('shadow-md');
  });

  it('should apply selected styling consistently', () => {
    const { container } = render(
      <ReactFlowProvider>
        <CountdownTimerNode
          id="test-countdown-selected"
          data={mockCountdownData}
          selected={true}
        />
      </ReactFlowProvider>
    );

    const card = container.querySelector('.transition-all');
    expect(card).toBeTruthy();
    expect(card).toHaveClass('ring-2');
    expect(card).toHaveClass('ring-blue-500');
    expect(card).toHaveClass('shadow-lg');
  });
});
