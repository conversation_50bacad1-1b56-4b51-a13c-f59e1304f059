import { describe, it, expect, beforeEach } from 'vitest';
import { TRPCError } from '@trpc/server';
import { appRouter } from '@/server/routers/_app';
import { createTRPCContext } from '@/server/trpc';
import { testPrisma, createTestUser, createTestWhiteboard, createTestNode, createTestEdge } from '../setup';

// Create a test context
const createTestContext = () => {
  return createTRPCContext({
    req: {} as any,
    res: {} as any,
  });
};

// Create a test caller
const createTestCaller = () => {
  const ctx = createTestContext();
  return appRouter.createCaller(ctx);
};

describe('Whiteboard tRPC Router', () => {
  let caller: ReturnType<typeof createTestCaller>;
  let testUser: any;

  beforeEach(async () => {
    caller = createTestCaller();
    testUser = await createTestUser();
  });

  describe('list', () => {
    it('should return empty list when no whiteboards exist', async () => {
      const result = await caller.whiteboard.list({
        page: 1,
        limit: 10,
      });

      expect(result.whiteboards).toHaveLength(0);
      expect(result.total).toBe(0);
      expect(result.totalPages).toBe(0);
    });

    it('should return whiteboards with pagination', async () => {
      // Create test whiteboards
      await createTestWhiteboard(testUser.id, { name: 'Whiteboard 1' });
      await createTestWhiteboard(testUser.id, { name: 'Whiteboard 2' });
      await createTestWhiteboard(testUser.id, { name: 'Whiteboard 3' });

      const result = await caller.whiteboard.list({
        page: 1,
        limit: 2,
      });

      expect(result.whiteboards).toHaveLength(2);
      expect(result.total).toBe(3);
      expect(result.totalPages).toBe(2);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(2);
    });

    it('should filter by userId', async () => {
      const otherUser = await createTestUser({ email: '<EMAIL>' });
      
      await createTestWhiteboard(testUser.id, { name: 'User 1 Board' });
      await createTestWhiteboard(otherUser.id, { name: 'User 2 Board' });

      const result = await caller.whiteboard.list({
        userId: testUser.id,
        page: 1,
        limit: 10,
      });

      expect(result.whiteboards).toHaveLength(1);
      expect(result.whiteboards[0].name).toBe('User 1 Board');
    });
  });

  describe('getById', () => {
    it('should return whiteboard by id', async () => {
      const whiteboard = await createTestWhiteboard(testUser.id, {
        name: 'Test Whiteboard',
      });

      const result = await caller.whiteboard.getById({ id: whiteboard.id });

      expect(result).toBeTruthy();
      expect(result!.name).toBe('Test Whiteboard');
      expect(result!.userId).toBe(testUser.id);
    });

    it('should return null for non-existent whiteboard', async () => {
      const result = await caller.whiteboard.getById({ id: 'non-existent-id' });
      expect(result).toBeNull();
    });
  });

  describe('create', () => {
    it('should create a new whiteboard', async () => {
      const whiteboardData = {
        name: 'New Whiteboard',
        userId: testUser.id,
        isPublic: true,
        theme: 'dark' as const,
        backgroundColor: '#1a1a1a',
      };

      const result = await caller.whiteboard.create(whiteboardData);

      expect(result.name).toBe('New Whiteboard');
      expect(result.userId).toBe(testUser.id);
      expect(result.isPublic).toBe(true);
      expect(result.theme).toBe('dark');
      expect(result.backgroundColor).toBe('#1a1a1a');
    });

    it('should use default values for optional fields', async () => {
      const whiteboardData = {
        name: 'Simple Whiteboard',
        userId: testUser.id,
      };

      const result = await caller.whiteboard.create(whiteboardData);

      expect(result.isPublic).toBe(false);
      expect(result.allowComments).toBe(true);
      expect(result.viewMode).toBe('edit');
      expect(result.backgroundColor).toBe('#ffffff');
      expect(result.gridMode).toBe(false);
      expect(result.snapToGrid).toBe(false);
      expect(result.theme).toBe('light');
      expect(result.viewportX).toBe(0);
      expect(result.viewportY).toBe(0);
      expect(result.viewportZoom).toBe(1);
    });
  });

  describe('update', () => {
    it('should update whiteboard', async () => {
      const whiteboard = await createTestWhiteboard(testUser.id);

      const updateData = {
        name: 'Updated Whiteboard',
        backgroundColor: '#f0f0f0',
        isPublic: true,
      };

      const result = await caller.whiteboard.update({
        id: whiteboard.id,
        data: updateData,
      });

      expect(result.name).toBe('Updated Whiteboard');
      expect(result.backgroundColor).toBe('#f0f0f0');
      expect(result.isPublic).toBe(true);
    });

    it('should throw error for non-existent whiteboard', async () => {
      await expect(
        caller.whiteboard.update({
          id: 'non-existent-id',
          data: { name: 'Updated' },
        })
      ).rejects.toThrow(TRPCError);
    });
  });

  describe('delete', () => {
    it('should delete whiteboard', async () => {
      const whiteboard = await createTestWhiteboard(testUser.id);

      const result = await caller.whiteboard.delete({ id: whiteboard.id });
      expect(result.success).toBe(true);

      // Verify deletion
      const deletedWhiteboard = await testPrisma.whiteboard.findUnique({
        where: { id: whiteboard.id },
      });
      expect(deletedWhiteboard).toBeNull();
    });

    it('should throw error for non-existent whiteboard', async () => {
      await expect(
        caller.whiteboard.delete({ id: 'non-existent-id' })
      ).rejects.toThrow(TRPCError);
    });
  });

  describe('updateViewport', () => {
    it('should update viewport settings', async () => {
      const whiteboard = await createTestWhiteboard(testUser.id);

      const result = await caller.whiteboard.updateViewport({
        id: whiteboard.id,
        viewportX: 100,
        viewportY: 200,
        viewportZoom: 1.5,
      });

      expect(result.viewportX).toBe(100);
      expect(result.viewportY).toBe(200);
      expect(result.viewportZoom).toBe(1.5);
    });

    it('should reject invalid zoom level', async () => {
      const whiteboard = await createTestWhiteboard(testUser.id);

      await expect(
        caller.whiteboard.updateViewport({
          id: whiteboard.id,
          viewportX: 0,
          viewportY: 0,
          viewportZoom: 15, // Too high
        })
      ).rejects.toThrow();
    });
  });

  describe('getWithData', () => {
    it('should return whiteboard with all related data', async () => {
      const whiteboard = await createTestWhiteboard(testUser.id);
      
      // Create related data
      await createTestNode(whiteboard.id, { nodeId: 'node-1' });
      await createTestNode(whiteboard.id, { nodeId: 'node-2' });
      await createTestEdge(whiteboard.id, 'node-1', 'node-2');

      const result = await caller.whiteboard.getWithData({ id: whiteboard.id });

      expect(result).toBeTruthy();
      expect(result.nodes).toHaveLength(2);
      expect(result.edges).toHaveLength(1);
      expect(result.countdownTimers).toHaveLength(0);
      expect(result.goals).toHaveLength(0);
      expect(result.hillCharts).toHaveLength(0);
    });

    it('should throw error for non-existent whiteboard', async () => {
      await expect(
        caller.whiteboard.getWithData({ id: 'non-existent-id' })
      ).rejects.toThrow(TRPCError);
    });
  });

  describe('bulkUpdate', () => {
    it('should update nodes, edges, and viewport in transaction', async () => {
      const whiteboard = await createTestWhiteboard(testUser.id);

      const updateData = {
        id: whiteboard.id,
        nodes: [
          {
            nodeId: 'node-1',
            type: 'textNode',
            positionX: 100,
            positionY: 200,
            data: { label: 'Node 1' },
          },
          {
            nodeId: 'node-2',
            type: 'rectangleNode',
            positionX: 300,
            positionY: 400,
            data: { label: 'Node 2' },
          },
        ],
        edges: [
          {
            edgeId: 'edge-1',
            source: 'node-1',
            target: 'node-2',
            type: 'smoothstep',
          },
        ],
        viewport: {
          x: 50,
          y: 100,
          zoom: 1.2,
        },
      };

      const result = await caller.whiteboard.bulkUpdate(updateData);
      expect(result.success).toBe(true);

      // Verify the data was saved
      const updatedWhiteboard = await testPrisma.whiteboard.findUnique({
        where: { id: whiteboard.id },
        include: {
          nodes: true,
          edges: true,
        },
      });

      expect(updatedWhiteboard!.viewportX).toBe(50);
      expect(updatedWhiteboard!.viewportY).toBe(100);
      expect(updatedWhiteboard!.viewportZoom).toBe(1.2);
      expect(updatedWhiteboard!.nodes).toHaveLength(2);
      expect(updatedWhiteboard!.edges).toHaveLength(1);
    });

    it('should replace existing nodes and edges', async () => {
      const whiteboard = await createTestWhiteboard(testUser.id);
      
      // Create initial data
      await createTestNode(whiteboard.id, { nodeId: 'old-node' });
      await createTestEdge(whiteboard.id, 'old-node', 'old-node');

      const updateData = {
        id: whiteboard.id,
        nodes: [
          {
            nodeId: 'new-node',
            type: 'textNode',
            positionX: 100,
            positionY: 200,
            data: { label: 'New Node' },
          },
        ],
        edges: [],
        viewport: { x: 0, y: 0, zoom: 1 },
      };

      await caller.whiteboard.bulkUpdate(updateData);

      // Verify old data was replaced
      const updatedWhiteboard = await testPrisma.whiteboard.findUnique({
        where: { id: whiteboard.id },
        include: {
          nodes: true,
          edges: true,
        },
      });

      expect(updatedWhiteboard!.nodes).toHaveLength(1);
      expect(updatedWhiteboard!.nodes[0].nodeId).toBe('new-node');
      expect(updatedWhiteboard!.edges).toHaveLength(0);
    });
  });

  describe('search', () => {
    it('should search whiteboards by name', async () => {
      await createTestWhiteboard(testUser.id, { name: 'Product Planning' });
      await createTestWhiteboard(testUser.id, { name: 'Team Retrospective' });
      await createTestWhiteboard(testUser.id, { name: 'Design Workshop' });

      const result = await caller.whiteboard.search({
        query: 'product',
        page: 1,
        limit: 10,
      });

      expect(result.whiteboards).toHaveLength(1);
      expect(result.whiteboards[0].name).toBe('Product Planning');
    });

    it('should be case insensitive', async () => {
      await createTestWhiteboard(testUser.id, { name: 'Product Planning' });

      const result = await caller.whiteboard.search({
        query: 'PRODUCT',
        page: 1,
        limit: 10,
      });

      expect(result.whiteboards).toHaveLength(1);
    });

    it('should filter by userId in search', async () => {
      const otherUser = await createTestUser({ email: '<EMAIL>' });
      
      await createTestWhiteboard(testUser.id, { name: 'My Product Board' });
      await createTestWhiteboard(otherUser.id, { name: 'Other Product Board' });

      const result = await caller.whiteboard.search({
        query: 'product',
        userId: testUser.id,
        page: 1,
        limit: 10,
      });

      expect(result.whiteboards).toHaveLength(1);
      expect(result.whiteboards[0].name).toBe('My Product Board');
    });
  });
});
