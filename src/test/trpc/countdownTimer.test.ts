import { describe, it, expect, beforeEach } from 'vitest';
import { TRPCError } from '@trpc/server';
import { appRouter } from '@/server/routers/_app';
import { createTRPCContext } from '@/server/trpc';
import { testPrisma, createTestUser, createTestWhiteboard, createTestCountdownTimer } from '../setup';
import type { NextApiRequest, NextApiResponse } from 'next';

// Create a test context
const createTestContext = () => {
  return createTRPCContext({
    req: {} as NextApiRequest,
    res: {} as NextApiResponse,
  });
};

// Create a test caller
const createTestCaller = () => {
  const ctx = createTestContext();
  return appRouter.createCaller(ctx);
};

describe('CountdownTimer tRPC Router', () => {
  let caller: ReturnType<typeof createTestCaller>;
  let testUser: any;
  let testWhiteboard: any;

  beforeEach(async () => {
    caller = createTestCaller();
    testUser = await createTestUser();
    testWhiteboard = await createTestWhiteboard(testUser.id);
  });

  describe('getByWhiteboardId', () => {
    it('should return empty array when no timers exist', async () => {
      const result = await caller.countdownTimer.getByWhiteboardId({
        whiteboardId: testWhiteboard.id,
      });

      expect(result).toHaveLength(0);
    });

    it('should return timers for whiteboard', async () => {
      await createTestCountdownTimer(testWhiteboard.id, { title: 'Timer 1' });
      await createTestCountdownTimer(testWhiteboard.id, { title: 'Timer 2' });

      const result = await caller.countdownTimer.getByWhiteboardId({
        whiteboardId: testWhiteboard.id,
      });

      expect(result).toHaveLength(2);
      expect(result.map(t => t.title)).toContain('Timer 1');
      expect(result.map(t => t.title)).toContain('Timer 2');
    });

    it('should order by creation date descending', async () => {
      const timer1 = await createTestCountdownTimer(testWhiteboard.id, { title: 'First Timer' });
      // Wait a bit to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10));
      const timer2 = await createTestCountdownTimer(testWhiteboard.id, { title: 'Second Timer' });

      const result = await caller.countdownTimer.getByWhiteboardId({
        whiteboardId: testWhiteboard.id,
      });

      expect(result[0].title).toBe('Second Timer');
      expect(result[1].title).toBe('First Timer');
    });
  });

  describe('getById', () => {
    it('should return timer by id', async () => {
      const timer = await createTestCountdownTimer(testWhiteboard.id, {
        title: 'Test Timer',
        color: '#ff0000',
      });

      const result = await caller.countdownTimer.getById({ id: timer.id });

      expect(result).toBeTruthy();
      expect(result!.title).toBe('Test Timer');
      expect(result!.color).toBe('#ff0000');
    });

    it('should return null for non-existent timer', async () => {
      const result = await caller.countdownTimer.getById({ id: 'non-existent-id' });
      expect(result).toBeNull();
    });
  });

  describe('create', () => {
    it('should create a new countdown timer', async () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 7);

      const timerData = {
        whiteboardId: testWhiteboard.id,
        title: 'Sprint End',
        endDate: futureDate,
        positionX: 100,
        positionY: 200,
        isActive: true,
        color: '#ff0000',
        fontSize: 18,
        backgroundColor: '#ffffff',
      };

      const result = await caller.countdownTimer.create(timerData);

      expect(result.title).toBe('Sprint End');
      expect(result.whiteboardId).toBe(testWhiteboard.id);
      expect(result.positionX).toBe(100);
      expect(result.positionY).toBe(200);
      expect(result.isActive).toBe(true);
      expect(result.color).toBe('#ff0000');
      expect(result.fontSize).toBe(18);
    });

    it('should use default values for optional fields', async () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 7);

      const timerData = {
        whiteboardId: testWhiteboard.id,
        title: 'Simple Timer',
        endDate: futureDate,
        positionX: 100,
        positionY: 200,
      };

      const result = await caller.countdownTimer.create(timerData);

      expect(result.isActive).toBe(true);
      expect(result.color).toBe('#000000');
      expect(result.fontSize).toBe(16);
    });

    it('should throw error for non-existent whiteboard', async () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 7);

      const timerData = {
        whiteboardId: 'non-existent-id',
        title: 'Test Timer',
        endDate: futureDate,
        positionX: 100,
        positionY: 200,
      };

      await expect(caller.countdownTimer.create(timerData)).rejects.toThrow(TRPCError);
    });
  });

  describe('update', () => {
    it('should update countdown timer', async () => {
      const timer = await createTestCountdownTimer(testWhiteboard.id);
      const newDate = new Date();
      newDate.setDate(newDate.getDate() + 14);

      const updateData = {
        title: 'Updated Timer',
        endDate: newDate,
        color: '#00ff00',
        fontSize: 20,
      };

      const result = await caller.countdownTimer.update({
        id: timer.id,
        data: updateData,
      });

      expect(result.title).toBe('Updated Timer');
      expect(result.color).toBe('#00ff00');
      expect(result.fontSize).toBe(20);
    });

    it('should throw error for non-existent timer', async () => {
      await expect(
        caller.countdownTimer.update({
          id: 'non-existent-id',
          data: { title: 'Updated' },
        })
      ).rejects.toThrow(TRPCError);
    });
  });

  describe('delete', () => {
    it('should delete countdown timer', async () => {
      const timer = await createTestCountdownTimer(testWhiteboard.id);

      const result = await caller.countdownTimer.delete({ id: timer.id });
      expect(result.success).toBe(true);

      // Verify deletion
      const deletedTimer = await testPrisma.countdownTimer.findUnique({
        where: { id: timer.id },
      });
      expect(deletedTimer).toBeNull();
    });

    it('should throw error for non-existent timer', async () => {
      await expect(
        caller.countdownTimer.delete({ id: 'non-existent-id' })
      ).rejects.toThrow(TRPCError);
    });
  });

  describe('updatePosition', () => {
    it('should update timer position', async () => {
      const timer = await createTestCountdownTimer(testWhiteboard.id);

      const result = await caller.countdownTimer.updatePosition({
        id: timer.id,
        positionX: 300,
        positionY: 400,
      });

      expect(result.positionX).toBe(300);
      expect(result.positionY).toBe(400);
    });
  });

  describe('toggleActive', () => {
    it('should toggle timer active state', async () => {
      const timer = await createTestCountdownTimer(testWhiteboard.id, { isActive: true });

      const result = await caller.countdownTimer.toggleActive({ id: timer.id });
      expect(result.isActive).toBe(false);

      // Toggle again
      const result2 = await caller.countdownTimer.toggleActive({ id: timer.id });
      expect(result2.isActive).toBe(true);
    });

    it('should throw error for non-existent timer', async () => {
      await expect(
        caller.countdownTimer.toggleActive({ id: 'non-existent-id' })
      ).rejects.toThrow(TRPCError);
    });
  });

  describe('getActiveByWhiteboardId', () => {
    it('should return only active timers', async () => {
      await createTestCountdownTimer(testWhiteboard.id, { 
        title: 'Active Timer',
        isActive: true 
      });
      await createTestCountdownTimer(testWhiteboard.id, { 
        title: 'Inactive Timer',
        isActive: false 
      });

      const result = await caller.countdownTimer.getActiveByWhiteboardId({
        whiteboardId: testWhiteboard.id,
      });

      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('Active Timer');
      expect(result[0].isActive).toBe(true);
    });

    it('should order by end date ascending', async () => {
      const nearDate = new Date();
      nearDate.setDate(nearDate.getDate() + 1);
      
      const farDate = new Date();
      farDate.setDate(farDate.getDate() + 7);

      await createTestCountdownTimer(testWhiteboard.id, { 
        title: 'Far Timer',
        endDate: farDate,
        isActive: true 
      });
      await createTestCountdownTimer(testWhiteboard.id, { 
        title: 'Near Timer',
        endDate: nearDate,
        isActive: true 
      });

      const result = await caller.countdownTimer.getActiveByWhiteboardId({
        whiteboardId: testWhiteboard.id,
      });

      expect(result[0].title).toBe('Near Timer');
      expect(result[1].title).toBe('Far Timer');
    });
  });

  describe('bulkCreate', () => {
    it('should create multiple timers', async () => {
      const futureDate1 = new Date();
      futureDate1.setDate(futureDate1.getDate() + 7);
      
      const futureDate2 = new Date();
      futureDate2.setDate(futureDate2.getDate() + 14);

      const timersData = [
        {
          title: 'Timer 1',
          endDate: futureDate1,
          positionX: 100,
          positionY: 100,
        },
        {
          title: 'Timer 2',
          endDate: futureDate2,
          positionX: 200,
          positionY: 200,
        },
      ];

      const result = await caller.countdownTimer.bulkCreate({
        whiteboardId: testWhiteboard.id,
        timers: timersData,
      });

      expect(result).toHaveLength(2);
      expect(result.map(t => t.title)).toContain('Timer 1');
      expect(result.map(t => t.title)).toContain('Timer 2');
    });

    it('should throw error for non-existent whiteboard', async () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 7);

      const timersData = [
        {
          title: 'Timer 1',
          endDate: futureDate,
          positionX: 100,
          positionY: 100,
        },
      ];

      await expect(
        caller.countdownTimer.bulkCreate({
          whiteboardId: 'non-existent-id',
          timers: timersData,
        })
      ).rejects.toThrow(TRPCError);
    });
  });

  describe('deleteByWhiteboardId', () => {
    it('should delete all timers for whiteboard', async () => {
      await createTestCountdownTimer(testWhiteboard.id, { title: 'Timer 1' });
      await createTestCountdownTimer(testWhiteboard.id, { title: 'Timer 2' });
      await createTestCountdownTimer(testWhiteboard.id, { title: 'Timer 3' });

      const result = await caller.countdownTimer.deleteByWhiteboardId({
        whiteboardId: testWhiteboard.id,
      });

      expect(result.count).toBe(3);

      // Verify deletion
      const remainingTimers = await testPrisma.countdownTimer.findMany({
        where: { whiteboardId: testWhiteboard.id },
      });
      expect(remainingTimers).toHaveLength(0);
    });

    it('should return 0 count when no timers exist', async () => {
      const result = await caller.countdownTimer.deleteByWhiteboardId({
        whiteboardId: testWhiteboard.id,
      });

      expect(result.count).toBe(0);
    });
  });
});
