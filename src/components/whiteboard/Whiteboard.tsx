'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import { Flex, Button, IconButton, Dialog, Text } from '@radix-ui/themes';
import { Settings, Timer, Target, TrendingUp, Wand2, Save, Download, Cloud, CloudOff, WifiOff, Wifi, Link2, CheckSquare } from 'lucide-react';
import type { Node, Edge, Viewport } from 'reactflow';
import { useDebouncedCallback } from 'use-debounce';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';

import ReactFlowWrapper from './ReactFlowWrapper';
import WhiteboardSettings from './WhiteboardSettings';

import type {
  WhiteboardData,
  WhiteboardSettings as WhiteboardSettingsType,
  CountdownTimer as CountdownTimerType,
  Goal,
  HillChart as HillChartType
} from '@/types';
import {
  transformStoredDataToReactFlow,
  createCountdownTimerNode,
  createGoalNode,
  createHillChartNode,
  createUrlNode,
  createTodoListNode
} from '@/lib/whiteboard-utils';
import { useAutoSaveWhiteboard, useUpdateWhiteboard } from '@/hooks/useWhiteboardData';

interface WhiteboardProps {
  initialData?: WhiteboardData;
  whiteboardId?: string;
  isReadOnly?: boolean;
}

export default function Whiteboard({
  initialData,
  whiteboardId,
  isReadOnly = false
}: WhiteboardProps) {
  // Auto-save hooks
  const { autoSave, isAutoSaving } = useAutoSaveWhiteboard();
  const updateWhiteboard = useUpdateWhiteboard();
  const { isOnline, isSlowConnection } = useNetworkStatus();

  // Whiteboard state - updated for React Flow
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const [viewport, setViewport] = useState<Viewport>({ x: 0, y: 0, zoom: 1 });
  const [settings, setSettings] = useState<WhiteboardSettingsType>({
    isPublic: initialData?.isPublic ?? false,
    allowComments: initialData?.allowComments ?? true,
    viewMode: isReadOnly ? 'view' : (initialData?.viewMode as 'edit' | 'view' ?? 'edit'),
    backgroundColor: initialData?.backgroundColor ?? '#ffffff',
    gridMode: initialData?.gridMode ?? false,
    snapToGrid: initialData?.snapToGrid ?? false,
    theme: initialData?.theme as 'light' | 'dark' ?? 'light'
  });

  // Track if data has changed for auto-save
  const hasChangesRef = useRef(false);
  const lastSaveRef = useRef<Date>(new Date());

  // UI states
  const [showSettings, setShowSettings] = useState(false);

  // Update settings with auto-save
  const handleSettingsChange = useCallback((newSettings: WhiteboardSettingsType) => {
    if (isReadOnly) return;
    
    setSettings(newSettings);
    
    // Auto-save settings changes
    if (whiteboardId && isOnline) {
      autoSave({
        id: whiteboardId,
        isPublic: newSettings.isPublic,
        allowComments: newSettings.allowComments,
        viewMode: newSettings.viewMode,
        backgroundColor: newSettings.backgroundColor,
        gridMode: newSettings.gridMode,
        snapToGrid: newSettings.snapToGrid,
        theme: newSettings.theme,
      });
    }
  }, [whiteboardId, isOnline, autoSave, isReadOnly]);

  // Load initial data
  useEffect(() => {
    if (initialData?.content) {
      const { nodes: loadedNodes, edges: loadedEdges, viewport: loadedViewport } =
        transformStoredDataToReactFlow(JSON.stringify(initialData.content));

      // Load React Flow data
      setNodes(loadedNodes);
      setEdges(loadedEdges);
      setViewport(loadedViewport);

      // Update viewport from database if available
      if (initialData.viewportX !== undefined && initialData.viewportY !== undefined && initialData.viewportZoom !== undefined) {
        setViewport({
          x: initialData.viewportX,
          y: initialData.viewportY,
          zoom: initialData.viewportZoom
        });
      }
    }
  }, [initialData]);



  // Debounced auto-save function
  const debouncedAutoSave = useDebouncedCallback(
    (whiteboardData: { nodes: Node[]; edges: Edge[]; viewport: Viewport }) => {
      if (whiteboardId && hasChangesRef.current && isOnline) {
        autoSave({
          id: whiteboardId,
          content: whiteboardData,
          viewportX: whiteboardData.viewport.x,
          viewportY: whiteboardData.viewport.y,
          viewportZoom: whiteboardData.viewport.zoom,
        });
        hasChangesRef.current = false;
        lastSaveRef.current = new Date();
      }
    },
    isSlowConnection ? 5000 : 2000 // Longer delay for slow connections
  );

  // Handle React Flow changes with auto-save
  const handleReactFlowChange = useCallback((
    newNodes: Node[],
    newEdges: Edge[],
    newViewport: Viewport
  ) => {
    setNodes(newNodes);
    setEdges(newEdges);
    setViewport(newViewport);

    // Mark as changed and trigger auto-save
    hasChangesRef.current = true;
    const whiteboardData = {
      nodes: newNodes,
      edges: newEdges,
      viewport: newViewport,
    };
    debouncedAutoSave(whiteboardData);
  }, [debouncedAutoSave]);

  // Manual save function
  const handleSave = useCallback(() => {
    if (!whiteboardId) return;

    const whiteboardData = {
      nodes,
      edges,
      viewport,
    };

    updateWhiteboard.mutate({
      id: whiteboardId,
      content: whiteboardData,
      viewportX: viewport.x,
      viewportY: viewport.y,
      viewportZoom: viewport.zoom,
    });

    hasChangesRef.current = false;
    lastSaveRef.current = new Date();
  }, [nodes, edges, viewport, whiteboardId, updateWhiteboard]);

  // Export functionality
  const handleExport = useCallback((format: 'png' | 'svg' | 'json' = 'png') => {
    console.log(`Exporting whiteboard as ${format}...`);
    // The actual export is handled by the ReactFlowWrapper component
  }, []);

  // Add countdown timer - create node directly with default values
  const handleAddCountdownTimer = () => {
    console.log('handleAddCountdownTimer called');
    const defaultTimer: Omit<CountdownTimerType, 'id' | 'whiteboardId'> = {
      title: 'New Timer',
      endDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      position: { x: 0, y: 0 }, // Will be overridden by node position
      style: {
        color: '#000000',
        fontSize: 16,
        backgroundColor: '#ffffff'
      },
      isActive: false
    };

    const position = {
      x: 100 + Math.random() * 200,
      y: 100 + Math.random() * 200
    };

    const newNode = createCountdownTimerNode(defaultTimer, position);
    console.log('Created countdown timer node:', newNode);
    setNodes(prev => {
      const newNodes = [...prev, newNode];
      console.log('Updated nodes state:', newNodes);
      return newNodes;
    });
  };

  // Add goal - create node directly with default values
  const handleAddGoal = () => {
    console.log('handleAddGoal called');
    const defaultGoal: Omit<Goal, 'id' | 'whiteboardId'> = {
      title: 'New Goal',
      description: '',
      priority: 'medium',
      status: 'not-started',
      position: { x: 0, y: 0 }, // Will be overridden by node position
      tags: []
    };

    const position = {
      x: 100 + Math.random() * 200,
      y: 100 + Math.random() * 200
    };

    const newNode = createGoalNode(defaultGoal, position);
    console.log('Created goal node:', newNode);
    setNodes(prev => {
      const newNodes = [...prev, newNode];
      console.log('Updated nodes state:', newNodes);
      return newNodes;
    });
  };

  // Add hill chart - create node directly with default values
  const handleAddHillChart = () => {
    console.log('handleAddHillChart called');
    const defaultHillChart: Omit<HillChartType, 'id' | 'whiteboardId'> = {
      title: 'New Hill Chart',
      items: [
        {
          id: `item-${Date.now()}-1`,
          name: 'Sample Item',
          position: 25,
          color: '#3b82f6',
          description: ''
        }
      ],
      position: { x: 0, y: 0 }, // Will be overridden by node position
      width: 400,
      height: 200
    };

    const position = {
      x: 100 + Math.random() * 200,
      y: 100 + Math.random() * 200
    };

    const newNode = createHillChartNode(defaultHillChart, position);
    console.log('Created hill chart node:', newNode);
    setNodes(prev => {
      const newNodes = [...prev, newNode];
      console.log('Updated nodes state:', newNodes);
      return newNodes;
    });
  };

  // Add URL shortcut - create node directly with default values
  const handleAddUrl = () => {
    console.log('handleAddUrl called');
    const defaultUrl = {
      title: 'New Link',
      url: 'example.com',
      description: 'Click to navigate to this website'
    };

    const position = {
      x: 100 + Math.random() * 200,
      y: 100 + Math.random() * 200
    };

    const newNode = createUrlNode(defaultUrl, position);
    console.log('Created URL node:', newNode);
    setNodes(prev => {
      const newNodes = [...prev, newNode];
      console.log('Updated nodes state:', newNodes);
      return newNodes;
    });
  };

  // Add todo list - create node directly with default values
  const handleAddTodoList = () => {
    console.log('handleAddTodoList called');
    const defaultTodoList = {
      title: 'New Todo List',
      items: [],
      backgroundColor: '#ffffff',
      textColor: '#1f2937',
      maxItems: 20
    };

    const position = {
      x: 100 + Math.random() * 200,
      y: 100 + Math.random() * 200
    };

    const newNode = createTodoListNode(defaultTodoList, position);
    console.log('Created todo list node:', newNode);
    setNodes(prev => {
      const newNodes = [...prev, newNode];
      console.log('Updated nodes state:', newNodes);
      return newNodes;
    });
  };


  return (
    <div className={`relative w-full h-screen flex flex-col ${settings.theme === 'dark' ? 'dark' : ''}`}>
      {/* Toolbar */}
      {!isReadOnly && (
        <div className={`flex items-center justify-between p-4 border-b z-10 shadow-sm ${
          settings.theme === 'dark' 
            ? 'bg-gray-900 border-gray-700 text-gray-100' 
            : 'bg-white border-gray-200 text-gray-900'
        }`}>
          <Flex gap="2" align="center">
            <Button
              size="2"
              variant="soft"
              onClick={handleAddCountdownTimer}
            >
              <Timer size={16} />
              Timer
            </Button>

            <Button
              size="2"
              variant="soft"
              onClick={handleAddGoal}
            >
              <Target size={16} />
              Goal
            </Button>

            <Button
              size="2"
              variant="soft"
              onClick={handleAddHillChart}
            >
              <TrendingUp size={16} />
              Hill Chart
            </Button>

            <Button
              size="2"
              variant="soft"
              onClick={handleAddUrl}
            >
              <Link2 size={16} />
              Link
            </Button>

            <Button
              size="2"
              variant="soft"
              onClick={handleAddTodoList}
            >
              <CheckSquare size={16} />
              Todo List
            </Button>

            <Button size="2" variant="soft" disabled>
              <Wand2 size={16} />
              AI Assistant
            </Button>
          </Flex>

          <Flex gap="2" align="center">
            {/* Network status indicator */}
            {!isOnline && (
              <Flex gap="1" align="center">
                <WifiOff size={14} className="text-red-500" />
                <Text size="1" color="red">Offline</Text>
              </Flex>
            )}

            {isOnline && isSlowConnection && (
              <Flex gap="1" align="center">
                <Wifi size={14} className="text-orange-500" />
                <Text size="1" color="orange">Slow</Text>
              </Flex>
            )}

            {/* Save status indicator */}
            <Flex gap="1" align="center">
              {!isOnline ? (
                <>
                  <CloudOff size={14} className="text-red-500" />
                  <Text size="1" color="red">Offline</Text>
                </>
              ) : isAutoSaving ? (
                <>
                  <Cloud size={14} className="animate-pulse" />
                  <Text size="1" color="gray">Saving...</Text>
                </>
              ) : hasChangesRef.current ? (
                <>
                  <CloudOff size={14} />
                  <Text size="1" color="orange">Unsaved</Text>
                </>
              ) : (
                <>
                  <Cloud size={14} />
                  <Text size="1" color="green">Saved</Text>
                </>
              )}
            </Flex>

            <Button
              size="2"
              variant="soft"
              onClick={handleSave}
              disabled={updateWhiteboard.isPending || !isOnline}
            >
              <Save size={16} />
              {!isOnline ? 'Offline' : updateWhiteboard.isPending ? 'Saving...' : 'Save'}
            </Button>

            <Button size="2" variant="soft" onClick={() => handleExport('png')}>
              <Download size={16} />
              Export
            </Button>

            <IconButton
              size="2"
              variant="soft"
              onClick={() => setShowSettings(true)}
            >
              <Settings size={16} />
            </IconButton>
          </Flex>
        </div>
      )}

      {/* Main whiteboard area */}
      <div className="flex-1 relative">
        <ReactFlowWrapper
          whiteboardData={initialData}
          nodes={nodes}
          edges={edges}
          viewport={viewport}
          onSave={handleReactFlowChange}
          onExport={handleExport}
          settings={settings}
          isReadOnly={isReadOnly}
          className="w-full h-full"
        />

        {/* Custom elements are now handled as React Flow nodes */}
      </div>

      {/* Modals and Dialogs */}
      {showSettings && (
        <Dialog.Root open={showSettings} onOpenChange={setShowSettings}>
          <Dialog.Content>
            <WhiteboardSettings
              settings={settings}
              onSettingsChange={handleSettingsChange}
              onClose={() => setShowSettings(false)}
            />
          </Dialog.Content>
        </Dialog.Root>
      )}


    </div>
  );
}
