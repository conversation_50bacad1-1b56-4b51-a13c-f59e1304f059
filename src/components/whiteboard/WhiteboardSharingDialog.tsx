'use client';

import React, { useState } from 'react';
import { WhiteboardRole } from '@/types';
import { 
  Dialog, 
  Button, 
  TextField, 
  Select, 
  Tabs, 
  Box, 
  Text, 
  Flex,
  Badge,
  IconButton,
  Card,
  Separator
} from '@radix-ui/themes';
import { 
  Share2Icon, 
  UserPlusIcon, 
  LinkIcon, 
  TrashIcon, 
  CopyIcon,
  MailIcon,
  ClockIcon,
  UsersIcon
} from 'lucide-react';
import { toast } from 'sonner';
import { 
  useWhiteboardAccess, 
  useWhiteboardSharing, 
  useWhiteboardInvitations 
} from '@/hooks/useWhiteboardAccess';
import { getRoleColor } from '@/lib/rbac'; // Import the new utility function

interface WhiteboardSharingDialogProps {
  whiteboardId: string;
  isOpen: boolean;
  onClose: () => void;
}

export default function WhiteboardSharingDialog({
  whiteboardId,
  isOpen,
  onClose,
}: WhiteboardSharingDialogProps) {
  const [activeTab, setActiveTab] = useState('collaborators');
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<WhiteboardRole>(WhiteboardRole.VIEWER);
  const [linkRole, setLinkRole] = useState<WhiteboardRole>(WhiteboardRole.VIEWER);
  const [linkExpiry, setLinkExpiry] = useState<number>(7);

  const { accessInfo, grantAccess, revokeAccess, isLoadingAccess } = useWhiteboardAccess(whiteboardId);
  const { shareLinks, createShareLink, deactivateShareLink, isLoadingLinks } = useWhiteboardSharing(whiteboardId);
  const { invitations, inviteUser, isInvitingUser } = useWhiteboardInvitations(whiteboardId);

  const handleInviteUser = () => {
    if (!inviteEmail.trim()) {
      toast.error('Please enter an email address');
      return;
    }

    inviteUser(inviteEmail, inviteRole);
    setInviteEmail('');
  };

  const handleCreateShareLink = () => {
    createShareLink(linkRole, {
      expiresInDays: linkExpiry,
    });
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const getShareUrl = (token: string) => {
    return `${window.location.origin}/whiteboard/shared/${token}`;
  };

  // Update the getRoleBadgeColor function to use the centralized utility
  const getRoleBadgeColor = (role: WhiteboardRole) => {
    return getRoleColor(role);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Content style={{ maxWidth: '600px', maxHeight: '80vh', overflow: 'auto' }}>
        <Dialog.Title>
          <Flex align="center" gap="2">
            <Share2Icon size={20} />
            Share Whiteboard
          </Flex>
        </Dialog.Title>

        <Tabs.Root value={activeTab} onValueChange={setActiveTab}>
          <Tabs.List>
            <Tabs.Trigger value="collaborators">
              <Flex align="center" gap="1">
                <UsersIcon size={16} />
                Collaborators
              </Flex>
            </Tabs.Trigger>
            <Tabs.Trigger value="links">
              <Flex align="center" gap="1">
                <LinkIcon size={16} />
                Share Links
              </Flex>
            </Tabs.Trigger>
            <Tabs.Trigger value="invitations">
              <Flex align="center" gap="1">
                <MailIcon size={16} />
                Invitations
              </Flex>
            </Tabs.Trigger>
          </Tabs.List>

          <Box mt="4">
            <Tabs.Content value="collaborators">
              <Box>
                <Text size="2" weight="medium" mb="3">
                  Invite People
                </Text>
                
                <Flex gap="2" mb="4">
                  <TextField.Root
                    placeholder="Enter email address"
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                    style={{ flex: 1 }}
                  />
                  <Select.Root value={inviteRole} onValueChange={(value) => setInviteRole(value as WhiteboardRole)}>
                    <Select.Trigger style={{ width: '120px' }} />
                    <Select.Content>
                      <Select.Item value={WhiteboardRole.VIEWER}>Viewer</Select.Item>
                      <Select.Item value={WhiteboardRole.COMMENTER}>Commenter</Select.Item>
                      <Select.Item value={WhiteboardRole.EDITOR}>Editor</Select.Item>
                    </Select.Content>
                  </Select.Root>
                  <Button 
                    onClick={handleInviteUser}
                    disabled={isInvitingUser}
                    loading={isInvitingUser}
                  >
                    <UserPlusIcon size={16} />
                    Invite
                  </Button>
                </Flex>

                <Separator mb="4" />

                <Text size="2" weight="medium" mb="3">
                  Current Collaborators
                </Text>

                {isLoadingAccess ? (
                  <Text size="2" color="gray">Loading...</Text>
                ) : (
                  <Box>
                    {/* Owner */}
                    <Card mb="2" style={{ padding: '12px' }}>
                      <Flex justify="between" align="center">
                        <Flex align="center" gap="3">
                          <Box>
                            <Text size="2" weight="medium">
                              {accessInfo?.user.name || accessInfo?.user.email}
                            </Text>
                            <Text size="1" color="gray">
                              {accessInfo?.user.email}
                            </Text>
                          </Box>
                        </Flex>
                        <Badge color={getRoleBadgeColor(WhiteboardRole.OWNER)}>
                          Owner
                        </Badge>
                      </Flex>
                    </Card>

                    {/* Other collaborators */}
                    {accessInfo?.userAccess?.map((access) => (
                      <Card key={access.id} mb="2" style={{ padding: '12px' }}>
                        <Flex justify="between" align="center">
                          <Flex align="center" gap="3">
                            <Box>
                              <Text size="2" weight="medium">
                                {access.user.name || access.user.email}
                              </Text>
                              <Text size="1" color="gray">
                                {access.user.email}
                              </Text>
                            </Box>
                          </Flex>
                          <Flex align="center" gap="2">
                            <Badge color={getRoleBadgeColor(access.role as WhiteboardRole)}>
                              {access.role}
                            </Badge>
                            <IconButton
                              size="1"
                              variant="ghost"
                              color="red"
                              onClick={() => revokeAccess(access.userId)}
                            >
                              <TrashIcon size={14} />
                            </IconButton>
                          </Flex>
                        </Flex>
                      </Card>
                    ))}
                  </Box>
                )}
              </Box>
            </Tabs.Content>

            <Tabs.Content value="links">
              <Box>
                <Text size="2" weight="medium" mb="3">
                  Create Share Link
                </Text>
                
                <Flex gap="2" mb="4">
                  <Select.Root value={linkRole} onValueChange={(value) => setLinkRole(value as WhiteboardRole)}>
                    <Select.Trigger style={{ width: '120px' }} />
                    <Select.Content>
                      <Select.Item value={WhiteboardRole.VIEWER}>Viewer</Select.Item>
                      <Select.Item value={WhiteboardRole.COMMENTER}>Commenter</Select.Item>
                      <Select.Item value={WhiteboardRole.EDITOR}>Editor</Select.Item>
                    </Select.Content>
                  </Select.Root>
                  <Select.Root value={linkExpiry.toString()} onValueChange={(value) => setLinkExpiry(Number(value))}>
                    <Select.Trigger style={{ width: '120px' }} />
                    <Select.Content>
                      <Select.Item value="1">1 day</Select.Item>
                      <Select.Item value="7">7 days</Select.Item>
                      <Select.Item value="30">30 days</Select.Item>
                      <Select.Item value="365">1 year</Select.Item>
                    </Select.Content>
                  </Select.Root>
                  <Button onClick={handleCreateShareLink}>
                    <LinkIcon size={16} />
                    Create Link
                  </Button>
                </Flex>

                <Separator mb="4" />

                <Text size="2" weight="medium" mb="3">
                  Active Share Links
                </Text>

                {isLoadingLinks ? (
                  <Text size="2" color="gray">Loading...</Text>
                ) : shareLinks?.length === 0 ? (
                  <Text size="2" color="gray">No active share links</Text>
                ) : (
                  <Box>
                    {shareLinks?.map((link) => (
                      <Card key={link.id} mb="2" style={{ padding: '12px' }}>
                        <Flex justify="between" align="center">
                          <Box style={{ flex: 1 }}>
                            <Flex align="center" gap="2" mb="1">
                              <Badge color={getRoleBadgeColor(link.role as WhiteboardRole)}>
                                {link.role}
                              </Badge>
                              {link.expiresAt && (
                                <Flex align="center" gap="1">
                                  <ClockIcon size={12} />
                                  <Text size="1" color="gray">
                                    Expires {formatDate(link.expiresAt)}
                                  </Text>
                                </Flex>
                              )}
                            </Flex>
                            <Text size="1" color="gray" style={{ fontFamily: 'monospace' }}>
                              {getShareUrl(link.token)}
                            </Text>
                          </Box>
                          <Flex align="center" gap="1">
                            <IconButton
                              size="1"
                              variant="ghost"
                              onClick={() => copyToClipboard(getShareUrl(link.token))}
                            >
                              <CopyIcon size={14} />
                            </IconButton>
                            <IconButton
                              size="1"
                              variant="ghost"
                              color="red"
                              onClick={() => deactivateShareLink(link.id)}
                            >
                              <TrashIcon size={14} />
                            </IconButton>
                          </Flex>
                        </Flex>
                      </Card>
                    ))}
                  </Box>
                )}
              </Box>
            </Tabs.Content>

            <Tabs.Content value="invitations">
              <Box>
                <Text size="2" weight="medium" mb="3">
                  Pending Invitations
                </Text>

                {invitations?.length === 0 ? (
                  <Text size="2" color="gray">No pending invitations</Text>
                ) : (
                  <Box>
                    {invitations?.map((invitation) => (
                      <Card key={invitation.id} mb="2" style={{ padding: '12px' }}>
                        <Flex justify="between" align="center">
                          <Box>
                            <Text size="2" weight="medium">
                              {invitation.inviteeEmail}
                            </Text>
                            <Text size="1" color="gray">
                              Invited {formatDate(invitation.createdAt)} • Expires {formatDate(invitation.expiresAt)}
                            </Text>
                          </Box>
                                                        <Badge color={getRoleBadgeColor(invitation.role as WhiteboardRole)}>
                            {invitation.role}
                          </Badge>
                        </Flex>
                      </Card>
                    ))}
                  </Box>
                )}
              </Box>
            </Tabs.Content>
          </Box>
        </Tabs.Root>

        <Flex gap="3" mt="4" justify="end">
          <Dialog.Close>
            <Button variant="soft" color="gray">
              Close
            </Button>
          </Dialog.Close>
        </Flex>
      </Dialog.Content>
    </Dialog.Root>
  );
}
