'use client';

import { useState } from 'react';
import { Button, Card, Flex, Text, Switch, Select, Separator } from '@radix-ui/themes';
import type { WhiteboardSettings as WhiteboardSettingsType } from '@/types';

interface WhiteboardSettingsProps {
  settings: WhiteboardSettingsType;
  onSettingsChange: (settings: WhiteboardSettingsType) => void;
  onClose: () => void;
}

export default function WhiteboardSettings({
  settings,
  onSettingsChange,
  onClose
}: WhiteboardSettingsProps) {
  const [localSettings, setLocalSettings] = useState<WhiteboardSettingsType>(settings);

  const handleSave = () => {
    onSettingsChange(localSettings);
    onClose();
  };

  const handleReset = () => {
    const defaultSettings: WhiteboardSettingsType = {
      isPublic: false,
      allowComments: true,
      viewMode: 'edit',
      backgroundColor: '#ffffff',
      gridMode: false,
      snapToGrid: false,
      theme: 'light'
    };
    setLocalSettings(defaultSettings);
  };

  return (
    <Card className="w-96 p-6">
      <Flex direction="column" gap="4">
        <Text size="5" weight="bold">Whiteboard Settings</Text>

        {/* Visibility Settings */}
        <Flex direction="column" gap="3">
          <Text size="3" weight="medium">Visibility & Access</Text>

          <Flex justify="between" align="center">
            <Text size="2">Public whiteboard</Text>
            <Switch
              checked={localSettings.isPublic}
              onCheckedChange={(checked) =>
                setLocalSettings(prev => ({ ...prev, isPublic: checked }))
              }
            />
          </Flex>

          <Flex justify="between" align="center">
            <Text size="2">Allow comments</Text>
            <Switch
              checked={localSettings.allowComments}
              onCheckedChange={(checked) =>
                setLocalSettings(prev => ({ ...prev, allowComments: checked }))
              }
            />
          </Flex>

          <Flex justify="between" align="center">
            <Text size="2">View mode</Text>
            <Select.Root
              value={localSettings.viewMode}
              onValueChange={(value: 'edit' | 'view') =>
                setLocalSettings(prev => ({ ...prev, viewMode: value }))
              }
            >
              <Select.Trigger />
              <Select.Content>
                <Select.Item value="edit">Edit</Select.Item>
                <Select.Item value="view">View only</Select.Item>
              </Select.Content>
            </Select.Root>
          </Flex>
        </Flex>

        <Separator />

        {/* Appearance Settings */}
        <Flex direction="column" gap="3">
          <Text size="3" weight="medium">Appearance</Text>

          <Flex justify="between" align="center">
            <Text size="2">Theme</Text>
            <Select.Root
              value={localSettings.theme}
              onValueChange={(value: 'light' | 'dark') =>
                setLocalSettings(prev => ({ ...prev, theme: value }))
              }
            >
              <Select.Trigger />
              <Select.Content>
                <Select.Item value="light">Light</Select.Item>
                <Select.Item value="dark">Dark</Select.Item>
              </Select.Content>
            </Select.Root>
          </Flex>

          <Flex justify="between" align="center">
            <Text size="2">Show grid</Text>
            <Switch
              checked={localSettings.gridMode}
              onCheckedChange={(checked) =>
                setLocalSettings(prev => ({ ...prev, gridMode: checked }))
              }
            />
          </Flex>

          <Flex justify="between" align="center">
            <Text size="2">Snap to grid</Text>
            <Switch
              checked={localSettings.snapToGrid}
              onCheckedChange={(checked) =>
                setLocalSettings(prev => ({ ...prev, snapToGrid: checked }))
              }
            />
          </Flex>

          <Flex justify="between" align="center">
            <Text size="2">Background color</Text>
            <input
              type="color"
              value={localSettings.backgroundColor}
              onChange={(e) =>
                setLocalSettings(prev => ({ ...prev, backgroundColor: e.target.value }))
              }
              className="w-8 h-8 rounded border cursor-pointer"
            />
          </Flex>
        </Flex>

        <Separator />

        {/* Action Buttons */}
        <Flex gap="2" justify="end">
          <Button variant="soft" onClick={handleReset}>
            Reset to Default
          </Button>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Settings
          </Button>
        </Flex>
      </Flex>
    </Card>
  );
}
