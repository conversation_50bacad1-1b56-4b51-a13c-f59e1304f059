'use client';

import { useState } from 'react';
import {
  <PERSON>lex,
  Card,
  Text,
  Button,
  IconButton,
  Dialog,
  Select,
  Badge,
  Spinner
} from '@radix-ui/themes';
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye,
  Calendar,
  User,
  Globe,
  Lock
} from 'lucide-react';
import Link from 'next/link';

import { useCreateWhiteboard, useDeleteWhiteboard } from '@/hooks/useWhiteboardData';
import { api } from '@/components/providers/TRPCProvider';
import type { CreateWhiteboardInput } from '@/hooks/useWhiteboardData';

interface WhiteboardListProps {
  userId?: string;
}

export default function WhiteboardList({ userId }: WhiteboardListProps) {
  const [page, setPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newWhiteboardName, setNewWhiteboardName] = useState('');
  const [newWhiteboardTheme, setNewWhiteboardTheme] = useState<'light' | 'dark'>('light');
  const [newWhiteboardIsPublic, setNewWhiteboardIsPublic] = useState(false);

  // tRPC queries
  const {
    data: whiteboardsData,
    isLoading,
    error,
    refetch
  } = api.whiteboard.getAll.useQuery({
    page,
    limit: 12,
  });

  const {
    data: searchResults,
    isLoading: isSearching
  } = api.whiteboard.search.useQuery({
    query: searchQuery,
    page: 1,
    limit: 12,
  }, {
    enabled: searchQuery.length > 0,
  });

  const createWhiteboard = useCreateWhiteboard();
  const deleteWhiteboard = useDeleteWhiteboard();

  // Handle create whiteboard
  const handleCreateWhiteboard = async () => {
    if (!newWhiteboardName.trim()) return;

    try {
      const whiteboardData: CreateWhiteboardInput = {
        title: newWhiteboardName.trim(),
        isPublic: newWhiteboardIsPublic,
        theme: newWhiteboardTheme,
        allowComments: true,
        viewMode: 'edit',
        backgroundColor: newWhiteboardTheme === 'dark' ? '#1a1a1a' : '#ffffff',
        gridMode: false,
        snapToGrid: false,
      };

      createWhiteboard.mutate(whiteboardData);
      setShowCreateDialog(false);
      setNewWhiteboardName('');
      setNewWhiteboardTheme('light');
      setNewWhiteboardIsPublic(false);
      refetch();
    } catch (error) {
      console.error('Failed to create whiteboard:', error);
    }
  };

  // Handle delete whiteboard
  const handleDeleteWhiteboard = async (id: string) => {
    if (!confirm('Are you sure you want to delete this whiteboard?')) return;

    try {
      deleteWhiteboard.mutate({ id });
      refetch();
    } catch (error) {
      console.error('Failed to delete whiteboard:', error);
    }
  };

  // Format date
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  };

  const displayData = searchQuery ? searchResults : whiteboardsData;
  const isLoadingData = searchQuery ? isSearching : isLoading;

  return (
    <div className="p-6">
      {/* Header */}
      <Flex justify="between" align="center" mb="6">
        <div>
          <Text size="6" weight="bold">Whiteboards</Text>
          <Text size="3" color="gray" mt="1">
            Create and manage your collaborative whiteboards
          </Text>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus size={16} />
          New Whiteboard
        </Button>
      </Flex>

      {/* Search */}
      <Flex gap="3" mb="6">
        <div className="relative flex-1">
          <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search whiteboards..."
            value={searchQuery}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
            className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </Flex>

      {/* Loading state */}
      {isLoadingData && (
        <Flex align="center" justify="center" py="8">
          <Spinner size="3" />
        </Flex>
      )}

      {/* Error state */}
      {error && (
        <Flex align="center" justify="center" py="8">
          <Text color="red">Failed to load whiteboards: {error.message}</Text>
        </Flex>
      )}

      {/* Whiteboards grid */}
      {displayData && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-6">
            {displayData.whiteboards.map((whiteboard) => (
              <Card key={whiteboard.id} className="p-4 hover:shadow-md transition-shadow">
                <Flex direction="column" gap="3">
                  {/* Header */}
                  <Flex justify="between" align="start">
                    <div className="flex-1 min-w-0">
                      <Text size="3" weight="medium" className="truncate">
                        {whiteboard.title}
                      </Text>
                      <Flex align="center" gap="2" mt="1">
                        {whiteboard.isPublic ? (
                          <Badge color="green" size="1">
                            <Globe size={10} />
                            Public
                          </Badge>
                        ) : (
                          <Badge color="gray" size="1">
                            <Lock size={10} />
                            Private
                          </Badge>
                        )}
                        <Badge color="blue" size="1">
                          {whiteboard.theme}
                        </Badge>
                      </Flex>
                    </div>
                    <IconButton variant="ghost" size="1">
                      <MoreHorizontal size={14} />
                    </IconButton>
                  </Flex>

                  {/* Metadata */}
                  <Flex direction="column" gap="1">
                    <Flex align="center" gap="1">
                      <Calendar size={12} className="text-gray-400" />
                      <Text size="1" color="gray">
                        Updated {formatDate(whiteboard.updatedAt)}
                      </Text>
                    </Flex>
                    <Flex align="center" gap="1">
                      <User size={12} className="text-gray-400" />
                      <Text size="1" color="gray">
                        {whiteboard.userId}
                      </Text>
                    </Flex>
                  </Flex>

                  {/* Actions */}
                  <Flex gap="2" mt="2">
                    <Link href={`/whiteboard/${whiteboard.id}`} className="flex-1">
                      <Button variant="soft" size="2" className="w-full">
                        <Edit size={14} />
                        Edit
                      </Button>
                    </Link>
                    <Link href={`/whiteboard/${whiteboard.id}?mode=view`}>
                      <IconButton variant="ghost" size="2">
                        <Eye size={14} />
                      </IconButton>
                    </Link>
                    <IconButton 
                      variant="ghost" 
                      size="2" 
                      color="red"
                      onClick={() => handleDeleteWhiteboard(whiteboard.id)}
                      disabled={deleteWhiteboard.isPending}
                    >
                      <Trash2 size={14} />
                    </IconButton>
                  </Flex>
                </Flex>
              </Card>
            ))}
          </div>

          {/* Pagination */}
          {displayData.totalPages > 1 && (
            <Flex justify="center" gap="2">
              <Button
                variant="soft"
                disabled={page === 1}
                onClick={() => setPage(page - 1)}
              >
                Previous
              </Button>
              <Text size="2" className="flex items-center px-3">
                Page {page} of {displayData.totalPages}
              </Text>
              <Button
                variant="soft"
                disabled={page === displayData.totalPages}
                onClick={() => setPage(page + 1)}
              >
                Next
              </Button>
            </Flex>
          )}

          {/* Empty state */}
          {displayData.whiteboards.length === 0 && (
            <Flex direction="column" align="center" py="8" gap="3">
              <Text size="4" color="gray">
                {searchQuery ? 'No whiteboards found' : 'No whiteboards yet'}
              </Text>
              <Text size="2" color="gray">
                {searchQuery 
                  ? 'Try adjusting your search terms'
                  : 'Create your first whiteboard to get started'
                }
              </Text>
              {!searchQuery && (
                <Button onClick={() => setShowCreateDialog(true)} mt="2">
                  <Plus size={16} />
                  Create Whiteboard
                </Button>
              )}
            </Flex>
          )}
        </>
      )}

      {/* Create Whiteboard Dialog */}
      <Dialog.Root open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <Dialog.Content style={{ maxWidth: 450 }}>
          <Dialog.Title>Create New Whiteboard</Dialog.Title>
          <Dialog.Description size="2" mb="4">
            Create a new collaborative whiteboard for your team.
          </Dialog.Description>

          <Flex direction="column" gap="3">
            <label>
              <Text as="div" size="2" mb="1" weight="bold">
                Name
              </Text>
              <input
                type="text"
                placeholder="Enter whiteboard name..."
                value={newWhiteboardName}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewWhiteboardName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </label>

            <label>
              <Text as="div" size="2" mb="1" weight="bold">
                Theme
              </Text>
              <Select.Root value={newWhiteboardTheme} onValueChange={(value: 'light' | 'dark') => setNewWhiteboardTheme(value)}>
                <Select.Trigger />
                <Select.Content>
                  <Select.Item value="light">Light</Select.Item>
                  <Select.Item value="dark">Dark</Select.Item>
                </Select.Content>
              </Select.Root>
            </label>

            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={newWhiteboardIsPublic}
                onChange={(e) => setNewWhiteboardIsPublic(e.target.checked)}
              />
              <Text size="2">Make this whiteboard public</Text>
            </label>
          </Flex>

          <Flex gap="3" mt="4" justify="end">
            <Dialog.Close>
              <Button variant="soft" color="gray">
                Cancel
              </Button>
            </Dialog.Close>
            <Button 
              onClick={handleCreateWhiteboard}
              disabled={!newWhiteboardName.trim() || createWhiteboard.isPending}
            >
              {createWhiteboard.isPending ? <Spinner size="1" /> : 'Create'}
            </Button>
          </Flex>
        </Dialog.Content>
      </Dialog.Root>
    </div>
  );
}
