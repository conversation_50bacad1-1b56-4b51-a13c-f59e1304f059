'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { signOut } from '@/lib/auth-client';
import { Container, Flex, Text, Button, Card, IconButton } from '@radix-ui/themes';
import { Plus, FileText, Users, LogOut, ArrowLeft, Search, Trash2, Edit, Eye } from 'lucide-react';
import dynamic from 'next/dynamic';
import { toast, Toaster } from 'sonner';
import type { WhiteboardData } from '@/types';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { ErrorBoundary, WhiteboardErrorFallback } from '@/components/error/ErrorBoundary';
import TitleEditModal from '@/components/whiteboard/TitleEditModal';
import VisibilityToggle from '@/components/whiteboard/VisibilityToggle';
import {
  useWhiteboards,
  useWhiteboard,
  usePublicWhiteboard,
  useCreateWhiteboard,
  useDeleteWhiteboard,
  useSearchWhiteboards,
  type CreateWhiteboardInput,
} from '@/hooks/useWhiteboardData';

// Lazy load heavy components for better performance
const Whiteboard = dynamic(() => import('@/components/whiteboard/Whiteboard'), {
  loading: () => (
    <div className="h-screen bg-gray-50 flex items-center justify-center">
      <LoadingSpinner size="lg" text="Loading whiteboard editor..." />
    </div>
  ),
  ssr: false
});

const WhiteboardPreloader = dynamic(() => import('@/components/whiteboard/WhiteboardPreloader'), {
  loading: () => (
    <div className="fixed inset-0 bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center z-50">
      <LoadingSpinner size="lg" text="Preparing workspace..." />
    </div>
  ),
  ssr: false
});

interface WhiteboardAppProps {
  whiteboardId?: string | null;
  isNewWhiteboard?: boolean;
}

export default function WhiteboardApp({ whiteboardId, isNewWhiteboard }: WhiteboardAppProps) {
  const router = useRouter();
  const { data: session, status } = useAuth();

  // Local state
  const [isPreloaderVisible, setIsPreloaderVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [isTitleEditModalOpen, setIsTitleEditModalOpen] = useState(false);

  // Ref to track if we've already initiated whiteboard creation
  const hasInitiatedCreation = useRef(false);

  // Server state hooks
  const { data: whiteboardsData, isLoading: isLoadingWhiteboards, error: whiteboardsError } = useWhiteboards(currentPage, 10);
  const { data: currentWhiteboard, isLoading: isLoadingWhiteboard, error: whiteboardError } = useWhiteboard(
    whiteboardId && session?.user ? whiteboardId : undefined
  );
  const { data: publicWhiteboard, isLoading: isLoadingPublicWhiteboard, error: publicWhiteboardError } = usePublicWhiteboard(
    whiteboardId && !session?.user ? whiteboardId : undefined
  );
  const { data: searchResults, isLoading: isSearching } = useSearchWhiteboards(searchQuery, 1, 10);
  const createWhiteboard = useCreateWhiteboard();
  const deleteWhiteboard = useDeleteWhiteboard();

  // Determine which whiteboard to use based on authentication status
  const activeWhiteboard = session?.user ? currentWhiteboard : publicWhiteboard;
  const isLoadingActiveWhiteboard = session?.user ? isLoadingWhiteboard : isLoadingPublicWhiteboard;
  const activeWhiteboardError = session?.user ? whiteboardError : publicWhiteboardError;

  // Determine if user can edit the whiteboard
  const canEdit = session?.user?.id && activeWhiteboard && activeWhiteboard.userId === session.user.id;
  const isReadOnly = !canEdit;

  // Handle creating a new whiteboard
  const handleCreateWhiteboard = useCallback(() => {
    if (!session?.user) {
      toast.error('You must be logged in to create a whiteboard');
      return;
    }

    setIsPreloaderVisible(true);

    const newWhiteboardData: CreateWhiteboardInput = {
      title: `Whiteboard ${(whiteboardsData?.total || 0) + 1}`,
      content: { nodes: [], edges: [], viewport: { x: 0, y: 0, zoom: 1 } },
      isPublic: false,
      allowComments: true,
      viewMode: 'edit',
      backgroundColor: '#ffffff',
      gridMode: false,
      snapToGrid: false,
      theme: 'light',
    };

    createWhiteboard.mutate(newWhiteboardData, {
      onSuccess: (data) => {
        setIsPreloaderVisible(false);
        // Navigate to the new whiteboard using the dynamic route
        router.push(`/whiteboard/${data.id}`);
      },
      onError: () => {
        setIsPreloaderVisible(false);
        // Reset the flag on error so user can try again
        hasInitiatedCreation.current = false;
      },
    });
  }, [session?.user, whiteboardsData?.total, createWhiteboard]);

  // Handle auto-creation of whiteboard if isNewWhiteboard is true
  // Use a ref to prevent multiple creation attempts
  useEffect(() => {
    if (isNewWhiteboard && !whiteboardId && session?.user && !hasInitiatedCreation.current) {
      hasInitiatedCreation.current = true;
      handleCreateWhiteboard();
    }

    // Reset the flag when we're no longer in "new whiteboard" mode
    if (!isNewWhiteboard) {
      hasInitiatedCreation.current = false;
    }
  }, [isNewWhiteboard, whiteboardId, session?.user, handleCreateWhiteboard]);

  // Handle opening a whiteboard
  const handleOpenWhiteboard = (whiteboard: WhiteboardData) => {
    setIsPreloaderVisible(true);

    setTimeout(() => {
      setIsPreloaderVisible(false);
      // Navigate to the whiteboard using the dynamic route
      router.push(`/whiteboard/${whiteboard.id}`);
    }, 1000);
  };

  // Handle deleting a whiteboard
  const handleDeleteWhiteboard = useCallback((id: string, title: string) => {
    if (window.confirm(`Are you sure you want to delete "${title}"? This action cannot be undone.`)) {
      deleteWhiteboard.mutate({ id });
    }
  }, [deleteWhiteboard]);

  // Handle search
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const handleBackToHome = () => {
    router.push('/');
  };

  const handleBackToList = () => {
    // Navigate back to the whiteboard dashboard
    router.push('/whiteboard');
  };

  // Show loading state
  if (status === 'loading') {
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading..." />
      </div>
    );
  }

  // Redirect to sign-in if not authenticated and trying to access a specific whiteboard
  if (!session && whiteboardId) {
    router.push(`/auth/signin?callbackUrl=/whiteboard/${whiteboardId}`);
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Redirecting to sign in..." />
      </div>
    );
  }

  // Redirect to sign-in if not authenticated and trying to create a new whiteboard
  if (!session && isNewWhiteboard) {
    router.push('/auth/signin?callbackUrl=/whiteboard?new=true');
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Redirecting to sign in..." />
      </div>
    );
  }

  // Show loading state for whiteboard
  if (whiteboardId && isLoadingActiveWhiteboard) {
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading whiteboard..." />
      </div>
    );
  }

  // Show error state for whiteboard
  if (whiteboardId && activeWhiteboardError) {
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Text size="5" weight="medium" className="mb-2">
            Whiteboard not found
          </Text>
          <Text size="3" color="gray" className="mb-4">
            The whiteboard you're looking for doesn't exist or you don't have permission to view it.
          </Text>
          <Button onClick={handleBackToList}>
            <ArrowLeft size={16} />
            Back to Whiteboards
          </Button>
        </div>
      </div>
    );
  }

  // Show whiteboard interface if one is selected
  if (whiteboardId && activeWhiteboard) {
    return (
      <ErrorBoundary fallback={WhiteboardErrorFallback}>
        <div className="h-screen">
          <div className="absolute top-4 left-4 right-4 z-20 flex justify-between items-center bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-lg shadow-sm p-3">
            <Button
              variant="soft"
              size="2"
              onClick={handleBackToList}
            >
              <ArrowLeft size={16} />
              Back to Whiteboards
            </Button>
            <Flex gap="2" align="center">
              {isReadOnly && (
                <Flex align="center" gap="2" className="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded">
                  <Eye size={14} />
                  <Text size="2" color="gray">View Only</Text>
                </Flex>
              )}
              {canEdit && (
                <VisibilityToggle
                  whiteboardId={activeWhiteboard.id}
                  isPublic={activeWhiteboard.isPublic}
                  size="sm"
                />
              )}
              {!canEdit && activeWhiteboard.isPublic && (
                <Flex align="center" gap="2" className="px-3 py-1 bg-blue-100 dark:bg-blue-900 rounded">
                  <Users size={14} />
                  <Text size="2" color="blue">Public</Text>
                </Flex>
              )}
              <Text size="3" weight="medium">
                {activeWhiteboard.title}
              </Text>
              {canEdit && (
                <IconButton
                  size="1"
                  variant="ghost"
                  onClick={() => setIsTitleEditModalOpen(true)}
                >
                  <Edit size={14} />
                </IconButton>
              )}
            </Flex>
          </div>
          <Whiteboard
            initialData={activeWhiteboard}
            whiteboardId={activeWhiteboard.id}
            isReadOnly={isReadOnly}
          />

          {/* Title Edit Modal - only show for owners */}
          {canEdit && (
            <TitleEditModal
              isOpen={isTitleEditModalOpen}
              onClose={() => setIsTitleEditModalOpen(false)}
              whiteboardId={activeWhiteboard.id}
              currentTitle={activeWhiteboard.title}
            />
          )}
        </div>

        {/* Toast notifications */}
        <Toaster position="bottom-right" />
      </ErrorBoundary>
    );
  }

  // If no whiteboard is selected and user is not authenticated, redirect to homepage
  if (!whiteboardId && !session) {
    router.push('/');
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Redirecting..." />
      </div>
    );
  }

  // Show loading state for whiteboards list
  if (isLoadingWhiteboards) {
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading your whiteboards..." />
      </div>
    );
  }

  // Show error state for whiteboards list
  if (whiteboardsError) {
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Text size="5" weight="medium" className="mb-2">
            Failed to load whiteboards
          </Text>
          <Text size="3" color="gray" className="mb-4">
            There was an error loading your whiteboards. Please try again.
          </Text>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  const displayWhiteboards = searchQuery ? searchResults?.whiteboards : whiteboardsData?.whiteboards;
  const isDisplayLoading = searchQuery ? isSearching : isLoadingWhiteboards;

  // Show whiteboard list/dashboard
  return (
    <>
      <Container size="4" className="min-h-screen py-8">
        <Flex direction="column" gap="6">
          {/* Header */}
          <Flex direction="column" gap="2" align="center">
            <Button variant="ghost" onClick={handleBackToHome} className="self-start">
              <ArrowLeft size={16} />
              Back to Home
            </Button>
            <Text size="8" weight="bold">
              Your Whiteboards
            </Text>
            <Text size="4" color="gray" align="center">
              Create, edit, and manage your collaborative whiteboards
            </Text>
          </Flex>

          {/* Create New Whiteboard */}
          <Card className="p-6">
            <Flex direction="column" gap="4" align="center">
              <Text size="5" weight="medium">
                Start Creating
              </Text>
              <Button
                size="3"
                onClick={handleCreateWhiteboard}
                disabled={createWhiteboard.isPending}
              >
                <Plus size={18} />
                {createWhiteboard.isPending ? 'Creating...' : 'Create New Whiteboard'}
              </Button>
            </Flex>
          </Card>

          {/* Search */}
          <Card className="p-4">
            <Flex gap="3" align="center">
              <Search size={20} />
              <input
                type="text"
                placeholder="Search whiteboards..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {searchQuery && (
                <Button variant="soft" size="1" onClick={() => setSearchQuery('')}>
                  Clear
                </Button>
              )}
            </Flex>
          </Card>

          {/* Whiteboards List */}
          {isDisplayLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="md" text={searchQuery ? "Searching..." : "Loading whiteboards..."} />
            </div>
          ) : displayWhiteboards && displayWhiteboards.length > 0 ? (
            <Flex direction="column" gap="4">
              <Flex justify="between" align="center">
                <Text size="5" weight="medium">
                  {searchQuery ? `Search Results (${searchResults?.total || 0})` : `Your Whiteboards (${whiteboardsData?.total || 0})`}
                </Text>
                {!searchQuery && whiteboardsData && whiteboardsData.totalPages > 1 && (
                  <Flex gap="2" align="center">
                    <Button
                      variant="soft"
                      size="1"
                      disabled={currentPage === 1}
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    >
                      Previous
                    </Button>
                    <Text size="2" color="gray">
                      Page {currentPage} of {whiteboardsData.totalPages}
                    </Text>
                    <Button
                      variant="soft"
                      size="1"
                      disabled={currentPage === whiteboardsData.totalPages}
                      onClick={() => setCurrentPage(prev => Math.min(whiteboardsData.totalPages, prev + 1))}
                    >
                      Next
                    </Button>
                  </Flex>
                )}
              </Flex>
              <Flex direction="column" gap="3">
                {displayWhiteboards.map(whiteboard => (
                  <Card
                    key={whiteboard.id}
                    className="p-4 hover:bg-gray-50 transition-colors"
                  >
                    <Flex justify="between" align="center">
                      <Flex
                        gap="3"
                        align="center"
                        className="flex-1 cursor-pointer"
                        onClick={() => handleOpenWhiteboard(whiteboard)}
                      >
                        <FileText size={20} />
                        <Flex direction="column" gap="1">
                          <Text size="3" weight="medium">
                            {whiteboard.title}
                          </Text>
                          <Text size="2" color="gray">
                            Updated {new Date(whiteboard.updatedAt).toLocaleDateString()}
                          </Text>
                        </Flex>
                      </Flex>
                      <Flex gap="2" align="center">
                        {whiteboard.isPublic && (
                          <div className="flex items-center gap-1">
                            <Users size={14} />
                            <Text size="1" color="gray">Public</Text>
                          </div>
                        )}
                        <Text size="2" color="gray">
                          {whiteboard.viewMode === 'view' ? 'View Only' : 'Editable'}
                        </Text>
                        <Button
                          variant="soft"
                          size="1"
                          color="red"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteWhiteboard(whiteboard.id, whiteboard.title);
                          }}
                          disabled={deleteWhiteboard.isPending}
                        >
                          <Trash2 size={14} />
                        </Button>
                      </Flex>
                    </Flex>
                  </Card>
                ))}
              </Flex>
            </Flex>
          ) : (
            <Card className="p-8">
              <Flex direction="column" gap="4" align="center">
                <FileText size={48} color="gray" />
                <Text size="4" weight="medium" color="gray">
                  {searchQuery ? 'No whiteboards found' : 'No whiteboards yet'}
                </Text>
                <Text size="3" color="gray" align="center">
                  {searchQuery
                    ? 'Try adjusting your search terms or create a new whiteboard.'
                    : 'Create your first whiteboard to get started with collaborative planning.'
                  }
                </Text>
                {!searchQuery && (
                  <Button size="2" onClick={handleCreateWhiteboard}>
                    <Plus size={16} />
                    Create Your First Whiteboard
                  </Button>
                )}
              </Flex>
            </Card>
          )}
        </Flex>
      </Container>

      {/* Preloader */}
      {isPreloaderVisible && (
        <WhiteboardPreloader onComplete={() => setIsPreloaderVisible(false)} />
      )}

      {/* Toast notifications */}
      <Toaster position="bottom-right" />
    </>
  );
}
