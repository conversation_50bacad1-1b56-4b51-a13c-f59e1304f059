'use client';

import { useState, useCallback } from 'react';
import { Card, Flex, Text, Badge, IconButton, Button, TextField, TextArea, Select } from '@radix-ui/themes';
import { Edit2, Trash2, Calendar, Tag, Check, X } from 'lucide-react';
import { Handle, Position, useReactFlow } from 'reactflow';
import type { NodeProps } from 'reactflow';
import type { GoalNodeData, Goal } from '@/types';

export default function GoalNode({
  id,
  data,
  selected
}: NodeProps<GoalNodeData>) {
  const [isHovered, setIsHovered] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  // Editing state
  const [editTitle, setEditTitle] = useState(data.title);
  const [editDescription, setEditDescription] = useState(data.description || '');
  const [editDueDate, setEditDueDate] = useState(
    data.dueDate ? new Date(data.dueDate).toISOString().slice(0, 10) : ''
  );
  const [editPriority, setEditPriority] = useState<Goal['priority']>(data.priority);
  const [editStatus, setEditStatus] = useState<Goal['status']>(data.status);
  const [editTags, setEditTags] = useState(data.tags.join(', '));

  const { setNodes } = useReactFlow();

  const updateNodeData = useCallback((updates: Partial<GoalNodeData>) => {
    setNodes((nodes) =>
      nodes.map((node) =>
        node.id === id
          ? { ...node, data: { ...node.data, ...updates } }
          : node
      )
    );
  }, [id, setNodes]);

  const handleStatusChange = () => {
    const statusOrder: Goal['status'][] = ['not-started', 'in-progress', 'completed'];
    const currentIndex = statusOrder.indexOf(data.status);
    const nextStatus = statusOrder[(currentIndex + 1) % statusOrder.length];
    updateNodeData({ status: nextStatus });
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSaveEdit = () => {
    if (!editTitle.trim()) {
      alert('Please enter a goal title');
      return;
    }

    const updatedData: GoalNodeData = {
      title: editTitle.trim(),
      description: editDescription.trim() || undefined,
      dueDate: editDueDate ? new Date(editDueDate) : undefined,
      priority: editPriority,
      status: editStatus,
      tags: editTags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
    };

    updateNodeData(updatedData);
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    // Reset edit state to current data
    setEditTitle(data.title);
    setEditDescription(data.description || '');
    setEditDueDate(data.dueDate ? new Date(data.dueDate).toISOString().slice(0, 10) : '');
    setEditPriority(data.priority);
    setEditStatus(data.status);
    setEditTags(data.tags.join(', '));
    setIsEditing(false);
  };

  const handleDelete = () => {
    setNodes((nodes) => nodes.filter((node) => node.id !== id));
  };

  const getPriorityColor = (priority: Goal['priority']) => {
    switch (priority) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'green';
      default: return 'gray';
    }
  };

  const getStatusColor = (status: Goal['status']) => {
    switch (status) {
      case 'completed': return 'green';
      case 'in-progress': return 'blue';
      case 'not-started': return 'gray';
      default: return 'gray';
    }
  };

  const getStatusLabel = (status: Goal['status']) => {
    switch (status) {
      case 'completed': return 'Completed';
      case 'in-progress': return 'In Progress';
      case 'not-started': return 'Not Started';
      default: return 'Unknown';
    }
  };

  // Render editing interface
  if (isEditing) {
    return (
      <>
        <Handle type="target" position={Position.Top} />
        <Card
          className={`w-96 transition-all duration-200 ${
            selected ? 'ring-2 ring-blue-500' : ''
          }`}
          style={{
            backgroundColor: '#ffffff',
            border: '2px solid #3b82f6'
          }}
        >
          <Flex direction="column" gap="3" p="4">
            <Flex justify="between" align="center">
              <Text size="3" weight="bold">Edit Goal</Text>
              <Flex gap="1">
                <IconButton size="1" variant="soft" color="green" onClick={handleSaveEdit}>
                  <Check size={12} />
                </IconButton>
                <IconButton size="1" variant="soft" color="red" onClick={handleCancelEdit}>
                  <X size={12} />
                </IconButton>
              </Flex>
            </Flex>

            <Flex direction="column" gap="2">
              <Text size="1" weight="medium">Goal Title *</Text>
              <TextField.Root
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                placeholder="Enter goal title"
                size="1"
              />
            </Flex>

            <Flex direction="column" gap="2">
              <Text size="1" weight="medium">Description</Text>
              <TextArea
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
                placeholder="Enter goal description (optional)"
                rows={2}
                size="1"
              />
            </Flex>

            <Flex gap="2">
              <Flex direction="column" gap="1" flexGrow="1">
                <Text size="1" weight="medium">Priority</Text>
                <Select.Root value={editPriority} onValueChange={(value: Goal['priority']) => setEditPriority(value)} size="1">
                  <Select.Trigger />
                  <Select.Content>
                    <Select.Item value="low">🟢 Low</Select.Item>
                    <Select.Item value="medium">🟡 Medium</Select.Item>
                    <Select.Item value="high">🔴 High</Select.Item>
                  </Select.Content>
                </Select.Root>
              </Flex>

              <Flex direction="column" gap="1" flexGrow="1">
                <Text size="1" weight="medium">Status</Text>
                <Select.Root value={editStatus} onValueChange={(value: Goal['status']) => setEditStatus(value)} size="1">
                  <Select.Trigger />
                  <Select.Content>
                    <Select.Item value="not-started">Not Started</Select.Item>
                    <Select.Item value="in-progress">In Progress</Select.Item>
                    <Select.Item value="completed">Completed</Select.Item>
                  </Select.Content>
                </Select.Root>
              </Flex>
            </Flex>

            <Flex direction="column" gap="1">
              <Text size="1" weight="medium">Due Date</Text>
              <input
                type="date"
                value={editDueDate}
                onChange={(e) => setEditDueDate(e.target.value)}
                min={new Date().toISOString().slice(0, 10)}
                className="px-2 py-1 border rounded-md text-sm"
              />
            </Flex>

            <Flex direction="column" gap="1">
              <Text size="1" weight="medium">Tags</Text>
              <TextField.Root
                value={editTags}
                onChange={(e) => setEditTags(e.target.value)}
                placeholder="Enter tags separated by commas"
                size="1"
              />
              <Text size="1" color="gray">
                Example: frontend, urgent, Q1-goals
              </Text>
            </Flex>
          </Flex>
        </Card>
        <Handle type="source" position={Position.Bottom} />
      </>
    );
  }

  // Render normal view
  return (
    <>
      <Handle type="target" position={Position.Top} />
      <Card
        className={`transition-all duration-200 ${
          selected ? 'ring-2 ring-blue-500 shadow-lg' : 'shadow-md hover:shadow-lg'
        }`}
        style={{
          width: '288px', // Equivalent to w-72
          borderLeft: `4px solid var(--${getPriorityColor(data.priority)}-9)`
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <Flex direction="column" gap="3" p="3">
          {/* Header */}
          <Flex justify="between" align="start">
            <Flex direction="column" gap="1" flexGrow="1">
              <Text 
                size="3" 
                weight="bold" 
                className={data.status === 'completed' ? 'line-through' : ''}
              >
                {data.title}
              </Text>
              {data.description && (
                <Text size="2" color="gray">
                  {data.description}
                </Text>
              )}
            </Flex>

            {(isHovered || selected) && (
              <Flex gap="1">
                <IconButton size="1" variant="ghost" onClick={handleEdit}>
                  <Edit2 size={12} />
                </IconButton>
                <IconButton size="1" variant="ghost" color="red" onClick={handleDelete}>
                  <Trash2 size={12} />
                </IconButton>
              </Flex>
            )}
          </Flex>

          {/* Status and Priority */}
          <Flex justify="between" align="center">
            <Button 
              size="1" 
              variant="soft" 
              color={getStatusColor(data.status)}
              onClick={handleStatusChange}
            >
              {getStatusLabel(data.status)}
            </Button>
            <Badge color={getPriorityColor(data.priority)} size="1">
              {data.priority.charAt(0).toUpperCase() + data.priority.slice(1)} Priority
            </Badge>
          </Flex>

          {/* Due Date */}
          {data.dueDate && (
            <Flex align="center" gap="1">
              <Calendar size={12} />
              <Text size="1" color="gray">
                Due: {new Date(data.dueDate).toLocaleDateString()}
              </Text>
            </Flex>
          )}

          {/* Tags */}
          {data.tags.length > 0 && (
            <Flex align="center" gap="1" wrap="wrap">
              <Tag size={12} />
              {data.tags.map((tag, index) => (
                <Badge key={index} size="1" variant="soft" color="gray">
                  {tag}
                </Badge>
              ))}
            </Flex>
          )}
        </Flex>
      </Card>
      <Handle type="source" position={Position.Bottom} />
    </>
  );
}
