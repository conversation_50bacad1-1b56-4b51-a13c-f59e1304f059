'use client';

import { useState, useRef, useCallback } from 'react';
import { Card, Flex, Text, IconButton, TextField, Button } from '@radix-ui/themes';
import { Edit2, Trash2, Check, X, Plus } from 'lucide-react';
import { Handle, Position, useReactFlow } from 'reactflow';
import type { NodeProps } from 'reactflow';
import type { HillChartNodeData, HillChartItem } from '@/types';

const defaultColors = [
  '#ef4444', '#f97316', '#eab308', '#22c55e',
  '#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899'
];

export default function HillChartNode({
  id,
  data,
  selected
}: NodeProps<HillChartNodeData>) {
  const [isHovered, setIsHovered] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [draggingItem, setDraggingItem] = useState<string | null>(null);
  const svgRef = useRef<SVGSVGElement>(null);

  // Editing state
  const [editTitle, setEditTitle] = useState(data.title);
  const [editWidth, setEditWidth] = useState(data.width);
  const [editHeight, setEditHeight] = useState(data.height);
  const [editItems, setEditItems] = useState<Omit<HillChartItem, 'id'>[]>(
    data.items.map(item => ({ ...item, id: undefined }))
  );

  const { setNodes } = useReactFlow();

  const { width, height } = data;
  const margin = 20;
  const hillHeight = height * 0.6;

  const updateNodeData = useCallback((updates: Partial<HillChartNodeData>) => {
    setNodes((nodes) =>
      nodes.map((node) =>
        node.id === id
          ? { ...node, data: { ...node.data, ...updates } }
          : node
      )
    );
  }, [id, setNodes]);

  // Generate hill path (bell curve)
  const generateHillPath = () => {
    const hillWidth = width - (margin * 2);
    const hillTop = margin + (height - hillHeight) / 2;
    const hillBottom = hillTop + hillHeight;

    let path = `M ${margin} ${hillBottom}`;

    for (let x = 0; x <= hillWidth; x += 2) {
      const progress = x / hillWidth;
      const y = hillBottom - (Math.sin(progress * Math.PI) * hillHeight);
      path += ` L ${margin + x} ${y}`;
    }

    return path;
  };

  // Calculate Y position for a given X position on the hill
  const getYForPosition = (position: number) => {
    const hillWidth = width - (margin * 2);
    const hillTop = margin + (height - hillHeight) / 2;
    const hillBottom = hillTop + hillHeight;
    const progress = position / 100;
    return hillBottom - (Math.sin(progress * Math.PI) * hillHeight);
  };

  const handleItemDrag = (itemId: string, event: React.MouseEvent) => {
    event.preventDefault();
    setDraggingItem(itemId);

    const svg = svgRef.current;
    if (!svg) return;

    const rect = svg.getBoundingClientRect();
    const hillWidth = width - (margin * 2);

    const handleMouseMove = (e: MouseEvent) => {
      const x = e.clientX - rect.left;
      const clampedX = Math.max(margin, Math.min(x, width - margin));
      const position = ((clampedX - margin) / hillWidth) * 100;

      const updatedItems = data.items.map(item =>
        item.id === itemId ? { ...item, position } : item
      );

      updateNodeData({ items: updatedItems });
    };

    const handleMouseUp = () => {
      setDraggingItem(null);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const addEditItem = () => {
    const newItem: Omit<HillChartItem, 'id'> = {
      name: `Item ${editItems.length + 1}`,
      position: 25,
      color: defaultColors[editItems.length % defaultColors.length],
      description: ''
    };
    setEditItems([...editItems, newItem]);
  };

  const updateEditItem = (index: number, field: keyof HillChartItem, value: string | number) => {
    const updatedItems = editItems.map((item, i) =>
      i === index ? { ...item, [field]: value } : item
    );
    setEditItems(updatedItems);
  };

  const removeEditItem = (index: number) => {
    setEditItems(editItems.filter((_, i) => i !== index));
  };

  const handleSaveEdit = () => {
    if (!editTitle.trim()) {
      alert('Please enter a chart title');
      return;
    }

    if (editItems.length === 0) {
      alert('Please add at least one item to the chart');
      return;
    }

    const itemsWithIds: HillChartItem[] = editItems.map((item, index) => ({
      ...item,
      id: `item-${Date.now()}-${index}`
    }));

    const updatedData: HillChartNodeData = {
      title: editTitle.trim(),
      items: itemsWithIds,
      width: editWidth,
      height: editHeight
    };

    updateNodeData(updatedData);
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    // Reset edit state to current data
    setEditTitle(data.title);
    setEditWidth(data.width);
    setEditHeight(data.height);
    setEditItems(data.items.map(item => ({ ...item, id: undefined })));
    setIsEditing(false);
  };

  const handleDelete = () => {
    setNodes((nodes) => nodes.filter((node) => node.id !== id));
  };

  // Render editing interface
  if (isEditing) {
    return (
      <>
        <Handle type="target" position={Position.Top} />
        <Card
          className={`transition-all duration-200 ${
            selected ? 'ring-2 ring-blue-500' : ''
          }`}
          style={{
            width: 500,
            maxHeight: '80vh',
            overflow: 'auto',
            backgroundColor: '#ffffff',
            border: '2px solid #3b82f6'
          }}
        >
          <Flex direction="column" gap="3" p="4">
            <Flex justify="between" align="center">
              <Text size="3" weight="bold">Edit Hill Chart</Text>
              <Flex gap="1">
                <IconButton size="1" variant="soft" color="green" onClick={handleSaveEdit}>
                  <Check size={12} />
                </IconButton>
                <IconButton size="1" variant="soft" color="red" onClick={handleCancelEdit}>
                  <X size={12} />
                </IconButton>
              </Flex>
            </Flex>

            <Flex direction="column" gap="2">
              <Text size="1" weight="medium">Chart Title *</Text>
              <TextField.Root
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                placeholder="Enter chart title"
                size="1"
              />
            </Flex>

            <Flex gap="2">
              <Flex direction="column" gap="1" flexGrow="1">
                <Text size="1" weight="medium">Width</Text>
                <TextField.Root
                  type="number"
                  value={editWidth.toString()}
                  onChange={(e) => setEditWidth(Number.parseInt(e.target.value) || 400)}
                  min="200"
                  max="800"
                  size="1"
                />
              </Flex>

              <Flex direction="column" gap="1" flexGrow="1">
                <Text size="1" weight="medium">Height</Text>
                <TextField.Root
                  type="number"
                  value={editHeight.toString()}
                  onChange={(e) => setEditHeight(Number.parseInt(e.target.value) || 200)}
                  min="150"
                  max="400"
                  size="1"
                />
              </Flex>
            </Flex>

            <Flex direction="column" gap="2">
              <Flex justify="between" align="center">
                <Text size="2" weight="medium">Chart Items</Text>
                <Button size="1" onClick={addEditItem}>
                  <Plus size={12} />
                  Add Item
                </Button>
              </Flex>

              {editItems.length === 0 ? (
                <Card className="p-3 text-center">
                  <Text size="1" color="gray">
                    No items added yet. Click "Add Item" to get started.
                  </Text>
                </Card>
              ) : (
                <Flex direction="column" gap="2" style={{ maxHeight: '300px', overflow: 'auto' }}>
                  {editItems.map((item, index) => (
                    <Card key={`edit-item-${index}-${item.name}`} className="p-2">
                      <Flex direction="column" gap="2">
                        <Flex justify="between" align="center">
                          <Text size="1" weight="medium">Item {index + 1}</Text>
                          <IconButton
                            size="1"
                            variant="ghost"
                            color="red"
                            onClick={() => removeEditItem(index)}
                          >
                            <Trash2 size={10} />
                          </IconButton>
                        </Flex>

                        <Flex gap="2">
                          <Flex direction="column" gap="1" flexGrow="1">
                            <Text size="1">Name *</Text>
                            <TextField.Root
                              value={item.name}
                              onChange={(e) => updateEditItem(index, 'name', e.target.value)}
                              placeholder="Item name"
                              size="1"
                            />
                          </Flex>

                          <Flex direction="column" gap="1">
                            <Text size="1">Color</Text>
                            <input
                              type="color"
                              value={item.color}
                              onChange={(e) => updateEditItem(index, 'color', e.target.value)}
                              className="w-8 h-6 rounded border cursor-pointer"
                            />
                          </Flex>
                        </Flex>

                        <Flex direction="column" gap="1">
                          <Text size="1">Position (0-100)</Text>
                          <Flex align="center" gap="2">
                            <input
                              type="range"
                              min="0"
                              max="100"
                              value={item.position}
                              onChange={(e) => updateEditItem(index, 'position', Number.parseInt(e.target.value))}
                              className="flex-grow"
                            />
                            <Text size="1" className="w-6 text-center">
                              {Math.round(item.position)}
                            </Text>
                          </Flex>
                        </Flex>

                        <Flex direction="column" gap="1">
                          <Text size="1">Description</Text>
                          <TextField.Root
                            value={item.description || ''}
                            onChange={(e) => updateEditItem(index, 'description', e.target.value)}
                            placeholder="Optional description"
                            size="1"
                          />
                        </Flex>
                      </Flex>
                    </Card>
                  ))}
                </Flex>
              )}
            </Flex>
          </Flex>
        </Card>
        <Handle type="source" position={Position.Bottom} />
      </>
    );
  }

  // Render normal view
  return (
    <>
      <Handle type="target" position={Position.Top} />
      <Card
        className={`transition-all duration-200 ${
          selected ? 'ring-2 ring-blue-500 shadow-lg' : 'shadow-md hover:shadow-lg'
        }`}
        style={{ width: width + 40, height: height + 80 }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <Flex direction="column" gap="3" p="3">
          {/* Header */}
          <Flex justify="between" align="center">
            <Text size="3" weight="bold">
              {data.title}
            </Text>
            {(isHovered || selected) && (
              <Flex gap="1">
                <IconButton size="1" variant="ghost" onClick={handleEdit}>
                  <Edit2 size={12} />
                </IconButton>
                <IconButton size="1" variant="ghost" color="red" onClick={handleDelete}>
                  <Trash2 size={12} />
                </IconButton>
              </Flex>
            )}
          </Flex>

          {/* Hill Chart SVG */}
          <div className="relative">
            <svg
              ref={svgRef}
              width={width}
              height={height}
              className="border rounded"
              style={{ backgroundColor: '#f8f9fa' }}
            >
              {/* Hill path */}
              <path
                d={generateHillPath()}
                fill="none"
                stroke="#6b7280"
                strokeWidth="2"
              />

              {/* Midpoint line */}
              <line
                x1={width / 2}
                y1={margin}
                x2={width / 2}
                y2={height - margin}
                stroke="#6b7280"
                strokeWidth="1"
                strokeDasharray="4,4"
                opacity="0.5"
              />

              {/* Status labels */}
              <text x={width * 0.25} y={height - 5} textAnchor="middle" fontSize="10" fill="#6b7280">
                Problem Unclear
              </text>
              <text x={width * 0.75} y={height - 5} textAnchor="middle" fontSize="10" fill="#6b7280">
                Solution Clear
              </text>

              {/* Hill chart items */}
              {data.items.map((item) => {
                const x = margin + ((width - margin * 2) * item.position / 100);
                const y = getYForPosition(item.position);

                return (
                  <g key={item.id}>
                    <circle
                      cx={x}
                      cy={y}
                      r="8"
                      fill={item.color}
                      stroke="#ffffff"
                      strokeWidth="2"
                      className={`cursor-pointer transition-all ${
                        draggingItem === item.id ? 'opacity-50' : 'hover:r-10'
                      }`}
                      onMouseDown={(e) => handleItemDrag(item.id, e)}
                    />
                    <text
                      x={x}
                      y={y - 15}
                      textAnchor="middle"
                      fontSize="10"
                      fontWeight="bold"
                      fill={item.color}
                      className="pointer-events-none"
                    >
                      {item.name}
                    </text>
                  </g>
                );
              })}
            </svg>
          </div>
        </Flex>
      </Card>
      <Handle type="source" position={Position.Bottom} />
    </>
  );
}
