'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, Flex, Text, Button, Badge, IconButton, TextField, Select, Dialog } from '@radix-ui/themes';
import { Play, Pause, RotateCcw, Edit2, Trash2, Check, X } from 'lucide-react';
import { Handle, Position, useReactFlow } from 'reactflow';
import type { NodeProps } from 'reactflow';
import type { CountdownTimerNodeData } from '@/types';

export default function CountdownTimerNode({
  id,
  data,
  selected
}: NodeProps<CountdownTimerNodeData>) {
  const [timeLeft, setTimeLeft] = useState<string>('');
  const [isRunning, setIsRunning] = useState(data.isActive);
  const [isHovered, setIsHovered] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  // Editing state
  const [editTitle, setEditTitle] = useState(data.title);
  const [editEndDate, setEditEndDate] = useState(
    new Date(data.endDate).toISOString().slice(0, 16)
  );
  const [editColor, setEditColor] = useState(data.style.color);
  const [editFontSize, setEditFontSize] = useState(data.style.fontSize);
  const [editBackgroundColor, setEditBackgroundColor] = useState(
    data.style.backgroundColor || '#ffffff'
  );

  const { setNodes, getNodes } = useReactFlow();

  // Calculate time left
  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date().getTime();
      const endTime = new Date(data.endDate).getTime();
      const difference = endTime - now;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        if (days > 0) {
          setTimeLeft(`${days}d ${hours}h ${minutes}m ${seconds}s`);
        } else if (hours > 0) {
          setTimeLeft(`${hours}h ${minutes}m ${seconds}s`);
        } else if (minutes > 0) {
          setTimeLeft(`${minutes}m ${seconds}s`);
        } else {
          setTimeLeft(`${seconds}s`);
        }
      } else {
        setTimeLeft('00:00:00');
      }
    };

    calculateTimeLeft();
    let interval: NodeJS.Timeout | null = null;

    if (isRunning) {
      interval = setInterval(calculateTimeLeft, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [data.endDate, isRunning]);

  const updateNodeData = useCallback((updates: Partial<CountdownTimerNodeData>) => {
    setNodes((nodes) =>
      nodes.map((node) =>
        node.id === id
          ? { ...node, data: { ...node.data, ...updates } }
          : node
      )
    );
  }, [id, setNodes]);

  const handlePlayPause = () => {
    const newIsRunning = !isRunning;
    setIsRunning(newIsRunning);
    updateNodeData({ isActive: newIsRunning });
  };

  const handleReset = () => {
    setIsRunning(false);
    updateNodeData({ isActive: false });
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSaveEdit = () => {
    if (!editTitle.trim() || !editEndDate) {
      alert('Please fill in all required fields');
      return;
    }

    const updatedData: CountdownTimerNodeData = {
      title: editTitle.trim(),
      endDate: new Date(editEndDate),
      style: {
        color: editColor,
        fontSize: editFontSize,
        backgroundColor: editBackgroundColor
      },
      isActive: isRunning
    };

    updateNodeData(updatedData);
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    // Reset edit state to current data
    setEditTitle(data.title);
    setEditEndDate(new Date(data.endDate).toISOString().slice(0, 16));
    setEditColor(data.style.color);
    setEditFontSize(data.style.fontSize);
    setEditBackgroundColor(data.style.backgroundColor || '#ffffff');
    setIsEditing(false);
  };

  const handleDelete = () => {
    setNodes((nodes) => nodes.filter((node) => node.id !== id));
  };

  const isExpired = new Date() > new Date(data.endDate);
  const statusColor = isExpired ? 'red' : isRunning ? 'green' : 'gray';

  // Render editing interface
  if (isEditing) {
    return (
      <>
        <Handle type="target" position={Position.Top} />
        <Card
          className={`w-80 transition-all duration-200 ${
            selected ? 'ring-2 ring-blue-500' : ''
          }`}
          style={{
            backgroundColor: '#ffffff',
            border: '2px solid #3b82f6'
          }}
        >
          <Flex direction="column" gap="3" p="4">
            <Flex justify="between" align="center">
              <Text size="3" weight="bold">Edit Timer</Text>
              <Flex gap="1">
                <IconButton size="1" variant="soft" color="green" onClick={handleSaveEdit}>
                  <Check size={12} />
                </IconButton>
                <IconButton size="1" variant="soft" color="red" onClick={handleCancelEdit}>
                  <X size={12} />
                </IconButton>
              </Flex>
            </Flex>

            <Flex direction="column" gap="2">
              <Text size="1" weight="medium">Timer Title *</Text>
              <TextField.Root
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                placeholder="Enter timer title"
                size="1"
              />
            </Flex>

            <Flex direction="column" gap="2">
              <Text size="1" weight="medium">End Date & Time *</Text>
              <input
                type="datetime-local"
                value={editEndDate}
                onChange={(e) => setEditEndDate(e.target.value)}
                min={new Date().toISOString().slice(0, 16)}
                className="px-2 py-1 border rounded-md text-sm"
              />
            </Flex>

            <Flex gap="2">
              <Flex direction="column" gap="1" flexGrow="1">
                <Text size="1" weight="medium">Text Color</Text>
                <input
                  type="color"
                  value={editColor}
                  onChange={(e) => setEditColor(e.target.value)}
                  className="w-full h-6 rounded border cursor-pointer"
                />
              </Flex>

              <Flex direction="column" gap="1" flexGrow="1">
                <Text size="1" weight="medium">Background</Text>
                <input
                  type="color"
                  value={editBackgroundColor}
                  onChange={(e) => setEditBackgroundColor(e.target.value)}
                  className="w-full h-6 rounded border cursor-pointer"
                />
              </Flex>
            </Flex>

            <Flex direction="column" gap="1">
              <Text size="1" weight="medium">Font Size</Text>
              <Select.Root
                value={editFontSize.toString()}
                onValueChange={(value) => setEditFontSize(Number.parseInt(value))}
                size="1"
              >
                <Select.Trigger />
                <Select.Content>
                  <Select.Item value="12">Small (12px)</Select.Item>
                  <Select.Item value="16">Medium (16px)</Select.Item>
                  <Select.Item value="20">Large (20px)</Select.Item>
                  <Select.Item value="24">Extra Large (24px)</Select.Item>
                </Select.Content>
              </Select.Root>
            </Flex>
          </Flex>
        </Card>
        <Handle type="source" position={Position.Bottom} />
      </>
    );
  }

  // Render normal view
  return (
    <>
      <Handle type="target" position={Position.Top} />
      <Card
        className={`transition-all duration-200 ${
          selected ? 'ring-2 ring-blue-500 shadow-lg' : 'shadow-md hover:shadow-lg'
        }`}
        style={{
          width: '256px', // Equivalent to w-64
          backgroundColor: data.style.backgroundColor || 'transparent',
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <Flex direction="column" gap="3" p="3">
          <Flex justify="between" align="center">
            <Text
              size="3"
              weight="bold"
              style={{
                color: data.style.color,
                fontSize: `${data.style.fontSize}px`
              }}
            >
              {data.title}
            </Text>
            <Flex align="center" gap="1">
              <Badge color={statusColor} size="1">
                {isExpired ? 'Expired' : isRunning ? 'Running' : 'Paused'}
              </Badge>
              {(isHovered || selected) && (
                <Flex gap="1">
                  <IconButton size="1" variant="ghost" onClick={handleEdit}>
                    <Edit2 size={12} />
                  </IconButton>
                  <IconButton size="1" variant="ghost" color="red" onClick={handleDelete}>
                    <Trash2 size={12} />
                  </IconButton>
                </Flex>
              )}
            </Flex>
          </Flex>

          <Text
            size="5"
            weight="bold"
            align="center"
            style={{
              color: data.style.color,
              fontFamily: 'monospace'
            }}
          >
            {timeLeft}
          </Text>

          <Text size="1" color="gray" align="center">
            Ends: {new Date(data.endDate).toLocaleString()}
          </Text>

          <Flex gap="2" justify="center">
            <Button size="1" variant="soft" onClick={handlePlayPause}>
              {isRunning ? <Pause size={12} /> : <Play size={12} />}
              {isRunning ? 'Pause' : 'Start'}
            </Button>
            <Button size="1" variant="soft" onClick={handleReset}>
              <RotateCcw size={12} />
              Reset
            </Button>
          </Flex>
        </Flex>
      </Card>
      <Handle type="source" position={Position.Bottom} />
    </>
  );
}
