'use client';

import { useState, useCallback } from 'react';
import { Card, Flex, Text, Button, IconButton, TextField, Checkbox } from '@radix-ui/themes';
import { Plus, Trash2, Edit2, Check, X } from 'lucide-react';
import { Handle, Position, useReactFlow } from 'reactflow';
import type { NodeProps } from 'reactflow';
import type { TodoListNodeData, TodoItem } from '@/types';

export default function TodoListNode({
  id,
  data,
  selected
}: NodeProps<TodoListNodeData>) {
  const [isHovered, setIsHovered] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [newTaskText, setNewTaskText] = useState('');
  const [editTitle, setEditTitle] = useState(data.title);

  const { setNodes } = useReactFlow();

  const updateNodeData = useCallback((updates: Partial<TodoListNodeData>) => {
    setNodes((nodes) =>
      nodes.map((node) =>
        node.id === id
          ? { ...node, data: { ...node.data, ...updates } }
          : node
      )
    );
  }, [id, setNodes]);

  const handleAddTask = () => {
    if (!newTaskText.trim()) return;

    const newTask: TodoItem = {
      id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      text: newTaskText.trim(),
      completed: false,
      createdAt: new Date()
    };

    const updatedItems = [...data.items, newTask];
    updateNodeData({ items: updatedItems });
    setNewTaskText('');
  };

  const handleDeleteTask = (taskId: string) => {
    const updatedItems = data.items.filter(item => item.id !== taskId);
    updateNodeData({ items: updatedItems });
  };

  const handleToggleTask = (taskId: string) => {
    const updatedItems = data.items.map(item =>
      item.id === taskId
        ? { ...item, completed: !item.completed }
        : item
    );
    updateNodeData({ items: updatedItems });
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSaveEdit = () => {
    updateNodeData({ title: editTitle });
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setEditTitle(data.title);
    setIsEditing(false);
  };

  const handleDelete = () => {
    setNodes((nodes) => nodes.filter((node) => node.id !== id));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddTask();
    }
  };

  const completedCount = data.items.filter(item => item.completed).length;
  const totalCount = data.items.length;

  // Render editing interface
  if (isEditing) {
    return (
      <>
        <Handle type="target" position={Position.Top} />
        <Card
          className={`transition-all duration-200 ${
            selected ? 'ring-2 ring-blue-500 shadow-lg' : 'shadow-md hover:shadow-lg'
          }`}
          style={{
            width: '320px', // Equivalent to w-80
            backgroundColor: data.backgroundColor || '#ffffff',
            border: '2px solid #3b82f6'
          }}
        >
          <Flex direction="column" gap="3" p="3">
            <Flex justify="between" align="center">
              <Text size="2" weight="medium">Edit Todo List</Text>
              <Flex gap="1">
                <IconButton size="1" variant="ghost" onClick={handleSaveEdit}>
                  <Check size={12} />
                </IconButton>
                <IconButton size="1" variant="ghost" onClick={handleCancelEdit}>
                  <X size={12} />
                </IconButton>
              </Flex>
            </Flex>

            <Flex direction="column" gap="2">
              <Text size="1" weight="medium">Title</Text>
              <TextField.Root
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                placeholder="Enter todo list title"
                size="2"
              />
            </Flex>
          </Flex>
        </Card>
        <Handle type="source" position={Position.Bottom} />
      </>
    );
  }

  // Render normal view
  return (
    <>
      <Handle type="target" position={Position.Top} />
      <Card
        className={`transition-all duration-200 ${
          selected ? 'ring-2 ring-blue-500 shadow-lg' : 'shadow-md hover:shadow-lg'
        }`}
        style={{
          width: '320px', // Equivalent to w-80
          backgroundColor: data.backgroundColor || '#ffffff',
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <Flex direction="column" gap="3" p="3">
          {/* Header */}
          <Flex justify="between" align="center">
            <Text
              size="3"
              weight="bold"
              style={{
                color: data.textColor || '#1f2937'
              }}
            >
              {data.title}
            </Text>
            <Flex align="center" gap="1">
              <Text size="1" color="gray">
                {completedCount}/{totalCount}
              </Text>
              {(isHovered || selected) && (
                <Flex gap="1">
                  <IconButton size="1" variant="ghost" onClick={handleEdit}>
                    <Edit2 size={12} />
                  </IconButton>
                  <IconButton size="1" variant="ghost" color="red" onClick={handleDelete}>
                    <Trash2 size={12} />
                  </IconButton>
                </Flex>
              )}
            </Flex>
          </Flex>

          {/* Add new task */}
          <Flex gap="2" align="center">
            <TextField.Root
              value={newTaskText}
              onChange={(e) => setNewTaskText(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Add a new task..."
              size="1"
              style={{ flex: 1 }}
            />
            <IconButton
              size="1"
              variant="soft"
              onClick={handleAddTask}
              disabled={!newTaskText.trim()}
            >
              <Plus size={12} />
            </IconButton>
          </Flex>

          {/* Task list */}
          <Flex direction="column" gap="2" style={{ maxHeight: '300px', overflowY: 'auto' }}>
            {data.items.length === 0 ? (
              <Text size="2" color="gray" align="center" style={{ padding: '20px 0' }}>
                No tasks yet. Add one above!
              </Text>
            ) : (
              data.items.map((item) => (
                <Flex key={item.id} align="center" gap="2" p="2" className="hover:bg-gray-50 rounded">
                  <Checkbox
                    checked={item.completed}
                    onCheckedChange={() => handleToggleTask(item.id)}
                    size="1"
                  />
                  <Text
                    size="2"
                    style={{
                      flex: 1,
                      textDecoration: item.completed ? 'line-through' : 'none',
                      color: item.completed ? '#6b7280' : data.textColor || '#1f2937',
                      opacity: item.completed ? 0.7 : 1
                    }}
                  >
                    {item.text}
                  </Text>
                  <IconButton
                    size="1"
                    variant="ghost"
                    color="red"
                    onClick={() => handleDeleteTask(item.id)}
                  >
                    <Trash2 size={10} />
                  </IconButton>
                </Flex>
              ))
            )}
          </Flex>

          {/* Progress indicator */}
          {totalCount > 0 && (
            <Flex direction="column" gap="1">
              <div
                className="w-full bg-gray-200 rounded-full h-2"
                style={{ backgroundColor: '#e5e7eb' }}
              >
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${(completedCount / totalCount) * 100}%`,
                    backgroundColor: '#3b82f6'
                  }}
                />
              </div>
              <Text size="1" color="gray" align="center">
                {completedCount === totalCount && totalCount > 0
                  ? '🎉 All tasks completed!'
                  : `${Math.round((completedCount / totalCount) * 100)}% complete`
                }
              </Text>
            </Flex>
          )}
        </Flex>
      </Card>
      <Handle type="source" position={Position.Bottom} />
    </>
  );
}
