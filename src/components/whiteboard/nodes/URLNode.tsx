'use client';

import { useState, useCallback } from 'react';
import { Card, Flex, Text, IconButton, Button, TextField } from '@radix-ui/themes';
import { Edit2, Trash2, ExternalLink, Globe, Link2, Check, X } from 'lucide-react';
import { <PERSON>le, Position, useReactFlow } from 'reactflow';
import type { NodeProps } from 'reactflow';
import type { URLNodeData } from '@/types';

export default function URLNode({
  id,
  data,
  selected
}: NodeProps<URLNodeData>) {
  const [isHovered, setIsHovered] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  // Editing state
  const [editTitle, setEditTitle] = useState(data.title);
  const [editUrl, setEditUrl] = useState(data.url);
  const [editDescription, setEditDescription] = useState(data.description || '');

  const { setNodes } = useReactFlow();

  const updateNodeData = useCallback((updates: Partial<URLNodeData>) => {
    setNodes((nodes) =>
      nodes.map((node) =>
        node.id === id
          ? { ...node, data: { ...node.data, ...updates } }
          : node
      )
    );
  }, [id, setNodes]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    updateNodeData({
      title: editTitle,
      url: editUrl,
      description: editDescription,
    });
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditTitle(data.title);
    setEditUrl(data.url);
    setEditDescription(data.description || '');
    setIsEditing(false);
  };

  const handleDelete = () => {
    setNodes((nodes) => nodes.filter((node) => node.id !== id));
  };

  const handleNavigate = () => {
    if (data.url) {
      // Ensure URL has protocol
      const url = data.url.startsWith('http://') || data.url.startsWith('https://') 
        ? data.url 
        : `https://${data.url}`;
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  // Extract domain for favicon
  const getDomain = (url: string) => {
    try {
      const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
      return urlObj.hostname;
    } catch {
      return null;
    }
  };

  const domain = getDomain(data.url);
  const faviconUrl = domain ? `https://www.google.com/s2/favicons?domain=${domain}&sz=32` : null;

  return (
    <>
      <Handle type="target" position={Position.Top} />
      <Card
        className={`url-node nodrag transition-all duration-200 ${
          selected ? 'ring-2 ring-blue-500 shadow-lg' : 'shadow-md hover:shadow-lg'
        }`}
        style={{
          width: '280px',
          backgroundColor: data.backgroundColor || '#ffffff',
        }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Handle type="target" position={Position.Top} />
      <Handle type="source" position={Position.Bottom} />

      <div className="p-3">
        {/* Header */}
        <Flex justify="between" align="center" className="mb-2">
          <Flex align="center" gap="2">
            {faviconUrl ? (
              <img 
                src={faviconUrl} 
                alt="" 
                className="w-5 h-5"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            ) : (
              <Globe size={20} className="text-gray-500" />
            )}
            <Text 
              size="2" 
              weight="medium"
              style={{ color: data.textColor || '#1f2937' }}
            >
              {data.title}
            </Text>
          </Flex>

          {(isHovered || isEditing) && !isEditing && (
            <Flex gap="1">
              <IconButton
                size="1"
                variant="ghost"
                onClick={handleEdit}
                className="cursor-pointer"
              >
                <Edit2 size={14} />
              </IconButton>
              <IconButton
                size="1"
                variant="ghost"
                color="red"
                onClick={handleDelete}
                className="cursor-pointer"
              >
                <Trash2 size={14} />
              </IconButton>
            </Flex>
          )}
        </Flex>

        {/* Content */}
        {isEditing ? (
          <div className="space-y-2">
            <TextField.Root
              value={editTitle}
              onChange={(e) => setEditTitle(e.target.value)}
              placeholder="Title"
              size="1"
            />
            <TextField.Root
              value={editUrl}
              onChange={(e) => setEditUrl(e.target.value)}
              placeholder="URL (e.g., google.com)"
              size="1"
            />
            <TextField.Root
              value={editDescription}
              onChange={(e) => setEditDescription(e.target.value)}
              placeholder="Description (optional)"
              size="1"
            />

            <Flex gap="2" justify="end">
              <Button
                size="1"
                variant="soft"
                color="gray"
                onClick={handleCancel}
              >
                <X size={14} />
                Cancel
              </Button>
              <Button
                size="1"
                onClick={handleSave}
              >
                <Check size={14} />
                Save
              </Button>
            </Flex>
          </div>
        ) : (
          <div>
            <Flex 
              align="center" 
              gap="1" 
              className="mb-1 cursor-pointer hover:text-blue-600"
              onClick={handleNavigate}
            >
              <Link2 size={14} className="text-gray-500" />
              <Text size="1" className="text-gray-600 truncate">
                {data.url}
              </Text>
              <ExternalLink size={12} className="text-gray-400" />
            </Flex>

            {data.description && (
              <Text size="1" className="text-gray-500">
                {data.description}
              </Text>
            )}

            <Button
              size="2"
              variant="soft"
              className="w-full mt-2 cursor-pointer"
              onClick={handleNavigate}
            >
              <ExternalLink size={14} />
              Open Website
            </Button>
          </div>
        )}
      </div>
    </Card>
    <Handle type="source" position={Position.Bottom} />
    </>
  );
}