'use client';

import dynamic from 'next/dynamic';
import { useCallback, useState, useEffect, useRef } from 'react';
import type {
  Node,
  Edge,
  ReactFlowInstance,
  Viewport,
  NodeChange,
  EdgeChange,
  Connection,
  XYPosition
} from 'reactflow';
import { <PERSON>le, Position } from 'reactflow';
import type { WhiteboardData, WhiteboardSettings, ReactFlowData, CustomNode, CustomEdge } from '@/types';
import { transformStoredDataToReactFlow, createDefaultNode, validateReactFlowData, exportReactFlowData } from '@/lib/whiteboard-utils';
import { CountdownTimerNode, GoalNode, HillChartNode, URLNode, TodoListNode } from './nodes';

// Lazy load React Flow components for better performance
const ReactFlow = dynamic(
  async () => {
    const reactFlowModule = await import('reactflow');
    return reactFlowModule.default;
  },
  {
    ssr: false,
    loading: () => (
      <div className="w-full h-full flex items-center justify-center bg-gray-50">
        <div className="text-center space-y-4">
          <div className="relative">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 border-t-blue-600 mx-auto"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-6 h-6 bg-blue-600 rounded-full animate-pulse"></div>
            </div>
          </div>
          <div className="space-y-2">
            <p className="text-gray-700 font-medium">Loading Whiteboard...</p>
            <p className="text-gray-500 text-sm">Preparing your creative workspace</p>
          </div>
          <div className="flex justify-center space-x-1">
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    ),
  }
);

const Controls = dynamic(() => import('reactflow').then(mod => ({ default: mod.Controls })), { ssr: false });
const MiniMap = dynamic(() => import('@reactflow/minimap').then(mod => ({ default: mod.MiniMap })), { ssr: false });
const Background = dynamic(() => import('@reactflow/background').then(mod => ({ default: mod.Background })), { ssr: false });

interface ReactFlowWrapperProps {
  whiteboardData?: WhiteboardData;
  nodes?: Node[];
  edges?: Edge[];
  viewport?: Viewport;
  onSave?: (nodes: Node[], edges: Edge[], viewport: Viewport) => void;
  settings?: WhiteboardSettings;
  className?: string;
  onExport?: (format: 'png' | 'svg' | 'json') => void;
  isReadOnly?: boolean;
}

// Standardized base node wrapper for consistent alignment and styling
const BaseNodeWrapper = ({
  children,
  selected,
  className = '',
  style = {},
  onMouseEnter,
  onMouseLeave,
  width,
  height
}: {
  children: React.ReactNode;
  selected?: boolean;
  className?: string;
  style?: React.CSSProperties;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  width?: number;
  height?: number;
}) => (
  <div
    className={`transition-all duration-200 ${
      selected ? 'ring-2 ring-blue-500 shadow-lg' : 'shadow-md hover:shadow-lg'
    } ${className}`}
    style={{
      width: width ? `${width}px` : undefined,
      height: height ? `${height}px` : undefined,
      ...style
    }}
    onMouseEnter={onMouseEnter}
    onMouseLeave={onMouseLeave}
  >
    {children}
  </div>
);

// Custom node components with standardized alignment
const TextNode = ({ data, selected }: { data: Record<string, unknown>; selected?: boolean }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <>
      <Handle type="target" position={Position.Top} />
      <BaseNodeWrapper
        selected={selected}
        className="px-4 py-2 rounded-md bg-white border-2 border-stone-400"
        style={{
          backgroundColor: (data.backgroundColor as string) || '#ffffff',
          color: (data.color as string) || '#000000',
          fontSize: (data.fontSize as number) || 14
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="font-medium">{data.label as string}</div>
      </BaseNodeWrapper>
      <Handle type="source" position={Position.Bottom} />
    </>
  );
};

const RectangleNode = ({ data, selected }: { data: Record<string, unknown>; selected?: boolean }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <>
      <Handle type="target" position={Position.Top} />
      <BaseNodeWrapper
        selected={selected}
        className="px-6 py-4 rounded-lg border-2"
        style={{
          backgroundColor: (data.backgroundColor as string) || '#f3f4f6',
          borderColor: (data.color as string) || '#6b7280',
          color: (data.color as string) || '#374151',
          fontSize: (data.fontSize as number) || 16
        }}
        width={(data.width as number) || 150}
        height={(data.height as number) || 80}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="font-semibold text-center flex items-center justify-center h-full">
          {data.label as string}
        </div>
      </BaseNodeWrapper>
      <Handle type="source" position={Position.Bottom} />
    </>
  );
};

const CircleNode = ({ data, selected }: { data: Record<string, unknown>; selected?: boolean }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <>
      <Handle type="target" position={Position.Top} />
      <BaseNodeWrapper
        selected={selected}
        className="rounded-full border-2 flex items-center justify-center"
        style={{
          backgroundColor: (data.backgroundColor as string) || '#dbeafe',
          borderColor: (data.color as string) || '#3b82f6',
          color: (data.color as string) || '#1e40af',
          fontSize: (data.fontSize as number) || 14
        }}
        width={(data.width as number) || 100}
        height={(data.height as number) || 100}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="font-medium text-center">{data.label as string}</div>
      </BaseNodeWrapper>
      <Handle type="source" position={Position.Bottom} />
    </>
  );
};

// Node types
const nodeTypes = {
  textNode: TextNode,
  rectangleNode: RectangleNode,
  circleNode: CircleNode,
  countdownTimer: CountdownTimerNode,
  goal: GoalNode,
  hillChart: HillChartNode,
  url: URLNode,
  todoList: TodoListNode,
};

// Default edge types
const edgeTypes = {
  // We can add custom edge types here later
};

export default function ReactFlowWrapper({
  whiteboardData,
  nodes: externalNodes,
  edges: externalEdges,
  viewport: externalViewport,
  onSave,
  settings,
  className = '',
  onExport,
  isReadOnly
}: ReactFlowWrapperProps) {
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);
  const [nodes, setNodes] = useState<Node[]>(externalNodes || []);
  const [edges, setEdges] = useState<Edge[]>(externalEdges || []);
  const [viewport, setViewport] = useState<Viewport>(externalViewport || { x: 0, y: 0, zoom: 1 });
  const [isLoaded, setIsLoaded] = useState(false);
  const [loadTime, setLoadTime] = useState<number | null>(null);
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const loadStartTime = useRef<number>(Date.now());

  // Sync external props with internal state
  useEffect(() => {
    if (externalNodes) {
      console.log('ReactFlowWrapper: Received external nodes:', externalNodes);
      setNodes(externalNodes);
    }
  }, [externalNodes]);

  useEffect(() => {
    if (externalEdges) {
      setEdges(externalEdges);
    }
  }, [externalEdges]);

  useEffect(() => {
    if (externalViewport) {
      setViewport(externalViewport);
    }
  }, [externalViewport]);

  // Load initial data
  useEffect(() => {
    if (whiteboardData?.content && !externalNodes && !externalEdges) {
      const { nodes: loadedNodes, edges: loadedEdges, viewport: loadedViewport } =
        transformStoredDataToReactFlow(JSON.stringify(whiteboardData.content));

      setNodes(loadedNodes);
      setEdges(loadedEdges);
      setViewport(loadedViewport);
    }
  }, [whiteboardData, externalNodes, externalEdges]);

  // Handle node changes
  const onNodesChange = useCallback(async (changes: NodeChange[]) => {
    const { applyNodeChanges } = await import('reactflow');
    setNodes((nds) => applyNodeChanges(changes, nds));
  }, []);

  // Handle edge changes
  const onEdgesChange = useCallback(async (changes: EdgeChange[]) => {
    const { applyEdgeChanges } = await import('reactflow');
    setEdges((eds) => applyEdgeChanges(changes, eds));
  }, []);

  // Handle new connections
  const onConnect = useCallback(async (connection: Connection) => {
    const { addEdge } = await import('reactflow');
    setEdges((eds) => addEdge(connection, eds));
  }, []);

  // Handle viewport changes
  const onViewportChange = useCallback((event: MouseEvent | TouchEvent, viewport: Viewport) => {
    setViewport(viewport);
  }, []);

  // Add new node
  const addNode = useCallback((type: string, position: XYPosition, label = 'New Node') => {
    const newNode = createDefaultNode(type, position, label);
    setNodes((nds) => [...nds, newNode]);
  }, []);

  // Handle canvas click for node creation
  const onPaneClick = useCallback((event: React.MouseEvent) => {
    // Only create node if not in view mode and no modifier keys are pressed
    if (settings?.viewMode === 'view' || event.ctrlKey || event.metaKey || event.shiftKey) {
      return;
    }

    if (reactFlowInstance) {
      const position = reactFlowInstance.screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      });

      // Create a text node by default on double-click
      if (event.detail === 2) {
        addNode('textNode', position, 'Double-click to edit');
      }
    }
  }, [reactFlowInstance, settings?.viewMode, addNode]);

  // Export functionality
  const handleExport = useCallback((format: 'png' | 'svg' | 'json') => {
    if (!reactFlowInstance) return;

    switch (format) {
      case 'json':
        const jsonData = exportReactFlowData(nodes, edges, viewport, 'json');
        const jsonBlob = new Blob([jsonData], { type: 'application/json' });
        const jsonUrl = URL.createObjectURL(jsonBlob);
        const jsonLink = document.createElement('a');
        jsonLink.href = jsonUrl;
        jsonLink.download = 'whiteboard.json';
        jsonLink.click();
        URL.revokeObjectURL(jsonUrl);
        break;

      case 'png':
        // Use React Flow's built-in screenshot functionality
        reactFlowInstance.getViewport();
        const imageWidth = 1920;
        const imageHeight = 1080;

        // Create a canvas element
        const canvas = document.createElement('canvas');
        canvas.width = imageWidth;
        canvas.height = imageHeight;
        const ctx = canvas.getContext('2d');

        if (ctx) {
          // Fill background
          ctx.fillStyle = settings?.backgroundColor || '#ffffff';
          ctx.fillRect(0, 0, imageWidth, imageHeight);

          // Export as PNG
          canvas.toBlob((blob) => {
            if (blob) {
              const url = URL.createObjectURL(blob);
              const link = document.createElement('a');
              link.href = url;
              link.download = 'whiteboard.png';
              link.click();
              URL.revokeObjectURL(url);
            }
          }, 'image/png');
        }
        break;

      case 'svg':
        // Basic SVG export
        const svgContent = `
          <svg width="1920" height="1080" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="${settings?.backgroundColor || '#ffffff'}"/>
            ${nodes.map(node => `
              <g transform="translate(${node.position.x}, ${node.position.y})">
                <rect width="${node.data?.width || 100}" height="${node.data?.height || 50}"
                      fill="${node.data?.backgroundColor || '#ffffff'}"
                      stroke="${node.data?.color || '#000000'}" stroke-width="2"/>
                <text x="${(node.data?.width || 100) / 2}" y="${(node.data?.height || 50) / 2}"
                      text-anchor="middle" dominant-baseline="middle"
                      fill="${node.data?.color || '#000000'}"
                      font-size="${node.data?.fontSize || 14}">
                  ${node.data?.label || ''}
                </text>
              </g>
            `).join('')}
          </svg>
        `;

        const svgBlob = new Blob([svgContent], { type: 'image/svg+xml' });
        const svgUrl = URL.createObjectURL(svgBlob);
        const svgLink = document.createElement('a');
        svgLink.href = svgUrl;
        svgLink.download = 'whiteboard.svg';
        svgLink.click();
        URL.revokeObjectURL(svgUrl);
        break;
    }

    onExport?.(format);
  }, [reactFlowInstance, nodes, edges, viewport, settings, onExport]);

  // Auto-save functionality with debouncing
  useEffect(() => {
    if (onSave && isLoaded) {
      // Clear existing timeout
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }

      // Set new timeout
      saveTimeoutRef.current = setTimeout(() => {
        onSave(nodes, edges, viewport);
        saveTimeoutRef.current = null;
      }, 1000);
    }
  }, [nodes, edges, viewport, onSave, isLoaded]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);

  // Performance monitoring
  const handleReactFlowInit = useCallback((instance: ReactFlowInstance) => {
    setReactFlowInstance(instance);
    if (!isLoaded) {
      const endTime = Date.now();
      const loadDuration = endTime - loadStartTime.current;
      setLoadTime(loadDuration);
      setIsLoaded(true);

      // Performance logging (development only)
      if (process.env.NODE_ENV === 'development') {
        console.log(`React Flow loaded in ${loadDuration}ms`);
      }

      // Send performance metrics to analytics (production)
      if (process.env.NODE_ENV === 'production' && typeof window !== 'undefined') {
        try {
          const windowWithGtag = window as Window & {
            gtag?: (command: string, action: string, parameters: Record<string, unknown>) => void;
          };
          if (windowWithGtag.gtag) {
            windowWithGtag.gtag('event', 'reactflow_load_time', {
              event_category: 'Performance',
              event_label: 'React Flow Load Time',
              value: loadDuration
            });
          }
        } catch (error) {
          console.warn('Failed to send performance metrics:', error);
        }
      }
    }
  }, [isLoaded]);

  return (
    <div className={`w-full h-full ${className}`}>
      {/* Performance indicator (development only) */}
      {process.env.NODE_ENV === 'development' && loadTime && (
        <div className="absolute top-2 right-2 z-50 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
          Loaded in {loadTime}ms
        </div>
      )}

      {/* Node creation toolbar */}
      {settings?.viewMode !== 'view' && !isReadOnly && (
        <div className="absolute top-4 left-4 z-40 flex gap-2 bg-white rounded-lg shadow-lg p-2 border">
          <button
            onClick={() => {
              if (reactFlowInstance) {
                const center = reactFlowInstance.getViewport();
                addNode('textNode', { x: -center.x + 100, y: -center.y + 100 }, 'Text');
              }
            }}
            className="px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            title="Add Text Node"
          >
            Text
          </button>
          <button
            onClick={() => {
              if (reactFlowInstance) {
                const center = reactFlowInstance.getViewport();
                addNode('rectangleNode', { x: -center.x + 200, y: -center.y + 100 }, 'Rectangle');
              }
            }}
            className="px-3 py-2 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            title="Add Rectangle Node"
          >
            Rectangle
          </button>
          <button
            onClick={() => {
              if (reactFlowInstance) {
                const center = reactFlowInstance.getViewport();
                addNode('circleNode', { x: -center.x + 300, y: -center.y + 100 }, 'Circle');
              }
            }}
            className="px-3 py-2 text-sm bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
            title="Add Circle Node"
          >
            Circle
          </button>
          <button
            onClick={() => {
              if (reactFlowInstance) {
                const center = reactFlowInstance.getViewport();
                const defaultTimer = {
                  title: 'New Timer',
                  endDate: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour from now
                  isActive: false,
                  style: {
                    backgroundColor: '#fef3c7',
                    color: '#92400e',
                    fontSize: 14
                  }
                };
                const newNode = createDefaultNode('countdownTimer', { x: -center.x + 400, y: -center.y + 100 }, defaultTimer);
                setNodes(prev => [...prev, newNode]);
              }
            }}
            className="px-3 py-2 text-sm bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
            title="Add Countdown Timer"
          >
            Timer
          </button>
          <button
            onClick={() => {
              if (reactFlowInstance) {
                const center = reactFlowInstance.getViewport();
                const defaultGoal = {
                  title: 'New Goal',
                  description: 'Goal description',
                  targetValue: 100,
                  currentValue: 0,
                  unit: 'units',
                  deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week from now
                  priority: 'medium' as const,
                  status: 'not_started' as const,
                  style: {
                    backgroundColor: '#dbeafe',
                    color: '#1e40af',
                    fontSize: 14
                  }
                };
                const newNode = createDefaultNode('goal', { x: -center.x + 500, y: -center.y + 100 }, defaultGoal);
                setNodes(prev => [...prev, newNode]);
              }
            }}
            className="px-3 py-2 text-sm bg-indigo-500 text-white rounded hover:bg-indigo-600 transition-colors"
            title="Add Goal"
          >
            Goal
          </button>
          <button
            onClick={() => {
              if (reactFlowInstance) {
                const center = reactFlowInstance.getViewport();
                const defaultHillChart = {
                  title: 'New Hill Chart',
                  items: [],
                  style: {
                    backgroundColor: '#f0fdf4',
                    color: '#166534',
                    fontSize: 14
                  }
                };
                const newNode = createDefaultNode('hillChart', { x: -center.x + 600, y: -center.y + 100 }, defaultHillChart);
                setNodes(prev => [...prev, newNode]);
              }
            }}
            className="px-3 py-2 text-sm bg-emerald-500 text-white rounded hover:bg-emerald-600 transition-colors"
            title="Add Hill Chart"
          >
            Hill Chart
          </button>
          <button
            onClick={() => {
              if (reactFlowInstance) {
                const center = reactFlowInstance.getViewport();
                const defaultUrl = {
                  title: 'New Link',
                  url: 'https://example.com',
                  description: 'Link description',
                  style: {
                    backgroundColor: '#fef2f2',
                    color: '#991b1b',
                    fontSize: 14
                  }
                };
                const newNode = createDefaultNode('url', { x: -center.x + 700, y: -center.y + 100 }, defaultUrl);
                setNodes(prev => [...prev, newNode]);
              }
            }}
            className="px-3 py-2 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
            title="Add URL Link"
          >
            URL
          </button>
          <button
            onClick={() => {
              if (reactFlowInstance) {
                const center = reactFlowInstance.getViewport();
                const defaultTodoList = {
                  title: 'New Todo List',
                  items: [],
                  backgroundColor: '#ffffff',
                  textColor: '#1f2937',
                  maxItems: 20
                };
                const newNode = createDefaultNode('todoList', { x: -center.x + 800, y: -center.y + 100 }, defaultTodoList);
                setNodes(prev => [...prev, newNode]);
              }
            }}
            className="px-3 py-2 text-sm bg-slate-500 text-white rounded hover:bg-slate-600 transition-colors"
            title="Add Todo List"
          >
            Todo
          </button>
        </div>
      )}

      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={isReadOnly ? undefined : onNodesChange}
        onEdgesChange={isReadOnly ? undefined : onEdgesChange}
        onConnect={isReadOnly ? undefined : onConnect}
        onInit={handleReactFlowInit}
        onMove={onViewportChange}
        onPaneClick={isReadOnly ? undefined : onPaneClick}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        attributionPosition="bottom-left"
        className={settings?.theme === 'dark' ? 'dark' : ''}
        snapToGrid={settings?.snapToGrid}
        snapGrid={settings?.snapToGrid ? [20, 20] : undefined}
        deleteKeyCode={isReadOnly ? undefined : "Delete"}
        multiSelectionKeyCode={isReadOnly ? undefined : "Shift"}
        panOnDrag={true}
        zoomOnScroll={true}
        zoomOnPinch={true}
        panOnScroll={false}
        preventScrolling={true}
        nodesDraggable={!isReadOnly}
        nodesConnectable={!isReadOnly}
        nodesFocusable={!isReadOnly}
        edgesFocusable={!isReadOnly}
        elementsSelectable={!isReadOnly}
      >
        <Controls />
        <MiniMap />
        <Background
          color={settings?.theme === 'dark' ? '#374151' : '#e5e7eb'}
          gap={settings?.gridMode ? 20 : undefined}
        />
      </ReactFlow>
    </div>
  );
}
