'use client';

import { useState } from 'react';
import { Flex, Text, Switch, Tooltip } from '@radix-ui/themes';
import { Globe, Lock, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { useUpdateWhiteboard } from '@/hooks/useWhiteboardData';

interface VisibilityToggleProps {
  whiteboardId: string;
  isPublic: boolean;
  disabled?: boolean;
  size?: 'sm' | 'md';
}

export default function VisibilityToggle({
  whiteboardId,
  isPublic,
  disabled = false,
  size = 'md'
}: VisibilityToggleProps) {
  const [isToggling, setIsToggling] = useState(false);
  const updateWhiteboard = useUpdateWhiteboard();

  const handleToggleVisibility = async (newIsPublic: boolean) => {
    if (disabled || isToggling) return;

    setIsToggling(true);

    try {
      await updateWhiteboard.mutateAsync({
        id: whiteboardId,
        isPublic: newIsPublic,
        // Ensure proper public access settings for anonymous users
        requiresAuth: newIsPublic ? false : true,
        allowAnonymousView: newIsPublic ? true : false,
        publicAccessLevel: newIsPublic ? 'view' : 'none',
      });

      toast.success(
        newIsPublic 
          ? 'Whiteboard is now public - anyone can view it' 
          : 'Whiteboard is now private - only you can access it'
      );
    } catch (error) {
      toast.error('Failed to update whiteboard visibility. Please try again.');
      console.error('Error updating whiteboard visibility:', error);
    } finally {
      setIsToggling(false);
    }
  };

  const iconSize = size === 'sm' ? 12 : 14;
  const textSize = size === 'sm' ? '1' : '2';

  return (
    <Tooltip content={isPublic ? 'Make private' : 'Make public'}>
      <Flex align="center" gap="2" className="cursor-pointer">
        {isToggling ? (
          <Loader2 size={iconSize} className="animate-spin text-gray-500" />
        ) : isPublic ? (
          <Globe size={iconSize} className="text-blue-600" />
        ) : (
          <Lock size={iconSize} className="text-gray-500" />
        )}
        
        <Switch
          checked={isPublic}
          onCheckedChange={handleToggleVisibility}
          disabled={disabled || isToggling}
          size={size === 'sm' ? '1' : '2'}
        />
        
        <Text size={textSize} color={isPublic ? 'blue' : 'gray'}>
          {isPublic ? 'Public' : 'Private'}
        </Text>
      </Flex>
    </Tooltip>
  );
}
