'use client';

import { useState, useEffect } from 'react';
import { Dialog, <PERSON>lex, Text, TextField, Button } from '@radix-ui/themes';
import { useUpdateWhiteboardTitle } from '@/hooks/useWhiteboardData';

interface TitleEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  whiteboardId: string;
  currentTitle: string;
}

export default function TitleEditModal({
  isOpen,
  onClose,
  whiteboardId,
  currentTitle,
}: TitleEditModalProps) {
  const [title, setTitle] = useState(currentTitle);
  const [error, setError] = useState<string | null>(null);
  
  const updateTitleMutation = useUpdateWhiteboardTitle();

  // Reset form when modal opens/closes or current title changes
  useEffect(() => {
    if (isOpen) {
      setTitle(currentTitle);
      setError(null);
    }
  }, [isOpen, currentTitle]);

  const validateTitle = (value: string): string | null => {
    if (!value.trim()) {
      return 'Title cannot be empty';
    }
    if (value.length > 100) {
      return 'Title must be less than 100 characters';
    }
    return null;
  };

  const handleSave = async () => {
    const trimmedTitle = title.trim();
    const validationError = validateTitle(trimmedTitle);
    
    if (validationError) {
      setError(validationError);
      return;
    }

    // Don't save if title hasn't changed
    if (trimmedTitle === currentTitle) {
      onClose();
      return;
    }

    try {
      await updateTitleMutation.mutateAsync({
        id: whiteboardId,
        title: trimmedTitle,
      });
      onClose();
    } catch (error) {
      // Error handling is done in the mutation hook with toast
      console.error('Failed to update title:', error);
    }
  };

  const handleCancel = () => {
    setTitle(currentTitle);
    setError(null);
    onClose();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTitle = e.target.value;
    setTitle(newTitle);
    
    // Clear error when user starts typing
    if (error) {
      setError(null);
    }
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && handleCancel()}>
      <Dialog.Content style={{ maxWidth: 450 }}>
        <Dialog.Title>Edit Whiteboard Title</Dialog.Title>
        <Dialog.Description size="2" mb="4">
          Update the title for your whiteboard.
        </Dialog.Description>

        <Flex direction="column" gap="3">
          <Flex direction="column" gap="2">
            <Text size="2" weight="medium">
              Title
            </Text>
            <TextField.Root
              value={title}
              onChange={handleTitleChange}
              onKeyDown={handleKeyDown}
              placeholder="Enter whiteboard title"
              autoFocus
              disabled={updateTitleMutation.isPending}
            />
            {error && (
              <Text size="2" color="red">
                {error}
              </Text>
            )}
          </Flex>

          <Flex gap="3" mt="4" justify="end">
            <Dialog.Close>
              <Button 
                variant="soft" 
                color="gray"
                disabled={updateTitleMutation.isPending}
              >
                Cancel
              </Button>
            </Dialog.Close>
            <Button 
              onClick={handleSave}
              disabled={updateTitleMutation.isPending || !!error}
            >
              {updateTitleMutation.isPending ? 'Saving...' : 'Save'}
            </Button>
          </Flex>
        </Flex>
      </Dialog.Content>
    </Dialog.Root>
  );
}
