'use client';

import { useEffect, useState, useMemo } from 'react';
import { Container, Flex, Text, Card } from '@radix-ui/themes';
import { Palette, Zap, Users, Bot } from 'lucide-react';

interface WhiteboardPreloaderProps {
  onComplete?: () => void;
}

export default function WhiteboardPreloader({ onComplete }: WhiteboardPreloaderProps) {
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  const steps = useMemo(() => [
    { icon: Palette, label: 'Loading drawing tools', duration: 800 },
    { icon: Zap, label: 'Initializing AI features', duration: 600 },
    { icon: Users, label: 'Setting up collaboration', duration: 500 },
    { icon: Bot, label: 'Preparing workspace', duration: 400 }
  ], []);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    let progressInterval: NodeJS.Timeout;

    const loadStep = (stepIndex: number) => {
      if (stepIndex >= steps.length) {
        setProgress(100);
        setIsComplete(true);
        setTimeout(() => {
          onComplete?.();
        }, 500);
        return;
      }

      setCurrentStep(stepIndex);
      const step = steps[stepIndex];
      const startProgress = (stepIndex / steps.length) * 100;
      const endProgress = ((stepIndex + 1) / steps.length) * 100;

      let currentProgress = startProgress;
      const progressIncrement = (endProgress - startProgress) / (step.duration / 50);

      progressInterval = setInterval(() => {
        currentProgress += progressIncrement;
        if (currentProgress >= endProgress) {
          currentProgress = endProgress;
          clearInterval(progressInterval);
        }
        setProgress(Math.min(currentProgress, 100));
      }, 50);

      timeoutId = setTimeout(() => {
        clearInterval(progressInterval);
        loadStep(stepIndex + 1);
      }, step.duration);
    };

    // Start loading after a brief delay
    const initialDelay = setTimeout(() => {
      loadStep(0);
    }, 300);

    return () => {
      clearTimeout(initialDelay);
      clearTimeout(timeoutId);
      clearInterval(progressInterval);
    };
  }, [onComplete, steps]);

  const CurrentIcon = currentStep < steps.length ? steps[currentStep].icon : Bot;

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center z-50">
      <Container size="2">
        <Card className="p-8 shadow-xl border-0 bg-white/90 backdrop-blur-sm">
          <Flex direction="column" gap="6" align="center">
            <div className="text-center">
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
                Anchorboard
              </h1>
              <Text size="2" color="gray">
                Preparing your creative workspace
              </Text>
            </div>

            <div className="w-full space-y-3">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-blue-600 to-purple-600 h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${progress}%` }}
                />
              </div>
              <Text size="2" color="gray" align="center">
                {Math.round(progress)}% complete
              </Text>
            </div>

            <div className="text-center space-y-4">
              {!isComplete ? (
                <>
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                    <CurrentIcon className="w-8 h-8 text-blue-600" />
                  </div>
                  <Text size="3" weight="medium">
                    {currentStep < steps.length ? steps[currentStep].label : 'Almost ready...'}
                  </Text>
                </>
              ) : (
                <>
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                    <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                      <div className="w-3 h-3 bg-white rounded-full" />
                    </div>
                  </div>
                  <Text size="3" weight="medium" color="green">
                    Ready to create!
                  </Text>
                </>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4 w-full mt-4">
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <Palette className="w-5 h-5 text-blue-600 mx-auto mb-1" />
                <Text size="1" color="gray">Drawing Tools</Text>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <Bot className="w-5 h-5 text-purple-600 mx-auto mb-1" />
                <Text size="1" color="gray">AI Assistant</Text>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <Users className="w-5 h-5 text-green-600 mx-auto mb-1" />
                <Text size="1" color="gray">Collaboration</Text>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <Zap className="w-5 h-5 text-orange-600 mx-auto mb-1" />
                <Text size="1" color="gray">Real-time Sync</Text>
              </div>
            </div>

            <div className="text-center">
              <Text size="1" color="gray">
                💡 Tip: Use Ctrl+Z to undo and Ctrl+Y to redo
              </Text>
            </div>
          </Flex>
        </Card>
      </Container>
    </div>
  );
}
