'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';

declare global {
  interface Window {
    gtag: (...args: unknown[]) => void;
    dataLayer: unknown[];
  }
}

interface SEOAnalyticsProps {
  trackingId?: string;
  enableDebug?: boolean;
}

export default function SEOAnalytics({ 
  trackingId = 'G-5CE5MRRH0Q', 
  enableDebug = false 
}: SEOAnalyticsProps) {
  const pathname = usePathname();

  useEffect(() => {
    // Track page views
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', trackingId, {
        page_path: pathname,
        custom_map: {
          custom_parameter_1: 'seo_source'
        }
      });

      // Track SEO-specific events
      window.gtag('event', 'page_view', {
        page_title: document.title,
        page_location: window.location.href,
        page_path: pathname,
        content_group1: 'SEO_Optimized_Page'
      });

      if (enableDebug) {
        console.log('SEO Analytics: Page view tracked', {
          path: pathname,
          title: document.title
        });
      }
    }
  }, [pathname, trackingId, enableDebug]);

  // Track Core Web Vitals
  useEffect(() => {
    if (typeof window !== 'undefined' && 'web-vital' in window) {
      // This would integrate with web-vitals library
      // For now, we'll track basic performance metrics
      
      const trackWebVital = (metric: { name: string; id: string; value: number }) => {
        if (window.gtag) {
          window.gtag('event', metric.name, {
            event_category: 'Web Vitals',
            event_label: metric.id,
            value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
            non_interaction: true,
          });
        }
      };

      // Track when page is fully loaded
      window.addEventListener('load', () => {
        // Simulate Core Web Vitals tracking
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation && window.gtag) {
          // Track loading performance
          window.gtag('event', 'page_load_time', {
            event_category: 'Performance',
            value: Math.round(navigation.loadEventEnd - navigation.fetchStart),
            non_interaction: true,
          });

          // Track First Contentful Paint
          const paintEntries = performance.getEntriesByType('paint');
          const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
          
          if (fcp) {
            window.gtag('event', 'first_contentful_paint', {
              event_category: 'Web Vitals',
              value: Math.round(fcp.startTime),
              non_interaction: true,
            });
          }
        }
      });
    }
  }, []);

  // Track SEO-specific interactions
  useEffect(() => {
    const trackSEOInteractions = () => {
      // Track clicks on internal links
      document.addEventListener('click', (event) => {
        const target = event.target as HTMLElement;
        const link = target.closest('a');
        
        if (link && link.href && window.gtag) {
          const isInternal = link.href.startsWith(window.location.origin);
          const isExternal = link.href.startsWith('http') && !isInternal;
          
          if (isInternal) {
            window.gtag('event', 'internal_link_click', {
              event_category: 'SEO',
              event_label: link.href,
              value: 1
            });
          } else if (isExternal) {
            window.gtag('event', 'external_link_click', {
              event_category: 'SEO',
              event_label: link.href,
              value: 1
            });
          }
        }
      });

      // Track scroll depth for content engagement
      let maxScroll = 0;
      const trackScrollDepth = () => {
        const scrollPercent = Math.round(
          (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
        );
        
        if (scrollPercent > maxScroll && scrollPercent % 25 === 0) {
          maxScroll = scrollPercent;
          if (window.gtag) {
            window.gtag('event', 'scroll_depth', {
              event_category: 'Engagement',
              event_label: `${scrollPercent}%`,
              value: scrollPercent
            });
          }
        }
      };

      window.addEventListener('scroll', trackScrollDepth, { passive: true });
    };

    trackSEOInteractions();
  }, []);

  return null; // This component doesn't render anything
}

// Hook for tracking custom SEO events
export function useSEOTracking() {
  const trackSEOEvent = (eventName: string, parameters: Record<string, unknown> = {}) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', eventName, {
        event_category: 'SEO',
        ...parameters
      });
    }
  };

  const trackConversion = (conversionType: string, value?: number) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'conversion', {
        event_category: 'SEO_Conversion',
        event_label: conversionType,
        value: value || 1
      });
    }
  };

  const trackFeatureUsage = (feature: string, action: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'feature_usage', {
        event_category: 'Product',
        event_label: `${feature}_${action}`,
        value: 1
      });
    }
  };

  return {
    trackSEOEvent,
    trackConversion,
    trackFeatureUsage
  };
}
