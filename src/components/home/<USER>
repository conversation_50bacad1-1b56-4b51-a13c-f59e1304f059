'use client';

import React from 'react';
import Link from 'next/link';
import { Card, Flex, Text, Button } from '@radix-ui/themes';
import { FileText, Clock, User, ArrowRight } from 'lucide-react';
import { useRecentPublicWhiteboards } from '@/hooks/useWhiteboardData';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

export default function RecentPublicWhiteboards() {
  const { data, isLoading, error } = useRecentPublicWhiteboards(10);

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner size="md" text="Loading recent whiteboards..." />
      </div>
    );
  }

  if (error) {
    return null; // Fail silently on homepage
  }

  if (!data?.whiteboards || data.whiteboards.length === 0) {
    return null; // Don't show section if no public whiteboards
  }

  return (
    <section className="w-full max-w-6xl">
      <h2 className="text-3xl font-bold mb-8 text-center">Recently Updated Public Whiteboards</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
        {data.whiteboards.slice(0, 9).map((whiteboard) => (
          <Link
            key={whiteboard.id}
            href={`/whiteboard/${whiteboard.id}`}
            className="no-underline"
          >
            <Card className="p-4 hover:shadow-lg transition-all duration-200 hover:-translate-y-1 h-full">
              <Flex direction="column" gap="3" className="h-full">
                <Flex align="center" gap="2">
                  <FileText size={20} className="text-blue-600" />
                  <Text size="3" weight="medium" className="line-clamp-1">
                    {whiteboard.title}
                  </Text>
                </Flex>
                
                <Flex direction="column" gap="2" className="flex-grow">
                  <Flex align="center" gap="2">
                    <User size={14} className="text-gray-500" />
                    <Text size="2" color="gray">
                      {whiteboard.user.name || whiteboard.user.email.split('@')[0]}
                    </Text>
                  </Flex>
                  
                  <Flex align="center" gap="2">
                    <Clock size={14} className="text-gray-500" />
                    <Text size="2" color="gray">
                      {formatTimeAgo(new Date(whiteboard.updatedAt))}
                    </Text>
                  </Flex>
                </Flex>
                
                <Flex justify="end">
                  <Text size="2" color="blue" weight="medium">
                    View →
                  </Text>
                </Flex>
              </Flex>
            </Card>
          </Link>
        ))}
      </div>

      {data.whiteboards.length > 9 && (
        <Flex justify="center">
          <Link href="/explore">
            <Button size="3" variant="soft">
              View All Public Whiteboards
              <ArrowRight size={16} className="ml-2" />
            </Button>
          </Link>
        </Flex>
      )}
    </section>
  );
}

function formatTimeAgo(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} ${days === 1 ? 'day' : 'days'} ago`;
  } else {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  }
}