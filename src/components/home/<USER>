'use client';

import { useAuth } from '@/hooks/useAuth';
import { Button } from '@radix-ui/themes';
import { ArrowRight } from 'lucide-react';
import Link from 'next/link';

export default function AuthenticatedSection() {
  const { data: session, status } = useAuth();

  // Don't show anything if user is not authenticated
  if (status !== 'authenticated' || !session?.user) {
    return null;
  }

  return (
    <div className="w-full max-w-md">
      <Link href="/whiteboard">
        <Button size="3" variant="soft" className="w-full">
          <ArrowRight size={18} />
          Go to My Whiteboard
        </Button>
      </Link>
    </div>
  );
}