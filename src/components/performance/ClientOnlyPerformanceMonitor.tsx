'use client';

import { useEffect, useState } from 'react';
import PerformanceMonitor from './PerformanceMonitor';

interface ClientOnlyPerformanceMonitorProps {
  enableConsoleLog?: boolean;
}

export default function ClientOnlyPerformanceMonitor({ 
  enableConsoleLog = false 
}: ClientOnlyPerformanceMonitorProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return null;
  }

  return <PerformanceMonitor enableConsoleLog={enableConsoleLog} />;
}
