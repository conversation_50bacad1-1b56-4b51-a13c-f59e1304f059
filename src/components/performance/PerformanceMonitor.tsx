'use client';

import { useEffect, useRef } from 'react';

interface PerformanceMetrics {
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  ttfb?: number; // Time to First Byte
}

interface PerformanceMonitorProps {
  onMetrics?: (metrics: PerformanceMetrics) => void;
  enableConsoleLog?: boolean;
}

// Type definitions for performance APIs

interface MemoryInfo {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
}

interface PerformanceWithMemory extends Performance {
  memory?: MemoryInfo;
}

interface LayoutShiftEntry extends PerformanceEntry {
  value: number;
  hadRecentInput: boolean;
}

interface FirstInputEntry extends PerformanceEntry {
  processingStart: number;
}

export default function PerformanceMonitor({
  onMetrics,
  enableConsoleLog = false
}: PerformanceMonitorProps) {
  const metricsRef = useRef<PerformanceMetrics>({});

  useEffect(() => {
    // 确保只在客户端运行
    if (typeof window === 'undefined') return;

    const reportMetric = (name: string, value: number) => {
      metricsRef.current = { ...metricsRef.current, [name]: value };
      
      if (enableConsoleLog) {
        console.log(`Performance Metric - ${name}:`, value);
      }
      
      onMetrics?.(metricsRef.current);
      
      // 发送到分析服务
      if (process.env.NODE_ENV === 'production') {
        try {
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              event_category: 'Performance',
              event_label: name,
              value: Math.round(value),
              custom_parameter_1: navigator.userAgent,
              custom_parameter_2: window.location.pathname
            });
          }
        } catch (error) {
          console.warn('Failed to send performance metrics:', error);
        }
      }
    };

    // 监控 Web Vitals
    const observeWebVitals = () => {
      // First Contentful Paint
      const fcpObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name === 'first-contentful-paint') {
            reportMetric('fcp', entry.startTime);
          }
        }
      });
      fcpObserver.observe({ entryTypes: ['paint'] });

      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        reportMetric('lcp', lastEntry.startTime);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const fidEntry = entry as FirstInputEntry;
          reportMetric('fid', fidEntry.processingStart - fidEntry.startTime);
        }
      });
      fidObserver.observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const clsEntry = entry as LayoutShiftEntry;
          if (!clsEntry.hadRecentInput) {
            clsValue += clsEntry.value;
          }
        }
        reportMetric('cls', clsValue);
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });

      // Time to First Byte
      const navigationEntries = performance.getEntriesByType('navigation');
      if (navigationEntries.length > 0) {
        const navEntry = navigationEntries[0] as PerformanceNavigationTiming;
        reportMetric('ttfb', navEntry.responseStart - navEntry.requestStart);
      }

      return () => {
        fcpObserver.disconnect();
        lcpObserver.disconnect();
        fidObserver.disconnect();
        clsObserver.disconnect();
      };
    };

    // 监控资源加载性能
    const observeResourceTiming = () => {
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const resource = entry as PerformanceResourceTiming;
          
          // 监控关键资源
          if (resource.name.includes('excalidraw') || 
              resource.name.includes('chunk') ||
              resource.name.includes('.js') ||
              resource.name.includes('.css')) {
            
            const loadTime = resource.responseEnd - resource.requestStart;
            
            if (enableConsoleLog) {
              console.log(`Resource Load Time - ${resource.name}:`, loadTime);
            }
            
            // 发送资源性能数据
            if (process.env.NODE_ENV === 'production') {
              try {
                if (window.gtag) {
                  window.gtag('event', 'resource_timing', {
                    event_category: 'Performance',
                    event_label: resource.name.split('/').pop(),
                    value: Math.round(loadTime)
                  });
                }
              } catch (error) {
                console.warn('Failed to send resource timing:', error);
              }
            }
          }
        }
      });
      
      resourceObserver.observe({ entryTypes: ['resource'] });
      
      return () => resourceObserver.disconnect();
    };

    // 监控内存使用情况
    const monitorMemoryUsage = () => {
      const performanceWithMemory = performance as PerformanceWithMemory;
      if (performanceWithMemory.memory) {
        const memoryInfo = performanceWithMemory.memory;

        if (enableConsoleLog) {
          console.log('Memory Usage:', {
            used: Math.round(memoryInfo.usedJSHeapSize / 1048576) + ' MB',
            total: Math.round(memoryInfo.totalJSHeapSize / 1048576) + ' MB',
            limit: Math.round(memoryInfo.jsHeapSizeLimit / 1048576) + ' MB'
          });
        }

        // 定期检查内存使用情况
        const memoryInterval = setInterval(() => {
          const currentPerformance = performance as PerformanceWithMemory;
          if (currentPerformance.memory) {
            const currentMemory = currentPerformance.memory;
            const usedMB = Math.round(currentMemory.usedJSHeapSize / 1048576);

            // 如果内存使用超过阈值，发出警告
            if (usedMB > 100) { // 100MB threshold
              console.warn(`High memory usage detected: ${usedMB}MB`);

              if (process.env.NODE_ENV === 'production') {
                try {
                  if (window.gtag) {
                    window.gtag('event', 'high_memory_usage', {
                      event_category: 'Performance',
                      event_label: 'Memory Warning',
                      value: usedMB
                    });
                  }
                } catch (error) {
                  console.warn('Failed to send memory warning:', error);
                }
              }
            }
          }
        }, 30000); // Check every 30 seconds

        return () => clearInterval(memoryInterval);
      }

      return () => {};
    };

    const cleanup1 = observeWebVitals();
    const cleanup2 = observeResourceTiming();
    const cleanup3 = monitorMemoryUsage();

    return () => {
      cleanup1();
      cleanup2();
      cleanup3();
    };
  }, [onMetrics, enableConsoleLog]);

  // 这个组件不渲染任何内容
  return null;
}
