'use client';

import { useState } from 'react';
import { Button, Card, Flex, Text, TextField, TextArea, Select } from '@radix-ui/themes';
import type { Goal } from '@/types';

interface GoalEditorProps {
  goal?: Partial<Goal>;
  onSave: (goal: Omit<Goal, 'id' | 'whiteboardId'>) => void;
  onCancel: () => void;
  position: { x: number; y: number };
}

export default function GoalEditor({
  goal,
  onSave,
  onCancel,
  position
}: GoalEditorProps) {
  const [title, setTitle] = useState(goal?.title || '');
  const [description, setDescription] = useState(goal?.description || '');
  const [dueDate, setDueDate] = useState(
    goal?.dueDate ? new Date(goal.dueDate).toISOString().slice(0, 10) : ''
  );
  const [priority, setPriority] = useState<Goal['priority']>(goal?.priority || 'medium');
  const [status, setStatus] = useState<Goal['status']>(goal?.status || 'not-started');
  const [tags, setTags] = useState(goal?.tags?.join(', ') || '');

  const handleSave = () => {
    if (!title.trim()) {
      alert('Please enter a goal title');
      return;
    }

    const newGoal: Omit<Goal, 'id' | 'whiteboardId'> = {
      title: title.trim(),
      description: description.trim() || undefined,
      dueDate: dueDate ? new Date(dueDate) : undefined,
      priority,
      status,
      position,
      tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
    };

    onSave(newGoal);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-96 max-h-[90vh] overflow-y-auto">
        <Flex direction="column" gap="4" p="4">
          <Text size="4" weight="bold">
            {goal ? 'Edit Goal' : 'Create New Goal'}
          </Text>

          <Flex direction="column" gap="2">
            <Text size="2" weight="medium">Goal Title *</Text>
            <TextField.Root
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter goal title"
            />
          </Flex>

          <Flex direction="column" gap="2">
            <Text size="2" weight="medium">Description</Text>
            <TextArea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter goal description (optional)"
              rows={3}
            />
          </Flex>

          <Flex gap="3">
            <Flex direction="column" gap="2" flexGrow="1">
              <Text size="2" weight="medium">Priority</Text>
              <Select.Root value={priority} onValueChange={(value: Goal['priority']) => setPriority(value)}>
                <Select.Trigger />
                <Select.Content>
                  <Select.Item value="low">🟢 Low</Select.Item>
                  <Select.Item value="medium">🟡 Medium</Select.Item>
                  <Select.Item value="high">🔴 High</Select.Item>
                </Select.Content>
              </Select.Root>
            </Flex>

            <Flex direction="column" gap="2" flexGrow="1">
              <Text size="2" weight="medium">Status</Text>
              <Select.Root value={status} onValueChange={(value: Goal['status']) => setStatus(value)}>
                <Select.Trigger />
                <Select.Content>
                  <Select.Item value="not-started">Not Started</Select.Item>
                  <Select.Item value="in-progress">In Progress</Select.Item>
                  <Select.Item value="completed">Completed</Select.Item>
                </Select.Content>
              </Select.Root>
            </Flex>
          </Flex>

          <Flex direction="column" gap="2">
            <Text size="2" weight="medium">Due Date</Text>
            <input
              type="date"
              value={dueDate}
              onChange={(e) => setDueDate(e.target.value)}
              min={new Date().toISOString().slice(0, 10)}
              className="px-3 py-2 border rounded-md"
            />
          </Flex>

          <Flex direction="column" gap="2">
            <Text size="2" weight="medium">Tags</Text>
            <TextField.Root
              value={tags}
              onChange={(e) => setTags(e.target.value)}
              placeholder="Enter tags separated by commas"
            />
            <Text size="1" color="gray">
              Example: frontend, urgent, Q1-goals
            </Text>
          </Flex>

          <Flex gap="2" justify="end" mt="4">
            <Button variant="soft" onClick={onCancel}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              {goal ? 'Update Goal' : 'Create Goal'}
            </Button>
          </Flex>
        </Flex>
      </Card>
    </div>
  );
}
