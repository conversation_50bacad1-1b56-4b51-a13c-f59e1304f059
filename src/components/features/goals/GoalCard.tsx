'use client';

import { useState } from 'react';
import { Card, Flex, Text, Button, Badge, IconButton } from '@radix-ui/themes';
import { Calendar, Tag, Edit2, Trash2, CheckCircle } from 'lucide-react';
import type { Goal } from '@/types';

interface GoalCardProps {
  goal: Goal;
  onUpdate?: (goal: Goal) => void;
  onDelete?: (id: string) => void;
  onEdit?: (goal: Goal) => void;
  isEditable?: boolean;
}

export default function GoalCard({
  goal,
  onUpdate,
  onDelete,
  onEdit,
  isEditable = true
}: GoalCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  const handleStatusChange = () => {
    if (!onUpdate) return;

    const statusOrder: Goal['status'][] = ['not-started', 'in-progress', 'completed'];
    const currentIndex = statusOrder.indexOf(goal.status);
    const nextStatus = statusOrder[(currentIndex + 1) % statusOrder.length];

    onUpdate({ ...goal, status: nextStatus });
  };

  const getPriorityColor = (priority: Goal['priority']) => {
    switch (priority) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'green';
      default: return 'gray';
    }
  };

  const getStatusColor = (status: Goal['status']) => {
    switch (status) {
      case 'completed': return 'green';
      case 'in-progress': return 'blue';
      case 'not-started': return 'gray';
      default: return 'gray';
    }
  };

  const getStatusIcon = (status: Goal['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle size={16} />;
      case 'in-progress': return '🔄';
      case 'not-started': return '⏸️';
      default: return null;
    }
  };

  const isOverdue = goal.dueDate && new Date() > new Date(goal.dueDate) && goal.status !== 'completed';

  return (
    <Card
      className={`w-72 cursor-move transition-all duration-200 ${isHovered ? 'shadow-lg' : ''}`}
      style={{
        position: 'absolute',
        left: goal.position.x,
        top: goal.position.y,
        zIndex: 1000,
        borderLeft: `4px solid var(--${getPriorityColor(goal.priority)}-9)`
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Flex direction="column" gap="3" p="3">
        {/* Header */}
        <Flex justify="between" align="start">
          <Flex direction="column" gap="1" flexGrow="1">
            <Text size="3" weight="bold" className={goal.status === 'completed' ? 'line-through' : ''}>
              {goal.title}
            </Text>
            {goal.description && (
              <Text size="2" color="gray">
                {goal.description}
              </Text>
            )}
          </Flex>

          {isEditable && isHovered && (
            <Flex gap="1">
              {onEdit && (
                <IconButton size="1" variant="ghost" onClick={() => onEdit(goal)}>
                  <Edit2 size={12} />
                </IconButton>
              )}
              {onDelete && (
                <IconButton size="1" variant="ghost" color="red" onClick={() => onDelete(goal.id)}>
                  <Trash2 size={12} />
                </IconButton>
              )}
            </Flex>
          )}
        </Flex>

        {/* Status and Priority */}
        <Flex gap="2" align="center">
          <Badge
            color={getStatusColor(goal.status)}
            className="cursor-pointer"
            onClick={isEditable ? handleStatusChange : undefined}
          >
            <Flex align="center" gap="1">
              {getStatusIcon(goal.status)}
              {goal.status.replace('-', ' ')}
            </Flex>
          </Badge>

          <Badge color={getPriorityColor(goal.priority)} size="1">
            {goal.priority} priority
          </Badge>

          {isOverdue && (
            <Badge color="red" size="1">
              Overdue
            </Badge>
          )}
        </Flex>

        {/* Due Date */}
        {goal.dueDate && (
          <Flex align="center" gap="2">
            <Calendar size={14} />
            <Text size="2" color={isOverdue ? 'red' : 'gray'}>
              Due: {new Date(goal.dueDate).toLocaleDateString()}
            </Text>
          </Flex>
        )}

        {/* Tags */}
        {goal.tags.length > 0 && (
          <Flex gap="1" wrap="wrap" align="center">
            <Tag size={12} />
            {goal.tags.map((tag) => (
              <Badge key={tag} size="1" variant="soft">
                {tag}
              </Badge>
            ))}
          </Flex>
        )}

        {/* Progress Indicator */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              goal.status === 'completed' ? 'bg-green-500' :
              goal.status === 'in-progress' ? 'bg-blue-500' : 'bg-gray-400'
            }`}
            style={{
              width: goal.status === 'completed' ? '100%' :
                     goal.status === 'in-progress' ? '50%' : '0%'
            }}
          />
        </div>
      </Flex>
    </Card>
  );
}
