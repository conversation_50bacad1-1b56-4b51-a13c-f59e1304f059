'use client';

import { useState, useRef, useEffect } from 'react';
import { <PERSON>, Flex, Text, Button, Badge } from '@radix-ui/themes';
import { Edit2, Trash2, Plus } from 'lucide-react';
import type { Hill<PERSON><PERSON> as HillChartType, HillChartItem } from '@/types';

interface HillChartProps {
  hillChart: HillChartType;
  onUpdate?: (hillChart: HillChartType) => void;
  onDelete?: (id: string) => void;
  onEdit?: (hillChart: HillChartType) => void;
  isEditable?: boolean;
}

export default function HillChart({
  hillChart,
  onUpdate,
  onDelete,
  onEdit,
  isEditable = true
}: HillChartProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [draggingItem, setDraggingItem] = useState<string | null>(null);
  const svgRef = useRef<SVGSVGElement>(null);

  const width = hillChart.width;
  const height = hillChart.height;
  const hillHeight = height * 0.6;
  const margin = 20;

  // Generate hill path (bell curve)
  const generateHillPath = () => {
    const hillWidth = width - (margin * 2);
    const hillTop = margin + (height - hillHeight) / 2;
    const hillBottom = hillTop + hillHeight;

    let path = `M ${margin} ${hillBottom}`;

    for (let x = 0; x <= hillWidth; x += 2) {
      const progress = x / hillWidth;
      const y = hillBottom - (Math.sin(progress * Math.PI) * hillHeight);
      path += ` L ${margin + x} ${y}`;
    }

    return path;
  };

  // Get Y coordinate for a given position on the hill
  const getYForPosition = (position: number) => {
    const hillWidth = width - (margin * 2);
    const hillTop = margin + (height - hillHeight) / 2;
    const hillBottom = hillTop + hillHeight;
    const progress = position / 100;

    return hillBottom - (Math.sin(progress * Math.PI) * hillHeight);
  };

  // Get position from coordinates
  const getPositionFromCoords = (x: number): number => {
    const hillWidth = width - (margin * 2);
    const relativeX = Math.max(0, Math.min(hillWidth, x - margin));
    return (relativeX / hillWidth) * 100;
  };

  // Handle item drag
  const handleItemDrag = (itemId: string, event: React.MouseEvent) => {
    if (!isEditable) return;

    event.preventDefault();
    setDraggingItem(itemId);

    const handleMouseMove = (e: MouseEvent) => {
      if (!svgRef.current) return;

      const rect = svgRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const newPosition = getPositionFromCoords(x);

      if (onUpdate) {
        const updatedItems = hillChart.items.map(item =>
          item.id === itemId ? { ...item, position: newPosition } : item
        );
        onUpdate({ ...hillChart, items: updatedItems });
      }
    };

    const handleMouseUp = () => {
      setDraggingItem(null);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Get status based on position
  const getStatusFromPosition = (position: number) => {
    if (position < 25) return { text: 'Problem Unclear', color: '#ef4444' };
    if (position < 50) return { text: 'Figuring Out', color: '#f97316' };
    if (position < 75) return { text: 'Making Progress', color: '#eab308' };
    return { text: 'Finished', color: '#22c55e' };
  };

  return (
    <Card
      className={`cursor-move transition-all duration-200 ${isHovered ? 'shadow-lg' : ''}`}
      style={{
        position: 'absolute',
        left: hillChart.position.x,
        top: hillChart.position.y,
        width: width + 40,
        height: height + 80,
        zIndex: 1000,
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Flex direction="column" gap="3" p="3">
        {/* Header */}
        <Flex justify="between" align="center">
          <Text size="3" weight="bold">{hillChart.title}</Text>
          {isEditable && isHovered && (
            <Flex gap="1">
              {onEdit && (
                <Button size="1" variant="ghost" onClick={() => onEdit(hillChart)}>
                  <Edit2 size={12} />
                </Button>
              )}
              {onDelete && (
                <Button size="1" variant="ghost" color="red" onClick={() => onDelete(hillChart.id)}>
                  <Trash2 size={12} />
                </Button>
              )}
            </Flex>
          )}
        </Flex>

        {/* Hill Chart SVG */}
        <div className="relative">
          <svg
            ref={svgRef}
            width={width}
            height={height}
            className="border rounded"
            style={{ backgroundColor: '#f8f9fa' }}
          >
            {/* Hill path */}
            <path
              d={generateHillPath()}
              fill="none"
              stroke="#6b7280"
              strokeWidth="2"
            />

            {/* Midpoint line */}
            <line
              x1={width / 2}
              y1={margin}
              x2={width / 2}
              y2={height - margin}
              stroke="#6b7280"
              strokeWidth="1"
              strokeDasharray="4,4"
              opacity="0.5"
            />

            {/* Status labels */}
            <text x={width * 0.25} y={height - 5} textAnchor="middle" fontSize="10" fill="#6b7280">
              Problem Unclear
            </text>
            <text x={width * 0.75} y={height - 5} textAnchor="middle" fontSize="10" fill="#6b7280">
              Solution Clear
            </text>

            {/* Hill chart items */}
            {hillChart.items.map((item) => {
              const x = margin + ((width - margin * 2) * item.position / 100);
              const y = getYForPosition(item.position);

              return (
                <g key={item.id}>
                  <circle
                    cx={x}
                    cy={y}
                    r="8"
                    fill={item.color}
                    stroke="#ffffff"
                    strokeWidth="2"
                    className={`cursor-pointer transition-all ${
                      draggingItem === item.id ? 'opacity-50' : 'hover:r-10'
                    }`}
                    onMouseDown={(e) => handleItemDrag(item.id, e)}
                  />
                  <text
                    x={x}
                    y={y - 15}
                    textAnchor="middle"
                    fontSize="10"
                    fontWeight="bold"
                    fill={item.color}
                    className="pointer-events-none"
                  >
                    {item.name}
                  </text>
                </g>
              );
            })}
          </svg>
        </div>

        {/* Items list */}
        <Flex direction="column" gap="2">
          {hillChart.items.map((item) => {
            const status = getStatusFromPosition(item.position);
            return (
              <Flex key={item.id} justify="between" align="center" p="2" className="bg-gray-50 rounded">
                <Flex align="center" gap="2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: item.color }}
                  />
                  <Text size="2" weight="medium">{item.name}</Text>
                </Flex>
                <Badge size="1" style={{ backgroundColor: status.color, color: 'white' }}>
                  {status.text}
                </Badge>
              </Flex>
            );
          })}
        </Flex>

        {/* Add item button */}
        {isEditable && (
          <Button size="1" variant="soft" onClick={() => onEdit?.(hillChart)}>
            <Plus size={12} />
            Add Item
          </Button>
        )}
      </Flex>
    </Card>
  );
}
