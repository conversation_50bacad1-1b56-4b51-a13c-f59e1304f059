'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, Card, Flex, Text, TextField, IconButton } from '@radix-ui/themes';
import { Trash2, Plus } from 'lucide-react';
import type { Hill<PERSON><PERSON>, HillChartItem } from '@/types';

interface HillChartEditorProps {
  hillChart?: Partial<HillChart>;
  onSave: (hillChart: Omit<HillChart, 'id' | 'whiteboardId'>) => void;
  onCancel: () => void;
  position: { x: number; y: number };
}

const defaultColors = [
  '#ef4444', '#f97316', '#eab308', '#22c55e',
  '#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899'
];

export default function HillChartEditor({
  hillChart,
  onSave,
  onCancel,
  position
}: HillChartEditorProps) {
  const [title, setTitle] = useState(hillChart?.title || 'New Hill Chart');
  const [width, setWidth] = useState(hillChart?.width || 400);
  const [height, setHeight] = useState(hillChart?.height || 200);
  const [items, setItems] = useState<Omit<HillChartItem, 'id'>[]>(
    hillChart?.items?.map(item => ({ ...item, id: undefined })) || []
  );

  const addItem = () => {
    const newItem: Omit<HillChartItem, 'id'> = {
      name: `Item ${items.length + 1}`,
      position: 25,
      color: defaultColors[items.length % defaultColors.length],
      description: ''
    };
    setItems([...items, newItem]);
  };

  const updateItem = (index: number, field: keyof HillChartItem, value: string | number) => {
    const updatedItems = items.map((item, i) =>
      i === index ? { ...item, [field]: value } : item
    );
    setItems(updatedItems);
  };

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const handleSave = () => {
    if (!title.trim()) {
      alert('Please enter a chart title');
      return;
    }

    if (items.length === 0) {
      alert('Please add at least one item to the chart');
      return;
    }

    const itemsWithIds: HillChartItem[] = items.map((item, index) => ({
      ...item,
      id: `item-${Date.now()}-${index}`
    }));

    const newHillChart: Omit<HillChart, 'id' | 'whiteboardId'> = {
      title: title.trim(),
      items: itemsWithIds,
      position,
      width,
      height
    };

    onSave(newHillChart);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-[600px] max-h-[90vh] overflow-y-auto">
        <Flex direction="column" gap="4" p="4">
          <Text size="4" weight="bold">
            {hillChart ? 'Edit Hill Chart' : 'Create New Hill Chart'}
          </Text>

          <Flex direction="column" gap="2">
            <Text size="2" weight="medium">Chart Title *</Text>
            <TextField.Root
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter chart title"
            />
          </Flex>

          <Flex gap="3">
            <Flex direction="column" gap="2" flexGrow="1">
              <Text size="2" weight="medium">Width</Text>
              <TextField.Root
                type="number"
                value={width.toString()}
                onChange={(e) => setWidth(Number.parseInt(e.target.value) || 400)}
                min="200"
                max="800"
              />
            </Flex>

            <Flex direction="column" gap="2" flexGrow="1">
              <Text size="2" weight="medium">Height</Text>
              <TextField.Root
                type="number"
                value={height.toString()}
                onChange={(e) => setHeight(Number.parseInt(e.target.value) || 200)}
                min="150"
                max="400"
              />
            </Flex>
          </Flex>

          <Flex direction="column" gap="3">
            <Flex justify="between" align="center">
              <Text size="3" weight="medium">Chart Items</Text>
              <Button size="2" onClick={addItem}>
                <Plus size={14} />
                Add Item
              </Button>
            </Flex>

            {items.length === 0 ? (
              <Card className="p-4 text-center">
                <Text size="2" color="gray">
                  No items added yet. Click "Add Item" to get started.
                </Text>
              </Card>
            ) : (
              <Flex direction="column" gap="3">
                {items.map((item, index) => (
                  <Card key={`item-${index}-${item.name}`} className="p-3">
                    <Flex direction="column" gap="3">
                      <Flex justify="between" align="center">
                        <Text size="2" weight="medium">Item {index + 1}</Text>
                        <IconButton
                          size="1"
                          variant="ghost"
                          color="red"
                          onClick={() => removeItem(index)}
                        >
                          <Trash2 size={12} />
                        </IconButton>
                      </Flex>

                      <Flex gap="3">
                        <Flex direction="column" gap="2" flexGrow="1">
                          <Text size="1">Name *</Text>
                          <TextField.Root
                            value={item.name}
                            onChange={(e) => updateItem(index, 'name', e.target.value)}
                            placeholder="Item name"
                          />
                        </Flex>

                        <Flex direction="column" gap="2">
                          <Text size="1">Color</Text>
                          <input
                            type="color"
                            value={item.color}
                            onChange={(e) => updateItem(index, 'color', e.target.value)}
                            className="w-12 h-8 rounded border cursor-pointer"
                          />
                        </Flex>
                      </Flex>

                      <Flex direction="column" gap="2">
                        <Text size="1">Position (0-100)</Text>
                        <Flex align="center" gap="2">
                          <input
                            type="range"
                            min="0"
                            max="100"
                            value={item.position}
                            onChange={(e) => updateItem(index, 'position', Number.parseInt(e.target.value))}
                            className="flex-grow"
                          />
                          <Text size="1" className="w-8 text-center">
                            {Math.round(item.position)}
                          </Text>
                        </Flex>
                        <Text size="1" color="gray">
                          {item.position < 25 ? 'Problem Unclear' :
                           item.position < 50 ? 'Figuring Out' :
                           item.position < 75 ? 'Making Progress' : 'Finished'}
                        </Text>
                      </Flex>

                      <Flex direction="column" gap="2">
                        <Text size="1">Description</Text>
                        <TextField.Root
                          value={item.description || ''}
                          onChange={(e) => updateItem(index, 'description', e.target.value)}
                          placeholder="Optional description"
                        />
                      </Flex>
                    </Flex>
                  </Card>
                ))}
              </Flex>
            )}
          </Flex>

          <Flex gap="2" justify="end" mt="4">
            <Button variant="soft" onClick={onCancel}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              {hillChart ? 'Update Chart' : 'Create Chart'}
            </Button>
          </Flex>
        </Flex>
      </Card>
    </div>
  );
}
