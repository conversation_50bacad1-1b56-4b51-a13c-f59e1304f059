'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, Card, Flex, Text, TextField, Select } from '@radix-ui/themes';
import type { CountdownTimer } from '@/types';

interface CountdownEditorProps {
  timer?: Partial<CountdownTimer>;
  onSave: (timer: Omit<CountdownTimer, 'id' | 'whiteboardId'>) => void;
  onCancel: () => void;
  position: { x: number; y: number };
}

export default function CountdownEditor({
  timer,
  onSave,
  onCancel,
  position
}: CountdownEditorProps) {
  const [title, setTitle] = useState(timer?.title || 'New Timer');
  const [endDate, setEndDate] = useState(
    timer?.endDate ? new Date(timer.endDate).toISOString().slice(0, 16) : ''
  );
  const [color, setColor] = useState(timer?.style?.color || '#000000');
  const [fontSize, setFontSize] = useState(timer?.style?.fontSize || 16);
  const [backgroundColor, setBackgroundColor] = useState(
    timer?.style?.backgroundColor || '#ffffff'
  );

  const handleSave = () => {
    if (!title.trim() || !endDate) {
      alert('Please fill in all required fields');
      return;
    }

    const newTimer: Omit<CountdownTimer, 'id' | 'whiteboardId'> = {
      title: title.trim(),
      endDate: new Date(endDate),
      position,
      style: {
        color,
        fontSize,
        backgroundColor
      },
      isActive: true
    };

    onSave(newTimer);
  };

  return (
    <Card className="w-80 p-4" style={{ position: 'fixed', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', zIndex: 1000 }}>
      <Flex direction="column" gap="4">
        <Text size="4" weight="bold">
          {timer ? 'Edit Timer' : 'Create New Timer'}
        </Text>

        <Flex direction="column" gap="2">
          <Text size="2" weight="medium">Timer Title *</Text>
          <TextField.Root
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter timer title"
          />
        </Flex>

        <Flex direction="column" gap="2">
          <Text size="2" weight="medium">End Date & Time *</Text>
          <input
            type="datetime-local"
            value={endDate}
            onChange={(e) => setEndDate(e.target.value)}
            min={new Date().toISOString().slice(0, 16)}
            className="px-3 py-2 border rounded-md"
          />
        </Flex>

        <Flex gap="4">
          <Flex direction="column" gap="2" flexGrow="1">
            <Text size="2" weight="medium">Text Color</Text>
            <input
              type="color"
              value={color}
              onChange={(e) => setColor(e.target.value)}
              className="w-full h-10 rounded border cursor-pointer"
            />
          </Flex>

          <Flex direction="column" gap="2" flexGrow="1">
            <Text size="2" weight="medium">Background</Text>
            <input
              type="color"
              value={backgroundColor}
              onChange={(e) => setBackgroundColor(e.target.value)}
              className="w-full h-10 rounded border cursor-pointer"
            />
          </Flex>
        </Flex>

        <Flex direction="column" gap="2">
          <Text size="2" weight="medium">Font Size</Text>
          <Select.Root
            value={fontSize.toString()}
            onValueChange={(value) => setFontSize(Number.parseInt(value))}
          >
            <Select.Trigger />
            <Select.Content>
              <Select.Item value="12">Small (12px)</Select.Item>
              <Select.Item value="16">Medium (16px)</Select.Item>
              <Select.Item value="20">Large (20px)</Select.Item>
              <Select.Item value="24">Extra Large (24px)</Select.Item>
            </Select.Content>
          </Select.Root>
        </Flex>

        <Flex gap="2" justify="end">
          <Button variant="soft" onClick={onCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            {timer ? 'Update Timer' : 'Create Timer'}
          </Button>
        </Flex>
      </Flex>
    </Card>
  );
}
