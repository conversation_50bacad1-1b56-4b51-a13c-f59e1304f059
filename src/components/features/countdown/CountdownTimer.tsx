'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, Flex, <PERSON>, <PERSON><PERSON>, Badge } from '@radix-ui/themes';
import { Play, Pause, RotateCcw } from 'lucide-react';
import type { CountdownTimer as CountdownTimerType } from '@/types';

interface CountdownTimerProps {
  timer: CountdownTimerType;
  onUpdate?: (timer: CountdownTimerType) => void;
  onDelete?: (id: string) => void;
  isEditable?: boolean;
}

export default function CountdownTimer({
  timer,
  onUpdate,
  onDelete,
  isEditable = true
}: CountdownTimerProps) {
  const [timeLeft, setTimeLeft] = useState<string>('');
  const [isRunning, setIsRunning] = useState(timer.isActive);

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date().getTime();
      const end = new Date(timer.endDate).getTime();
      const difference = end - now;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        let timeString = '';
        if (days > 0) timeString += `${days}d `;
        if (hours > 0) timeString += `${hours}h `;
        if (minutes > 0) timeString += `${minutes}m `;
        timeString += `${seconds}s`;

        setTimeLeft(timeString);
      } else {
        setTimeLeft('Time\'s up!');
        setIsRunning(false);
        if (onUpdate) {
          onUpdate({ ...timer, isActive: false });
        }
      }
    };

    calculateTimeLeft();

    let interval: NodeJS.Timeout;
    if (isRunning) {
      interval = setInterval(calculateTimeLeft, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [timer.endDate, isRunning, timer, onUpdate]);

  const handlePlayPause = () => {
    const newIsRunning = !isRunning;
    setIsRunning(newIsRunning);

    if (onUpdate) {
      onUpdate({ ...timer, isActive: newIsRunning });
    }
  };

  const handleReset = () => {
    setIsRunning(false);

    if (onUpdate) {
      onUpdate({ ...timer, isActive: false });
    }
  };

  const isExpired = new Date() > new Date(timer.endDate);
  const statusColor = isExpired ? 'red' : isRunning ? 'green' : 'gray';

  return (
    <Card
      className="w-64 cursor-move"
      style={{
        backgroundColor: timer.style.backgroundColor || 'transparent',
        position: 'absolute',
        left: timer.position.x,
        top: timer.position.y,
        zIndex: 1000,
      }}
    >
      <Flex direction="column" gap="3" p="3">
        <Flex justify="between" align="center">
          <Text
            size="3"
            weight="bold"
            style={{
              color: timer.style.color,
              fontSize: `${timer.style.fontSize}px`
            }}
          >
            {timer.title}
          </Text>
          <Badge color={statusColor} size="1">
            {isExpired ? 'Expired' : isRunning ? 'Running' : 'Paused'}
          </Badge>
        </Flex>

        <Text
          size="5"
          weight="bold"
          align="center"
          style={{
            color: timer.style.color,
            fontFamily: 'monospace'
          }}
        >
          {timeLeft}
        </Text>

        <Text size="1" color="gray" align="center">
          Ends: {new Date(timer.endDate).toLocaleString()}
        </Text>

        {isEditable && (
          <Flex gap="2" justify="center">
            <Button
              size="1"
              variant="soft"
              onClick={handlePlayPause}
              disabled={isExpired}
            >
              {isRunning ? <Pause size={12} /> : <Play size={12} />}
            </Button>

            <Button
              size="1"
              variant="soft"
              onClick={handleReset}
              disabled={isExpired}
            >
              <RotateCcw size={12} />
            </Button>

            {onDelete && (
              <Button
                size="1"
                variant="soft"
                color="red"
                onClick={() => onDelete(timer.id)}
              >
                Delete
              </Button>
            )}
          </Flex>
        )}
      </Flex>
    </Card>
  );
}
