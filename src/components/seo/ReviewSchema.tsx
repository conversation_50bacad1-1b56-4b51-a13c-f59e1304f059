import React from 'react';
import { Star } from 'lucide-react';

interface Review {
  author: string;
  rating: number;
  reviewBody: string;
  datePublished: string;
  company?: string;
  position?: string;
}

interface ReviewSchemaProps {
  reviews: Review[];
  aggregateRating?: {
    ratingValue: number;
    ratingCount: number;
  };
}

export default function ReviewSchema({ reviews, aggregateRating }: ReviewSchemaProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": "Anchorboard App",
    "description": "AI-powered collaborative whiteboard platform",
    "aggregateRating": aggregateRating ? {
      "@type": "AggregateRating",
      "ratingValue": aggregateRating.ratingValue,
      "ratingCount": aggregateRating.ratingCount,
      "bestRating": 5,
      "worstRating": 1
    } : undefined,
    "review": reviews.map(review => ({
      "@type": "Review",
      "author": {
        "@type": "Person",
        "name": review.author
      },
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": review.rating,
        "bestRating": 5,
        "worstRating": 1
      },
      "reviewBody": review.reviewBody,
      "datePublished": review.datePublished
    }))
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  );
}

// Testimonials Component with built-in schema
interface TestimonialsProps {
  reviews: Review[];
  title?: string;
  className?: string;
  aggregateRating?: {
    ratingValue: number;
    ratingCount: number;
  };
}

export function TestimonialsSection({ 
  reviews, 
  title = "What Our Users Say", 
  className = "",
  aggregateRating 
}: TestimonialsProps) {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={16}
        className={`${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <>
      <ReviewSchema reviews={reviews} aggregateRating={aggregateRating} />
      <section className={`space-y-8 ${className}`}>
        <div className="text-center space-y-4">
          <h2 className="text-3xl font-bold">{title}</h2>
          {aggregateRating && (
            <div className="flex items-center justify-center gap-2">
              <div className="flex">{renderStars(Math.round(aggregateRating.ratingValue))}</div>
              <span className="text-lg font-semibold">{aggregateRating.ratingValue}</span>
              <span className="text-gray-600">({aggregateRating.ratingCount} reviews)</span>
            </div>
          )}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reviews.map((review, index) => (
            <div key={index} className="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
              <div className="flex items-center gap-2 mb-3">
                <div className="flex">{renderStars(review.rating)}</div>
                <span className="text-sm text-gray-600">
                  {new Date(review.datePublished).toLocaleDateString()}
                </span>
              </div>
              
              <blockquote className="text-gray-700 mb-4 italic">
                "{review.reviewBody}"
              </blockquote>
              
              <div className="border-t pt-3">
                <div className="font-semibold text-gray-800">{review.author}</div>
                {review.position && review.company && (
                  <div className="text-sm text-gray-600">
                    {review.position} at {review.company}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </section>
    </>
  );
}
