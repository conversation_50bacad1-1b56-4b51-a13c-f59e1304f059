'use client';

import React from 'react';
import { Flex, <PERSON>, <PERSON><PERSON>, Card } from '@radix-ui/themes';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error!} retry={this.handleRetry} />;
      }

      return <DefaultErrorFallback error={this.state.error!} retry={this.handleRetry} />;
    }

    return this.props.children;
  }
}

interface ErrorFallbackProps {
  error: Error;
  retry: () => void;
}

function DefaultErrorFallback({ error, retry }: ErrorFallbackProps) {
  const isDevelopment = process.env.NODE_ENV === 'development';

  return (
    <Flex align="center" justify="center" style={{ minHeight: '50vh' }} p="4">
      <Card style={{ maxWidth: 500, width: '100%' }} className="p-6">
        <Flex direction="column" align="center" gap="4">
          <div className="text-red-500">
            <AlertTriangle size={48} />
          </div>
          
          <div className="text-center">
            <Text size="5" weight="bold" mb="2">
              Something went wrong
            </Text>
            <Text size="3" color="gray">
              We encountered an unexpected error. Please try again.
            </Text>
          </div>

          {isDevelopment && (
            <Card className="w-full p-3 bg-red-50 border border-red-200">
              <Text size="2" weight="bold" color="red" mb="2">
                Error Details (Development Only):
              </Text>
              <Text size="1" style={{ fontFamily: 'monospace', wordBreak: 'break-all' }}>
                {error.message}
              </Text>
              {error.stack && (
                <details className="mt-2">
                  <summary className="cursor-pointer text-sm text-red-600">
                    Stack Trace
                  </summary>
                  <pre className="mt-2 text-xs overflow-auto max-h-40 text-red-700">
                    {error.stack}
                  </pre>
                </details>
              )}
            </Card>
          )}

          <Flex gap="3">
            <Button onClick={retry} variant="solid">
              <RefreshCw size={16} />
              Try Again
            </Button>
            <Button 
              onClick={() => window.location.href = '/'} 
              variant="soft"
            >
              <Home size={16} />
              Go Home
            </Button>
          </Flex>
        </Flex>
      </Card>
    </Flex>
  );
}

// Hook for handling async errors in functional components
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const handleError = React.useCallback((error: Error) => {
    console.error('Async error caught:', error);
    setError(error);
  }, []);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { handleError, resetError };
}

// Whiteboard-specific error fallback
export function WhiteboardErrorFallback({ error, retry }: ErrorFallbackProps) {
  return (
    <Flex align="center" justify="center" style={{ minHeight: '100vh' }} p="4">
      <Card style={{ maxWidth: 500, width: '100%' }} className="p-6">
        <Flex direction="column" align="center" gap="4">
          <div className="text-red-500">
            <AlertTriangle size={48} />
          </div>

          <div className="text-center">
            <Text size="5" weight="bold" mb="2">
              Whiteboard Error
            </Text>
            <Text size="3" color="gray">
              There was an error loading your whiteboard. Don't worry, your data is safe.
            </Text>
          </div>

          <Flex gap="3">
            <Button onClick={retry} variant="solid">
              <RefreshCw size={16} />
              Try Again
            </Button>
            <Button
              onClick={() => window.location.href = '/whiteboard'}
              variant="soft"
            >
              <Home size={16} />
              Back to Whiteboards
            </Button>
          </Flex>
        </Flex>
      </Card>
    </Flex>
  );
}

export default ErrorBoundary;
