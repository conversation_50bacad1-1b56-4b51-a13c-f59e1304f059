import { Flex, Text } from '@radix-ui/themes';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
}

export default function LoadingSpinner({ 
  size = 'md', 
  text = 'Loading...', 
  className = '' 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  const textSizes = {
    sm: '2',
    md: '3',
    lg: '4'
  } as const;

  return (
    <Flex direction="column" align="center" gap="3" className={className}>
      <div 
        className={`animate-spin rounded-full border-2 border-blue-600 border-t-transparent ${sizeClasses[size]}`}
        role="status"
        aria-label="Loading"
      />
      {text && (
        <Text size={textSizes[size]} color="gray">
          {text}
        </Text>
      )}
    </Flex>
  );
}
