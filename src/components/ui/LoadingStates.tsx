'use client';

import { Flex, Text, Spinner, Card, Skeleton } from '@radix-ui/themes';
import { Loader2 } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  text?: string;
  className?: string;
}

export function LoadingSpinner({ size = 'medium', text, className }: LoadingSpinnerProps) {
  const sizeMap = {
    small: 16,
    medium: 24,
    large: 32,
  };

  const spinnerSize = {
    small: '1',
    medium: '2',
    large: '3',
  } as const;

  return (
    <Flex align="center" justify="center" direction="column" gap="3" className={className}>
      <Spinner size={spinnerSize[size]} />
      {text && (
        <Text size="2" color="gray">
          {text}
        </Text>
      )}
    </Flex>
  );
}

interface FullPageLoadingProps {
  text?: string;
}

export function FullPageLoading({ text = 'Loading...' }: FullPageLoadingProps) {
  return (
    <Flex 
      align="center" 
      justify="center" 
      style={{ height: '100vh' }}
      direction="column"
      gap="3"
    >
      <Loader2 className="animate-spin" size={32} />
      <Text size="3" color="gray">
        {text}
      </Text>
    </Flex>
  );
}

interface WhiteboardLoadingSkeletonProps {
  showToolbar?: boolean;
}

export function WhiteboardLoadingSkeleton({ showToolbar = true }: WhiteboardLoadingSkeletonProps) {
  return (
    <div className="h-screen flex flex-col">
      {showToolbar && (
        <div className="border-b border-gray-200 bg-white p-3">
          <Flex justify="between" align="center">
            <Flex gap="2" align="center">
              <Skeleton width="200px" height="24px" />
              <Skeleton width="80px" height="16px" />
            </Flex>
            <Flex gap="2">
              <Skeleton width="32px" height="32px" />
              <Skeleton width="32px" height="32px" />
              <Skeleton width="32px" height="32px" />
              <Skeleton width="80px" height="32px" />
              <Skeleton width="32px" height="32px" />
            </Flex>
          </Flex>
        </div>
      )}
      
      <div className="flex-1 bg-gray-50 relative">
        <div className="absolute inset-0 flex items-center justify-center">
          <LoadingSpinner size="large" text="Loading whiteboard..." />
        </div>
        
        {/* Skeleton nodes */}
        <div className="absolute top-20 left-20">
          <Skeleton width="120px" height="80px" />
        </div>
        <div className="absolute top-40 right-32">
          <Skeleton width="160px" height="100px" />
        </div>
        <div className="absolute bottom-32 left-40">
          <Skeleton width="140px" height="90px" />
        </div>
      </div>
    </div>
  );
}

interface CardLoadingSkeletonProps {
  count?: number;
  columns?: number;
}

export function CardLoadingSkeleton({ count = 8, columns = 4 }: CardLoadingSkeletonProps) {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  };

  return (
    <div className={`grid ${gridCols[columns as keyof typeof gridCols]} gap-4`}>
      {Array.from({ length: count }).map((_, index) => (
        <Card key={index} className="p-4">
          <Flex direction="column" gap="3">
            <Flex justify="between" align="start">
              <div className="flex-1">
                <Skeleton width="80%" height="20px" mb="2" />
                <Flex gap="2">
                  <Skeleton width="60px" height="16px" />
                  <Skeleton width="40px" height="16px" />
                </Flex>
              </div>
              <Skeleton width="24px" height="24px" />
            </Flex>
            
            <Flex direction="column" gap="1">
              <Skeleton width="70%" height="14px" />
              <Skeleton width="60%" height="14px" />
            </Flex>
            
            <Flex gap="2" mt="2">
              <Skeleton width="60px" height="28px" />
              <Skeleton width="28px" height="28px" />
              <Skeleton width="28px" height="28px" />
            </Flex>
          </Flex>
        </Card>
      ))}
    </div>
  );
}

interface InlineLoadingProps {
  text?: string;
  size?: 'small' | 'medium';
}

export function InlineLoading({ text, size = 'small' }: InlineLoadingProps) {
  return (
    <Flex align="center" gap="2">
      <Spinner size={size === 'small' ? '1' : '2'} />
      {text && (
        <Text size={size === 'small' ? '1' : '2'} color="gray">
          {text}
        </Text>
      )}
    </Flex>
  );
}

interface ButtonLoadingProps {
  isLoading: boolean;
  children: React.ReactNode;
  loadingText?: string;
}

export function ButtonLoading({ isLoading, children, loadingText }: ButtonLoadingProps) {
  if (isLoading) {
    return (
      <Flex align="center" gap="2">
        <Loader2 className="animate-spin" size={16} />
        {loadingText && <span>{loadingText}</span>}
      </Flex>
    );
  }
  
  return <>{children}</>;
}

interface ListLoadingSkeletonProps {
  count?: number;
  showAvatar?: boolean;
}

export function ListLoadingSkeleton({ count = 5, showAvatar = false }: ListLoadingSkeletonProps) {
  return (
    <div className="space-y-3">
      {Array.from({ length: count }).map((_, index) => (
        <Card key={index} className="p-3">
          <Flex align="center" gap="3">
            {showAvatar && <Skeleton width="40px" height="40px" style={{ borderRadius: '50%' }} />}
            <div className="flex-1">
              <Skeleton width="60%" height="16px" mb="1" />
              <Skeleton width="40%" height="14px" />
            </div>
            <Skeleton width="80px" height="32px" />
          </Flex>
        </Card>
      ))}
    </div>
  );
}

// Loading state for forms
export function FormLoadingSkeleton() {
  return (
    <div className="space-y-4">
      <div>
        <Skeleton width="80px" height="16px" mb="2" />
        <Skeleton width="100%" height="36px" />
      </div>
      <div>
        <Skeleton width="100px" height="16px" mb="2" />
        <Skeleton width="100%" height="36px" />
      </div>
      <div>
        <Skeleton width="120px" height="16px" mb="2" />
        <Skeleton width="100%" height="80px" />
      </div>
      <Flex gap="2" justify="end">
        <Skeleton width="80px" height="36px" />
        <Skeleton width="100px" height="36px" />
      </Flex>
    </div>
  );
}

// Loading overlay for existing content
interface LoadingOverlayProps {
  isLoading: boolean;
  children: React.ReactNode;
  text?: string;
}

export function LoadingOverlay({ isLoading, children, text = 'Loading...' }: LoadingOverlayProps) {
  return (
    <div className="relative">
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
          <LoadingSpinner text={text} />
        </div>
      )}
    </div>
  );
}
