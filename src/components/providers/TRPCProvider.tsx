'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { httpBatchLink, loggerLink } from '@trpc/client';
import { createTRPCReact } from '@trpc/react-query';
import { useState } from 'react';
import superjson from 'superjson';

import type { AppRouter } from '@/server/routers/_app';
import { authClient } from '@/lib/auth-client';

/**
 * Authentication Header Configuration for tRPC
 *
 * This module implements secure authentication header handling for tRPC requests
 * using BetterAuth session management. The implementation follows these principles:
 *
 * 1. **Cookie-First Approach**: BetterAuth primarily uses HTTP-only cookies for session storage,
 *    so we prioritize passing all authentication cookies to the server.
 *
 * 2. **Fallback to Bearer Token**: If cookies are not available, we attempt to extract
 *    session tokens from the BetterAuth client and send them as Authorization headers.
 *
 * 3. **Error Resilience**: All authentication header generation includes proper error
 *    handling to ensure tRPC requests continue to work even if auth fails.
 *
 * 4. **Type Safety**: All functions are properly typed to handle both authenticated
 *    and unauthenticated states safely.
 *
 * 5. **Security**: Session tokens are properly encoded/decoded and transmitted securely.
 */

const createQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        // With SSR, we usually want to set some default staleTime
        // above 0 to avoid refetching immediately on the client
        staleTime: 30 * 1000,
        retry: (failureCount, error) => {
          // Don't retry on 4xx errors
          if (error instanceof Error && 'status' in error) {
            const status = error.status as number;
            if (status >= 400 && status < 500) {
              return false;
            }
          }
          return failureCount < 3;
        },
      },
      mutations: {
        retry: false,
      },
    },
  });

const api = createTRPCReact<AppRouter>();

let clientQueryClientSingleton: QueryClient | undefined = undefined;

const getQueryClient = () => {
  if (typeof window === 'undefined') {
    // Server: always make a new query client
    return createQueryClient();
  }
  // Browser: use singleton pattern to keep the same query client
  return (clientQueryClientSingleton ??= createQueryClient());
};

function getBaseUrl() {
  if (typeof window !== 'undefined') return '';
  if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`;
  return `http://localhost:${process.env.PORT ?? 3000}`;
}

/**
 * Gets all authentication-related cookies as a formatted cookie string
 * This ensures we pass all relevant cookies to the server for BetterAuth session handling
 */
function getAuthCookies(): string | null {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    const cookies = document.cookie.split(';');
    const authCookies: string[] = [];

    for (const cookie of cookies) {
      const [name] = cookie.trim().split('=');

      // Include all BetterAuth related cookies
      if (name.startsWith('better-auth') ||
          name === 'session_token' ||
          name === 'auth_session') {
        authCookies.push(cookie.trim());
      }
    }

    return authCookies.length > 0 ? authCookies.join('; ') : null;
  } catch (error) {
    console.error('Error extracting auth cookies:', error);
    return null;
  }
}

/**
 * Gets authentication headers for tRPC requests
 * Returns headers object with session token if available
 *
 * @returns Promise<Record<string, string>> - Headers object with authentication data
 */
async function getAuthHeaders(): Promise<Record<string, string>> {
  const headers: Record<string, string> = {};

  try {
    console.log('TRPCProvider - Getting auth headers...');

    // Method 1: Pass all authentication cookies to maintain session state
    const authCookies = getAuthCookies();
    console.log('TRPCProvider - Auth cookies:', authCookies ? 'found' : 'not found');

    if (authCookies) {
      // Include all BetterAuth cookies in the request
      // This is the most reliable method for BetterAuth session handling
      headers['Cookie'] = authCookies;
      console.log('TRPCProvider - Added cookies to headers');
      return headers;
    }

    console.log('TRPCProvider - No auth cookies found, trying fallback method');

    // Method 2: Fallback - try to get session from authClient if available
    if (typeof window !== 'undefined' && authClient) {
      try {
        // Get current session data from BetterAuth client
        const sessionData = await authClient.getSession();
        console.log('TRPCProvider - Session data from authClient:', sessionData ? 'found' : 'not found');

        // Type-safe check for session data structure
        if (sessionData &&
            typeof sessionData === 'object' &&
            'data' in sessionData &&
            sessionData.data &&
            typeof sessionData.data === 'object' &&
            'session' in sessionData.data &&
            sessionData.data.session &&
            typeof sessionData.data.session === 'object' &&
            'token' in sessionData.data.session &&
            typeof sessionData.data.session.token === 'string') {

          // If we have a session token, include it in Authorization header as fallback
          headers['Authorization'] = `Bearer ${sessionData.data.session.token}`;
          console.log('TRPCProvider - Added Authorization header');
        }
      } catch (sessionError) {
        // Session retrieval failed, but this is not critical for app functionality
        console.debug('TRPCProvider - Could not retrieve session from authClient:', sessionError);
      }
    }

    console.log('TRPCProvider - Final headers:', Object.keys(headers));
    return headers;
  } catch (error) {
    console.error('TRPCProvider - Error generating auth headers:', error);
    // Return empty headers object to ensure tRPC requests continue to work
    return {};
  }
}

interface TRPCProviderProps {
  children: React.ReactNode;
}

export function TRPCProvider({ children }: TRPCProviderProps) {
  const queryClient = getQueryClient();

  const [trpcClient] = useState(() =>
    api.createClient({
      links: [
        loggerLink({
          enabled: (opts) =>
            process.env.NODE_ENV === 'development' ||
            (opts.direction === 'down' && opts.result instanceof Error),
        }),
        httpBatchLink({
          url: `${getBaseUrl()}/api/trpc`,
          transformer: superjson,
          // Dynamic headers with authentication support
          async headers() {
            try {
              // Get authentication headers (session token, etc.)
              const authHeaders = await getAuthHeaders();
              console.log('authHeaders ', authHeaders );

              return {
                // Include authentication headers if available
                ...authHeaders,
                // Add any other default headers
                'Content-Type': 'application/json',
              };
            } catch (error) {
              console.error('Error generating tRPC headers:', error);
              // Return minimal headers if auth header generation fails
              return {
                'Content-Type': 'application/json',
              };
            }
          },
        }),
      ],
    })
  );

  return (
    <api.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        {children}
        {process.env.NODE_ENV === 'development' && (
          <ReactQueryDevtools
            initialIsOpen={false}
            buttonPosition="bottom-left"
          />
        )}
      </QueryClientProvider>
    </api.Provider>
  );
}

// Export the api for use in components
export { api };
