import React from 'react';
import Link from 'next/link';
import { Container, Flex, Text, Separator } from '@radix-ui/themes';
import { 
  Home, 
  Info, 
  Mail, 
  Shield, 
  FileText, 
  Star, 
  DollarSign,
  Twitter,
  Github,
  Linkedin
} from 'lucide-react';

interface FooterProps {
  className?: string;
}

export default function Footer({ className = '' }: FooterProps) {
  const currentYear = new Date().getFullYear();

  const navigationLinks = [
    {
      label: 'Home',
      href: '/',
      icon: Home,
      description: 'Back to homepage'
    },
    {
      label: 'Features',
      href: '/features',
      icon: Star,
      description: 'Explore our features'
    },
    {
      label: 'Pricing',
      href: '/pricing',
      icon: DollarSign,
      description: 'View pricing plans'
    },
    {
      label: 'About',
      href: '/about',
      icon: Info,
      description: 'Learn about us'
    }
  ];

  const supportLinks = [
    {
      label: 'Contact',
      href: '/contact',
      icon: Mail,
      description: 'Get in touch'
    },
    {
      label: 'Privacy Policy',
      href: '/privacy',
      icon: Shield,
      description: 'Privacy information'
    },
    {
      label: 'Terms of Service',
      href: '/terms',
      icon: FileText,
      description: 'Terms and conditions'
    }
  ];

  const socialLinks = [
    {
      label: 'Twitter',
      href: 'https://twitter.com/anchorboardapp',
      icon: Twitter,
      color: 'text-blue-400 hover:text-blue-500'
    },
    {
      label: 'GitHub',
      href: 'https://github.com/anchorboardapp',
      icon: Github,
      color: 'text-gray-600 hover:text-gray-700'
    },
    {
      label: 'LinkedIn',
      href: 'https://linkedin.com/company/anchorboardapp',
      icon: Linkedin,
      color: 'text-blue-600 hover:text-blue-700'
    }
  ];

  return (
    <footer className={`bg-gray-50 border-t border-gray-200 mt-16 ${className}`}>
      <Container size="4" className="py-12">
        <Flex direction="column" gap="8">
          {/* Main Footer Content */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-1">
              <Flex direction="column" gap="4">
                <div>
                  <Text size="5" weight="bold" className="text-gray-900">
                    Anchorboard App
                  </Text>
                  <Text size="2" color="gray" className="mt-2 leading-relaxed">
                    AI-powered collaborative whiteboard for modern teams. Create, collaborate, 
                    and visualize ideas with countdown timers, goal tracking, and real-time collaboration.
                  </Text>
                </div>
                
                {/* Social Links */}
                <div>
                  <Text size="3" weight="medium" className="text-gray-700 mb-3 block">
                    Follow Us
                  </Text>
                  <Flex gap="3">
                    {socialLinks.map((social) => (
                      <a
                        key={social.label}
                        href={social.href}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`p-2 rounded-lg transition-colors hover:bg-gray-100 ${social.color}`}
                        aria-label={social.label}
                      >
                        <social.icon size={20} />
                      </a>
                    ))}
                  </Flex>
                </div>
              </Flex>
            </div>

            {/* Navigation Links */}
            <div>
              <Text size="3" weight="medium" className="text-gray-700 mb-4 block">
                Product
              </Text>
              <Flex direction="column" gap="3">
                {navigationLinks.map((link) => (
                  <Link
                    key={link.href}
                    href={link.href}
                    className="group flex items-center gap-2 text-gray-600 hover:text-blue-600 transition-colors"
                  >
                    <link.icon size={16} className="text-gray-400 group-hover:text-blue-500" />
                    <Text size="2" className="group-hover:text-blue-600">
                      {link.label}
                    </Text>
                  </Link>
                ))}
              </Flex>
            </div>

            {/* Support Links */}
            <div>
              <Text size="3" weight="medium" className="text-gray-700 mb-4 block">
                Support
              </Text>
              <Flex direction="column" gap="3">
                {supportLinks.map((link) => (
                  <Link
                    key={link.href}
                    href={link.href}
                    className="group flex items-center gap-2 text-gray-600 hover:text-blue-600 transition-colors"
                  >
                    <link.icon size={16} className="text-gray-400 group-hover:text-blue-500" />
                    <Text size="2" className="group-hover:text-blue-600">
                      {link.label}
                    </Text>
                  </Link>
                ))}
              </Flex>
            </div>

            {/* Newsletter/Contact */}
            <div>
              <Text size="3" weight="medium" className="text-gray-700 mb-4 block">
                Stay Updated
              </Text>
              <Flex direction="column" gap="4">
                <Text size="2" color="gray" className="leading-relaxed">
                  Get the latest updates about new features and improvements.
                </Text>
                <div className="space-y-3">
                  <a
                    href="/contact"
                    className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                  >
                    <Mail size={16} />
                    Contact Us
                  </a>
                  <div className="text-xs text-gray-500">
                    Questions? We're here to help!
                  </div>
                </div>
              </Flex>
            </div>
          </div>

          <Separator className="bg-gray-200" />

          {/* Bottom Footer */}
          <Flex 
            direction={{ initial: 'column', sm: 'row' }} 
            justify="between" 
            align="center" 
            gap="4"
            className="text-center sm:text-left"
          >
            <Flex direction="column" gap="2">
              <Text size="2" color="gray">
                © {currentYear} Anchorboard App. All rights reserved.
              </Text>
              <Text size="1" color="gray" className="max-w-md">
                Built with ❤️ for teams who value collaboration and visual thinking.
              </Text>
            </Flex>

            <Flex gap="6" wrap="wrap" className="text-xs">
              <Link 
                href="/privacy" 
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                Privacy
              </Link>
              <Link 
                href="/terms" 
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                Terms
              </Link>
              <Link 
                href="/contact" 
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                Support
              </Link>
              <a 
                href="mailto:<EMAIL>"
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                <EMAIL>
              </a>
            </Flex>
          </Flex>
        </Flex>
      </Container>
    </footer>
  );
}
