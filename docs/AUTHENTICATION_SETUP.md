# Authentication Header Configuration for tRPC

This document describes the authentication header configuration implemented for the tRPC client setup, enabling secure authenticated requests with BetterAuth session management.

## Overview

The authentication system has been configured to automatically include session tokens in tRPC requests, following BetterAuth best practices for secure token transmission.

## Implementation Details

### Client-Side (TRPCProvider.tsx)

#### 1. Session Token Extraction
- **Primary Method**: Extracts all BetterAuth-related cookies from `document.cookie`
- **<PERSON>ie Names Supported**:
  - `better-auth.session_token`
  - `better-auth-session`
  - `better-auth.session`
  - `session_token`
  - `auth_session`

#### 2. Dynamic Header Generation
The `getAuthHeaders()` function implements a two-tier approach:

**Method 1: Cookie-Based Authentication (Primary)**
```typescript
// Passes all BetterAuth cookies to maintain session state
headers['Cookie'] = authCookies;
```

**Method 2: Bearer Token Fallback**
```typescript
// Uses BetterAuth client to get session token
headers['Authorization'] = `Bearer ${sessionToken}`;
```

#### 3. Error Handling
- Graceful degradation when session retrieval fails
- Non-blocking errors that don't prevent tRPC requests
- Detailed error logging for debugging

#### 4. Type Safety
- Comprehensive type checking for session data structures
- Safe property access with runtime validation
- Proper TypeScript typing for all authentication functions

### Server-Side (trpc.ts)

#### 1. Session Management
The tRPC context functions handle session management through direct database lookups:
- Extracts session tokens from cookies (`better-auth.session_token`)
- Validates sessions by querying the database directly using Prisma
- Handles session expiration checking

#### 2. Context Creation
Both Pages Router and App Router contexts are supported:
- `createTRPCContext()` for Pages Router
- `createTRPCContextAppRouter()` for App Router

## Google OAuth Integration

### Setup Google OAuth Credentials

1. **Create Google Cloud Project**
   - Go to [Google Cloud Console](https://console.cloud.google.com/apis/dashboard)
   - Create a new project or select an existing one
   - Enable the Google+ API or Google Identity API

2. **Configure OAuth 2.0 Credentials**
   - Navigate to Credentials → Create Credentials → OAuth 2.0 Client IDs
   - Choose "Web application" as the application type
   - Set authorized redirect URIs:
     - Development: `http://localhost:3000/api/auth/callback/google`
     - Production: `https://your-domain.com/api/auth/callback/google`

3. **Environment Variables**
   Add the following to your `.env.local` file:
   ```env
   GOOGLE_CLIENT_ID=your_google_client_id_here
   GOOGLE_CLIENT_SECRET=your_google_client_secret_here
   ```

### Google OAuth Features

#### Basic Sign-In
```typescript
import { signInWithGoogle } from '@/lib/auth-client';

const handleGoogleSignIn = async () => {
  await signInWithGoogle();
};
```

#### Sign-In with Additional Scopes
```typescript
import { signInWithGoogleScopes } from '@/lib/auth-client';

const handleGoogleSignInWithDrive = async () => {
  await signInWithGoogleScopes(['https://www.googleapis.com/auth/drive.file']);
};
```

#### Link Google Account for Additional Permissions
```typescript
import { linkGoogleAccount } from '@/lib/auth-client';

const handleLinkGoogle = async () => {
  await linkGoogleAccount(['https://www.googleapis.com/auth/drive.file']);
};
```

### Configuration Options

The Google provider is configured with the following settings:
- `accessType: "offline"` - Always get refresh tokens
- `prompt: "select_account"` - Always ask users to select an account
- Automatic handling of OAuth flow and session creation

## Security Features

### 1. Secure Token Transmission
- **HTTP-Only Cookies**: Primary method for session storage
- **Bearer Tokens**: Fallback for programmatic access
- **Proper Encoding**: All tokens are properly encoded/decoded

### 2. Header Naming Conventions
- **Cookie Header**: `Cookie: better-auth.session_token=<token>`
- **Authorization Header**: `Authorization: Bearer <token>`

### 3. Conditional Header Inclusion
- Headers are only included when valid sessions exist
- No sensitive data is transmitted for unauthenticated requests

## Usage Examples

### Authenticated tRPC Request
```typescript
// Client automatically includes authentication headers
const whiteboards = api.whiteboard.getAll.useQuery();
```

### Manual Session Check
```typescript
import { useAuth } from '@/hooks/useAuth';

const { data: session, status } = useAuth();
if (status === 'authenticated') {
  // User is authenticated, tRPC requests will include session
}
```

## Configuration

### Environment Variables
- `BETTER_AUTH_SECRET`: Secret key for session encryption
- `BETTER_AUTH_URL`: Base URL for BetterAuth endpoints

### Cookie Configuration
BetterAuth cookies are configured with:
- `HttpOnly`: Prevents XSS attacks
- `Secure`: HTTPS-only transmission
- `SameSite`: CSRF protection

## Error Handling

### Client-Side Errors
- Session extraction failures are logged but don't block requests
- Invalid session data is handled gracefully
- Fallback mechanisms ensure app functionality

### Server-Side Errors
- Invalid tokens return null sessions (unauthenticated state)
- Malformed requests are handled safely
- Comprehensive error logging for debugging

## Testing

### Authentication Flow Testing
1. **Unauthenticated Requests**: Should work without headers
2. **Authenticated Requests**: Should include proper session tokens
3. **Invalid Sessions**: Should handle gracefully
4. **Session Expiry**: Should refresh or redirect appropriately

### Header Validation
- Verify correct cookie transmission
- Check Authorization header format
- Validate token encoding/decoding

## Best Practices Implemented

1. **Cookie-First Approach**: Leverages BetterAuth's default session storage
2. **Graceful Degradation**: App works even if authentication fails
3. **Type Safety**: Comprehensive TypeScript coverage
4. **Security**: Follows OWASP guidelines for session management
5. **Performance**: Minimal overhead for header generation
6. **Debugging**: Detailed logging for troubleshooting

## Future Enhancements

1. **Session Refresh**: Automatic token refresh before expiry
2. **Multi-Domain Support**: Cross-domain session handling
3. **Rate Limiting**: Request throttling for security
4. **Audit Logging**: Enhanced security event logging
