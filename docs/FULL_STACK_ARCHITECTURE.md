# Full-Stack Whiteboard Application Architecture

This document describes the modern full-stack architecture implemented for the whiteboard application, featuring Prisma ORM, tRPC, TanStack Query, and Zod validation.

## Architecture Overview

The application follows a modern full-stack architecture with the following key components:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Layer     │    │   Database      │
│                 │    │                 │    │                 │
│ React Flow      │◄──►│ tRPC Router     │◄──►│ SQLite/Prisma   │
│ TanStack Query  │    │ Zod Validation  │    │ Schema          │
│ Radix UI        │    │ Type Safety     │    │ Migrations      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Technology Stack

### Backend
- **Prisma ORM**: Database schema definition, migrations, and type-safe database access
- **tRPC**: Type-safe API layer with automatic TypeScript inference
- **Zod**: Runtime schema validation and type inference
- **SQLite**: Development database (easily switchable to PostgreSQL for production)

### Frontend
- **TanStack Query (React Query)**: Data fetching, caching, and synchronization
- **React Flow**: Interactive whiteboard canvas
- **Radix UI Themes**: Consistent UI components
- **TypeScript**: Full type safety across the stack

### Development Tools
- **Vitest**: Testing framework for unit and integration tests
- **tsx**: TypeScript execution for scripts and seeds
- **Prisma Studio**: Database GUI for development

## Project Structure

```
src/
├── components/
│   ├── providers/
│   │   └── TRPCProvider.tsx          # tRPC client setup
│   ├── whiteboard/
│   │   ├── WhiteboardWithTRPC.tsx    # Main whiteboard component
│   │   └── WhiteboardList.tsx        # Whiteboard management
│   ├── error/
│   │   └── ErrorBoundary.tsx         # Error handling
│   └── ui/
│       └── LoadingStates.tsx         # Loading components
├── hooks/
│   ├── useWhiteboard.ts              # Whiteboard operations hook
│   └── useErrorHandling.ts           # Error handling utilities
├── lib/
│   ├── schemas.ts                    # Zod validation schemas
│   ├── prisma.ts                     # Prisma client instance
│   └── trpc.ts                       # tRPC client configuration
├── server/
│   ├── trpc.ts                       # tRPC server setup
│   └── routers/
│       ├── _app.ts                   # Main app router
│       ├── whiteboard.ts             # Whiteboard procedures
│       ├── countdownTimer.ts         # Timer procedures
│       ├── goal.ts                   # Goal procedures
│       └── hillChart.ts              # Hill chart procedures
├── test/
│   ├── setup.ts                      # Test configuration
│   ├── schemas.test.ts               # Schema validation tests
│   ├── trpc/                         # tRPC procedure tests
│   └── hooks/                        # Hook tests
└── generated/
    └── prisma/                       # Generated Prisma client

prisma/
├── schema.prisma                     # Database schema
├── seed.ts                          # Database seed script
└── migrations/                      # Database migrations
```

## Database Schema

The application uses a comprehensive database schema designed for whiteboard collaboration:

### Core Entities

1. **Users**: User accounts and authentication
2. **Whiteboards**: Main whiteboard containers with settings
3. **WhiteboardNodes**: React Flow nodes (text, shapes, custom components)
4. **WhiteboardEdges**: Connections between nodes
5. **CountdownTimers**: Time-based elements
6. **Goals**: Task and objective tracking
7. **HillCharts**: Progress visualization charts
8. **HillChartItems**: Individual items on hill charts
9. **UserSessions**: Active user session tracking

### Key Features

- **Cascade Deletion**: Related data is automatically cleaned up
- **Flexible Node Data**: JSON storage for custom node properties
- **Viewport Persistence**: Save and restore canvas position/zoom
- **Multi-user Support**: User isolation and sharing controls

## API Design

### tRPC Procedures

The API is organized into logical routers with full type safety:

#### Whiteboard Router
- `list`: Paginated whiteboard listing with filtering
- `getById`: Single whiteboard retrieval
- `getWithData`: Whiteboard with all related data
- `create`: New whiteboard creation
- `update`: Whiteboard modification
- `delete`: Whiteboard removal
- `bulkUpdate`: Efficient batch updates for nodes/edges
- `updateViewport`: Canvas position/zoom updates
- `search`: Text-based whiteboard search

#### Feature Routers
- **CountdownTimer**: CRUD operations for timers
- **Goal**: Task management with status tracking
- **HillChart**: Progress visualization with items

### Type Safety

All API operations are fully type-safe with automatic TypeScript inference:

```typescript
// Client-side usage with full type safety
const { data: whiteboard } = api.whiteboard.getById.useQuery({ id: 'board-id' });
const createMutation = api.whiteboard.create.useMutation();

// TypeScript knows the exact shape of data and parameters
```

## Data Flow

### 1. Component → Hook → tRPC → Database

```typescript
// Component uses hook
const { createWhiteboard } = useWhiteboard();

// Hook calls tRPC mutation
await createWhiteboard({ name: 'New Board' });

// tRPC validates with Zod and calls Prisma
const result = await ctx.prisma.whiteboard.create({ data: validatedInput });
```

### 2. Real-time Updates

- **Optimistic Updates**: Immediate UI feedback
- **Background Sync**: Automatic data synchronization
- **Error Recovery**: Rollback on failure
- **Cache Invalidation**: Smart query refreshing

### 3. Error Handling

- **Zod Validation**: Input validation with detailed error messages
- **tRPC Error Codes**: Standardized error responses
- **Error Boundaries**: UI error containment
- **User Feedback**: Meaningful error messages

## Performance Optimizations

### Database
- **Efficient Queries**: Optimized Prisma queries with includes
- **Batch Operations**: Bulk updates for better performance
- **Indexes**: Strategic database indexing
- **Connection Pooling**: Efficient database connections

### Frontend
- **Query Caching**: TanStack Query automatic caching
- **Background Updates**: Non-blocking data fetching
- **Optimistic Updates**: Immediate UI responsiveness
- **Code Splitting**: Lazy loading of components

### API
- **Batch Requests**: tRPC request batching
- **Type-safe Serialization**: Efficient data transfer
- **Request Deduplication**: Automatic duplicate request handling

## Development Workflow

### Database Operations

```bash
# Generate Prisma client
pnpm db:generate

# Create and apply migration
pnpm db:migrate

# Seed database with sample data
pnpm db:seed

# Open Prisma Studio
pnpm db:studio

# Reset database
pnpm db:migrate:reset
```

### Testing

```bash
# Run all tests
pnpm test:run

# Run tests in watch mode
pnpm test

# Run tests with UI
pnpm test:ui

# Generate coverage report
pnpm test:coverage
```

### Type Safety

The entire stack maintains type safety from database to UI:

1. **Database Schema** → Prisma generates TypeScript types
2. **Zod Schemas** → Runtime validation with type inference
3. **tRPC Procedures** → API type safety
4. **React Components** → UI type safety

## Deployment Considerations

### Environment Variables

```env
# Database
DATABASE_URL="file:./dev.db"

# Production database example
# DATABASE_URL="postgresql://user:password@host:port/database"
```

### Production Setup

1. **Database Migration**: Use `pnpm db:deploy` for production
2. **Environment Config**: Set production DATABASE_URL
3. **Build Process**: Next.js handles tRPC compilation
4. **Error Monitoring**: Implement error tracking service

## Best Practices

### Schema Design
- Use descriptive field names
- Implement proper relationships
- Add appropriate constraints
- Plan for data growth

### API Design
- Keep procedures focused and single-purpose
- Use proper error codes
- Implement pagination for lists
- Validate all inputs with Zod

### Frontend Integration
- Use custom hooks for data operations
- Implement proper loading states
- Handle errors gracefully
- Optimize for performance

### Testing
- Test schema validation
- Test tRPC procedures
- Test React components
- Test error scenarios

This architecture provides a solid foundation for building scalable, type-safe, and maintainable whiteboard applications with excellent developer experience and user performance.
