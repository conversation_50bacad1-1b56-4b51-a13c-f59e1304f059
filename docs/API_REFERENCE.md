# API Reference

This document provides a comprehensive reference for the tRPC API endpoints available in the whiteboard application.

## Base URL

All API endpoints are available under `/api/trpc/` when using the tRPC client.

## Authentication

Currently, the API uses a simple user ID system. In production, you should implement proper authentication middleware.

## Error Handling

All endpoints return standardized tRPC errors with the following codes:

- `BAD_REQUEST`: Invalid input data
- `UNAUTHORIZED`: Authentication required
- `FORBIDDEN`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `CONFLICT`: Resource conflict
- `INTERNAL_SERVER_ERROR`: Server error

## Whiteboard API

### `whiteboard.list`

Get a paginated list of whiteboards.

**Input:**
```typescript
{
  page?: number;        // Default: 1
  limit?: number;       // Default: 10, Max: 100
  userId?: string;      // Filter by user ID
}
```

**Output:**
```typescript
{
  whiteboards: Whiteboard[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
```

**Example:**
```typescript
const { data } = api.whiteboard.list.useQuery({
  page: 1,
  limit: 20,
  userId: 'user-123'
});
```

### `whiteboard.getById`

Get a single whiteboard by ID.

**Input:**
```typescript
{
  id: string;
}
```

**Output:**
```typescript
Whiteboard | null
```

**Example:**
```typescript
const { data } = api.whiteboard.getById.useQuery({
  id: 'whiteboard-123'
});
```

### `whiteboard.getWithData`

Get a whiteboard with all related data (nodes, edges, timers, goals, hill charts).

**Input:**
```typescript
{
  id: string;
}
```

**Output:**
```typescript
{
  ...Whiteboard,
  nodes: WhiteboardNode[];
  edges: WhiteboardEdge[];
  countdownTimers: CountdownTimer[];
  goals: Goal[];
  hillCharts: (HillChart & { items: HillChartItem[] })[];
}
```

**Example:**
```typescript
const { data } = api.whiteboard.getWithData.useQuery({
  id: 'whiteboard-123'
});
```

### `whiteboard.create`

Create a new whiteboard.

**Input:**
```typescript
{
  name: string;
  userId: string;
  isPublic?: boolean;           // Default: false
  allowComments?: boolean;      // Default: true
  viewMode?: 'edit' | 'view';   // Default: 'edit'
  backgroundColor?: string;     // Default: '#ffffff'
  gridMode?: boolean;           // Default: false
  snapToGrid?: boolean;         // Default: false
  theme?: 'light' | 'dark';     // Default: 'light'
  viewportX?: number;           // Default: 0
  viewportY?: number;           // Default: 0
  viewportZoom?: number;        // Default: 1
}
```

**Output:**
```typescript
Whiteboard
```

**Example:**
```typescript
const createMutation = api.whiteboard.create.useMutation();
const newWhiteboard = await createMutation.mutateAsync({
  name: 'My New Whiteboard',
  userId: 'user-123',
  theme: 'dark'
});
```

### `whiteboard.update`

Update an existing whiteboard.

**Input:**
```typescript
{
  id: string;
  data: Partial<CreateWhiteboardInput>;
}
```

**Output:**
```typescript
Whiteboard
```

**Example:**
```typescript
const updateMutation = api.whiteboard.update.useMutation();
await updateMutation.mutateAsync({
  id: 'whiteboard-123',
  data: {
    name: 'Updated Name',
    backgroundColor: '#f0f0f0'
  }
});
```

### `whiteboard.delete`

Delete a whiteboard and all related data.

**Input:**
```typescript
{
  id: string;
}
```

**Output:**
```typescript
{
  success: boolean;
}
```

### `whiteboard.bulkUpdate`

Efficiently update whiteboard nodes, edges, and viewport in a single transaction.

**Input:**
```typescript
{
  id: string;
  nodes: {
    nodeId: string;
    type: string;
    positionX: number;
    positionY: number;
    width?: number;
    height?: number;
    data: Record<string, unknown>;
  }[];
  edges: {
    edgeId: string;
    source: string;
    target: string;
    type?: string;
    data?: Record<string, unknown>;
  }[];
  viewport: {
    x: number;
    y: number;
    zoom: number;
  };
}
```

**Output:**
```typescript
{
  success: boolean;
}
```

### `whiteboard.updateViewport`

Update only the viewport settings.

**Input:**
```typescript
{
  id: string;
  viewportX: number;
  viewportY: number;
  viewportZoom: number;  // Min: 0.1, Max: 10
}
```

**Output:**
```typescript
Whiteboard
```

### `whiteboard.search`

Search whiteboards by name.

**Input:**
```typescript
{
  query: string;
  userId?: string;
  page?: number;        // Default: 1
  limit?: number;       // Default: 10
}
```

**Output:**
```typescript
{
  whiteboards: Whiteboard[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
```

## Countdown Timer API

### `countdownTimer.getByWhiteboardId`

Get all countdown timers for a whiteboard.

**Input:**
```typescript
{
  whiteboardId: string;
}
```

**Output:**
```typescript
CountdownTimer[]
```

### `countdownTimer.create`

Create a new countdown timer.

**Input:**
```typescript
{
  whiteboardId: string;
  title: string;
  endDate: Date;
  positionX: number;
  positionY: number;
  isActive?: boolean;           // Default: true
  color?: string;               // Default: '#000000'
  fontSize?: number;            // Default: 16
  backgroundColor?: string;
}
```

**Output:**
```typescript
CountdownTimer
```

### `countdownTimer.update`

Update a countdown timer.

**Input:**
```typescript
{
  id: string;
  data: Partial<CreateCountdownTimerInput>;
}
```

**Output:**
```typescript
CountdownTimer
```

### `countdownTimer.delete`

Delete a countdown timer.

**Input:**
```typescript
{
  id: string;
}
```

**Output:**
```typescript
{
  success: boolean;
}
```

### `countdownTimer.toggleActive`

Toggle the active state of a timer.

**Input:**
```typescript
{
  id: string;
}
```

**Output:**
```typescript
CountdownTimer
```

### `countdownTimer.getActiveByWhiteboardId`

Get only active timers for a whiteboard, ordered by end date.

**Input:**
```typescript
{
  whiteboardId: string;
}
```

**Output:**
```typescript
CountdownTimer[]
```

## Goal API

### `goal.getByWhiteboardId`

Get all goals for a whiteboard.

**Input:**
```typescript
{
  whiteboardId: string;
}
```

**Output:**
```typescript
Goal[]
```

### `goal.create`

Create a new goal.

**Input:**
```typescript
{
  whiteboardId: string;
  title: string;
  description?: string;
  dueDate?: Date;
  priority?: 'low' | 'medium' | 'high';     // Default: 'medium'
  status?: 'not-started' | 'in-progress' | 'completed';  // Default: 'not-started'
  positionX: number;
  positionY: number;
  tags?: string;                            // Comma-separated
}
```

**Output:**
```typescript
Goal
```

### `goal.updateStatus`

Update the status of a goal.

**Input:**
```typescript
{
  id: string;
  status: 'not-started' | 'in-progress' | 'completed';
}
```

**Output:**
```typescript
Goal
```

### `goal.getByStatus`

Get goals filtered by status.

**Input:**
```typescript
{
  whiteboardId: string;
  status: 'not-started' | 'in-progress' | 'completed';
}
```

**Output:**
```typescript
Goal[]
```

### `goal.getOverdue`

Get overdue goals (past due date and not completed).

**Input:**
```typescript
{
  whiteboardId: string;
}
```

**Output:**
```typescript
Goal[]
```

## Hill Chart API

### `hillChart.getByWhiteboardId`

Get all hill charts for a whiteboard with their items.

**Input:**
```typescript
{
  whiteboardId: string;
}
```

**Output:**
```typescript
(HillChart & { items: HillChartItem[] })[]
```

### `hillChart.create`

Create a new hill chart.

**Input:**
```typescript
{
  whiteboardId: string;
  title: string;
  positionX: number;
  positionY: number;
  width?: number;               // Default: 400
  height?: number;              // Default: 200
}
```

**Output:**
```typescript
HillChart
```

### `hillChart.items.create`

Create a new hill chart item.

**Input:**
```typescript
{
  hillChartId: string;
  name: string;
  position: number;             // 0-100
  color: string;                // Hex color
  description?: string;
}
```

**Output:**
```typescript
HillChartItem
```

### `hillChart.items.updatePosition`

Update an item's position on the hill.

**Input:**
```typescript
{
  id: string;
  position: number;             // 0-100
}
```

**Output:**
```typescript
HillChartItem
```

## Usage Examples

### Creating a Complete Whiteboard

```typescript
// 1. Create whiteboard
const whiteboard = await api.whiteboard.create.mutate({
  name: 'Project Planning',
  userId: 'user-123'
});

// 2. Add a countdown timer
const timer = await api.countdownTimer.create.mutate({
  whiteboardId: whiteboard.id,
  title: 'Sprint End',
  endDate: new Date('2024-02-01'),
  positionX: 100,
  positionY: 100
});

// 3. Add a goal
const goal = await api.goal.create.mutate({
  whiteboardId: whiteboard.id,
  title: 'Complete Feature X',
  priority: 'high',
  positionX: 300,
  positionY: 100
});

// 4. Save whiteboard data
await api.whiteboard.bulkUpdate.mutate({
  id: whiteboard.id,
  nodes: [
    {
      nodeId: 'node-1',
      type: 'textNode',
      positionX: 500,
      positionY: 100,
      data: { label: 'Welcome!' }
    }
  ],
  edges: [],
  viewport: { x: 0, y: 0, zoom: 1 }
});
```

### Real-time Updates with TanStack Query

```typescript
// Component automatically updates when data changes
function WhiteboardComponent({ whiteboardId }: { whiteboardId: string }) {
  const { data: whiteboard, isLoading } = api.whiteboard.getWithData.useQuery({
    id: whiteboardId
  });

  const updateMutation = api.whiteboard.update.useMutation({
    onSuccess: () => {
      // Automatically invalidates and refetches the query
      utils.whiteboard.getWithData.invalidate({ id: whiteboardId });
    }
  });

  if (isLoading) return <div>Loading...</div>;
  
  return (
    <div>
      <h1>{whiteboard?.name}</h1>
      {/* Render whiteboard content */}
    </div>
  );
}
```
