# Logging with Winston and Logtail

This application uses <PERSON> for structured logging with Logtail integration for centralized log management.

## Setup

### 1. Environment Variables

Add these environment variables to your `.env.local` file:

```env
# Logging Configuration
LOGTAIL_SOURCE_TOKEN=your_logtail_source_token_here
LOGTAIL_ENDPOINT=https://in.logtail.com
LOG_LEVEL=info
```

### 2. Getting Your Logtail Source Token

1. Sign up at [Logtail.com](https://logtail.com)
2. Create a new source
3. Copy the source token
4. Add it to your environment variables

## Usage

### Basic Logging

```typescript
import { logInfo, logError, logWarn, logDebug } from '@/lib/logger';

// Basic logging
logInfo('User logged in successfully');
logWarn('Rate limit approaching');
logError('Database connection failed');
logDebug('Processing user data');
```

### Structured Logging with Metadata

```typescript
import { logInfo, logError } from '@/lib/logger';

// With metadata
logInfo('User action completed', {
  userId: 'user123',
  action: 'create_whiteboard',
  duration: 150,
  timestamp: new Date().toISOString()
});

logError('API request failed', new Error('Connection timeout'), {
  endpoint: '/api/whiteboards',
  method: 'POST',
  statusCode: 500
});
```

### Authentication Logging

```typescript
import { logAuthSuccess, logAuthError, logAuthAttempt } from '@/lib/logger';

// Authentication events
logAuthAttempt('login', { email: '<EMAIL>' });
logAuthSuccess('user123', 'login', { method: 'email' });
logAuthError('login', new Error('Invalid credentials'), { email: '<EMAIL>' });
```

### Database Logging

```typescript
import { logDatabaseQuery, logDatabaseError } from '@/lib/logger';

// Database operations
logDatabaseQuery('SELECT * FROM whiteboards WHERE userId = ?', 45, {
  userId: 'user123',
  resultCount: 5
});

logDatabaseError('create whiteboard', error, {
  userId: 'user123',
  operation: 'INSERT'
});
```

### API Logging

```typescript
import { logApiRequest, logApiResponse, logApiError } from '@/lib/logger';

// API operations
logApiRequest('POST', '/api/whiteboards', 'user123');
logApiResponse('POST', '/api/whiteboards', 201, 120);
logApiError('POST', '/api/whiteboards', error);
```

## Log Levels

The logger supports the following levels (in order of priority):

1. **error** - Error events that might still allow the application to continue
2. **warn** - Warning events that indicate potential issues
3. **info** - Informational messages that highlight application progress
4. **http** - HTTP request/response logging
5. **debug** - Detailed information for debugging purposes

Set the `LOG_LEVEL` environment variable to control which logs are captured:

- `error`: Only error logs
- `warn`: Error and warning logs
- `info`: Error, warning, and info logs (recommended for production)
- `http`: All above plus HTTP logs
- `debug`: All logs (recommended for development)

## Transports

### Development
- **Console**: Colored, formatted logs for easy reading
- **Logtail**: If configured, sends logs to Logtail for centralized management

### Production
- **File**: Writes logs to `logs/combined.log` and `logs/error.log`
- **Logtail**: Centralized log management and monitoring

## Best Practices

### 1. Use Appropriate Log Levels
```typescript
// Good
logDebug('Processing user input', { inputLength: data.length });
logInfo('User created whiteboard', { userId, whiteboardId });
logWarn('High memory usage detected', { memoryUsage: '85%' });
logError('Failed to save whiteboard', error, { userId, whiteboardId });

// Avoid
logError('User clicked button'); // This should be debug
logDebug('Database connection failed'); // This should be error
```

### 2. Include Relevant Context
```typescript
// Good
logInfo('Whiteboard updated', {
  userId: 'user123',
  whiteboardId: 'wb456',
  changes: ['title', 'content'],
  duration: 120
});

// Less helpful
logInfo('Whiteboard updated');
```

### 3. Handle Errors Properly
```typescript
// Good
try {
  await updateWhiteboard(id, data);
  logInfo('Whiteboard updated successfully', { whiteboardId: id });
} catch (error) {
  logError('Failed to update whiteboard', error, { whiteboardId: id });
  throw error; // Re-throw if needed
}
```

### 4. Use Structured Data
```typescript
// Good - structured data
logInfo('User session started', {
  userId: 'user123',
  sessionId: 'sess456',
  userAgent: req.headers['user-agent'],
  ip: req.ip
});

// Less searchable
logInfo(`User user123 started session sess456 from ${req.ip}`);
```

## Monitoring and Alerts

With Logtail integration, you can:

1. **Search and Filter**: Find specific logs using structured data
2. **Create Dashboards**: Visualize application metrics and trends
3. **Set Up Alerts**: Get notified of critical errors or unusual patterns
4. **Monitor Performance**: Track response times and error rates

## Security Considerations

- Never log sensitive information (passwords, tokens, personal data)
- Use metadata fields for structured data instead of interpolating into messages
- Consider log retention policies for compliance
- Ensure log files have appropriate permissions in production

## Example Integration

Here's how the logger is integrated into a tRPC procedure:

```typescript
export const whiteboardRouter = createTRPCRouter({
  create: protectedProcedure
    .input(CreateWhiteboardSchema)
    .mutation(async ({ ctx, input }) => {
      const startTime = Date.now();
      const userId = ctx.session.user.id;

      try {
        logInfo('Creating new whiteboard', { userId, title: input.title });

        const whiteboard = await ctx.prisma.whiteboard.create({
          data: {
            ...input,
            userId,
          },
        });

        const duration = Date.now() - startTime;
        logDatabaseQuery('create whiteboard', duration, {
          userId,
          whiteboardId: whiteboard.id,
        });

        logInfo('Whiteboard created successfully', {
          userId,
          whiteboardId: whiteboard.id,
          title: whiteboard.title,
          duration,
        });

        return whiteboard;
      } catch (error) {
        logDatabaseError('create whiteboard', error, {
          userId,
          title: input.title,
          duration: Date.now() - startTime,
        });
        throw error;
      }
    }),
});
```

This comprehensive logging setup provides excellent observability for your whiteboard application!
