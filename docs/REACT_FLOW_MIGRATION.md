# React Flow Migration Guide

This document outlines the migration from Excalidraw to React Flow for the whiteboard feature.

## Overview

The whiteboard feature has been completely refactored to use React Flow instead of Excalidraw. This change provides better performance, more customization options, and improved integration with our existing UI components.

## Key Changes

### 1. Core Library Replacement
- **Before**: `@excalidraw/excalidraw`
- **After**: `reactflow` with additional plugins:
  - `@reactflow/controls`
  - `@reactflow/minimap`
  - `@reactflow/background`
  - `@reactflow/node-toolbar`
  - `@reactflow/node-resizer`

### 2. Component Structure
- **Before**: `ExcalidrawWrapper.tsx`
- **After**: `ReactFlowWrapper.tsx`

### 3. Data Format
- **Before**: Excalidraw elements and app state
- **After**: React Flow nodes, edges, and viewport

### 4. Type Definitions
Updated `src/types/index.ts` to include:
- `ReactFlowData` interface
- `CustomNode` and `CustomEdge` types
- React Flow specific types

## New Features

### Node Types
The new implementation includes three built-in node types:

1. **Text Node** (`textNode`): Simple text display
2. **Rectangle Node** (`rectangleNode`): Rectangular shapes with text
3. **Circle Node** (`circleNode`): Circular shapes with text

### Node Creation
- **Toolbar**: Click buttons to create specific node types
- **Double-click**: Double-click on canvas to create text nodes
- **Programmatic**: Use the `addNode` utility function

### Export Options
- **PNG**: Raster image export
- **SVG**: Vector graphics export
- **JSON**: Data export for backup/sharing

## API Changes

### Props Interface
```typescript
interface ReactFlowWrapperProps {
  whiteboardData?: WhiteboardData;
  onSave?: (nodes: Node[], edges: Edge[], viewport: Viewport) => void;
  settings?: WhiteboardSettings;
  className?: string;
  onExport?: (format: 'png' | 'svg' | 'json') => void;
}
```

### Data Transformation
New utility functions in `src/lib/whiteboard-utils.ts`:
- `transformReactFlowDataForStorage()`
- `transformStoredDataToReactFlow()`
- `createDefaultNode()`
- `validateReactFlowData()`
- `migrateExcalidrawToReactFlow()`
- `exportReactFlowData()`

## Migration Process

### 1. Dependency Updates
```bash
# Remove Excalidraw
bun remove @excalidraw/excalidraw

# Add React Flow
bun add reactflow @reactflow/controls @reactflow/minimap @reactflow/background @reactflow/node-toolbar @reactflow/node-resizer
```

### 2. Component Updates
- Replaced `ExcalidrawWrapper` with `ReactFlowWrapper`
- Updated `Whiteboard.tsx` to use React Flow data structures
- Modified state management to handle nodes, edges, and viewport

### 3. Data Migration
Existing Excalidraw data is automatically migrated using the `migrateExcalidrawToReactFlow()` function.

## Performance Improvements

### Lazy Loading
React Flow components are dynamically imported to reduce initial bundle size:
```typescript
const ReactFlow = dynamic(() => import('reactflow'), { ssr: false });
```

### Performance Monitoring
Load time tracking and analytics integration for performance monitoring.

### Code Splitting
Heavy components are lazy-loaded to improve initial page load performance.

## Testing

Comprehensive test suite includes:
- Unit tests for utility functions
- Component tests for React Flow wrapper
- Integration tests for the main Whiteboard component
- Data transformation and validation tests

Run tests with:
```bash
bun test src/test/whiteboard/ src/test/lib/whiteboard-utils.test.ts
```

## Backward Compatibility

The migration maintains backward compatibility:
- Existing whiteboard data is automatically migrated
- Settings interface remains unchanged
- Export functionality is preserved with enhanced options

## Configuration

### Settings Integration
All existing whiteboard settings work with React Flow:
- `theme`: Light/dark mode support
- `gridMode`: Background grid display
- `snapToGrid`: Node snapping functionality
- `backgroundColor`: Canvas background color
- `viewMode`: Edit/view mode switching

### Customization
Node types can be easily extended by:
1. Creating new node components
2. Adding them to the `nodeTypes` object
3. Updating the toolbar with new creation buttons

## Troubleshooting

### Common Issues
1. **CSS not loading**: Ensure `reactflow/dist/style.css` is imported
2. **Performance issues**: Check if components are properly lazy-loaded
3. **Data migration**: Use validation functions to check data integrity

### Debug Mode
Development mode includes:
- Load time indicators
- Performance metrics
- Console logging for data transformations

## Future Enhancements

Planned improvements:
- Custom edge types
- Advanced node editing
- Collaborative features
- Enhanced export options
- Plugin system for custom node types

## Support

For issues or questions regarding the React Flow migration:
1. Check the test files for usage examples
2. Review the utility functions for data handling
3. Consult React Flow documentation: https://reactflow.dev/
