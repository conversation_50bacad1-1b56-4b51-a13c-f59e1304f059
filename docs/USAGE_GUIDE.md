# Usage Guide

This guide shows you how to use the full-stack whiteboard application with practical examples and best practices.

## Getting Started

### 1. Setup and Installation

```bash
# Install dependencies
pnpm install

# Set up the database
pnpm db:generate
pnpm db:migrate
pnpm db:seed

# Start the development server
pnpm dev
```

### 2. Environment Configuration

Create a `.env` file with the following variables:

```env
# Database
DATABASE_URL="file:./dev.db"

# For production with PostgreSQL:
# DATABASE_URL="postgresql://username:password@localhost:5432/whiteboard_db"
```

## Basic Usage

### Creating a New Whiteboard

```typescript
import { useWhiteboard } from '@/hooks/useWhiteboard';

function CreateWhiteboardButton() {
  const { createWhiteboard, isLoading } = useWhiteboard();

  const handleCreate = async () => {
    try {
      const newWhiteboard = await createWhiteboard({
        name: 'My New Whiteboard',
        theme: 'light',
        isPublic: false
      });
      
      console.log('Created whiteboard:', newWhiteboard.id);
      // Redirect to the new whiteboard
      router.push(`/whiteboard/${newWhiteboard.id}`);
    } catch (error) {
      console.error('Failed to create whiteboard:', error);
    }
  };

  return (
    <Button 
      onClick={handleCreate} 
      disabled={isLoading.createWhiteboard}
    >
      {isLoading.createWhiteboard ? 'Creating...' : 'New Whiteboard'}
    </Button>
  );
}
```

### Loading and Displaying Whiteboards

```typescript
import { WhiteboardList } from '@/components/whiteboard/WhiteboardList';

function WhiteboardsPage() {
  return (
    <div>
      <h1>My Whiteboards</h1>
      <WhiteboardList userId="current-user-id" />
    </div>
  );
}
```

### Using the Whiteboard Component

```typescript
import { WhiteboardWithTRPC } from '@/components/whiteboard/WhiteboardWithTRPC';

function WhiteboardPage({ params }: { params: { id: string } }) {
  return (
    <WhiteboardWithTRPC 
      whiteboardId={params.id}
      isReadOnly={false}
    />
  );
}
```

## Advanced Features

### Adding Custom Elements

#### Countdown Timers

```typescript
function AddTimerButton({ whiteboardId }: { whiteboardId: string }) {
  const { createCountdownTimer } = useWhiteboard();

  const handleAddTimer = async () => {
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 7); // 1 week from now

    await createCountdownTimer({
      whiteboardId,
      title: 'Sprint Deadline',
      endDate,
      positionX: 100,
      positionY: 100,
      color: '#ff0000',
      fontSize: 18
    });
  };

  return <Button onClick={handleAddTimer}>Add Timer</Button>;
}
```

#### Goals

```typescript
function AddGoalButton({ whiteboardId }: { whiteboardId: string }) {
  const { createGoal } = useWhiteboard();

  const handleAddGoal = async () => {
    await createGoal({
      whiteboardId,
      title: 'Complete User Research',
      description: 'Conduct interviews with 10 users',
      priority: 'high',
      status: 'not-started',
      positionX: 200,
      positionY: 200,
      tags: 'research,users'
    });
  };

  return <Button onClick={handleAddGoal}>Add Goal</Button>;
}
```

#### Hill Charts

```typescript
function AddHillChartButton({ whiteboardId }: { whiteboardId: string }) {
  const { createHillChart } = useWhiteboard();

  const handleAddHillChart = async () => {
    await createHillChart({
      whiteboardId,
      title: 'Project Progress',
      positionX: 300,
      positionY: 300,
      width: 500,
      height: 250
    });
  };

  return <Button onClick={handleAddHillChart}>Add Hill Chart</Button>;
}
```

### Real-time Data Synchronization

The application automatically handles real-time updates using TanStack Query:

```typescript
function WhiteboardStatus({ whiteboardId }: { whiteboardId: string }) {
  // This query automatically refetches when data changes
  const { data: whiteboard, isLoading } = api.whiteboard.getWithData.useQuery({
    id: whiteboardId
  }, {
    refetchInterval: 30000, // Refetch every 30 seconds
    staleTime: 10000,       // Consider data stale after 10 seconds
  });

  if (isLoading) return <Spinner />;

  return (
    <div>
      <p>Nodes: {whiteboard?.nodes.length}</p>
      <p>Edges: {whiteboard?.edges.length}</p>
      <p>Timers: {whiteboard?.countdownTimers.length}</p>
      <p>Goals: {whiteboard?.goals.length}</p>
    </div>
  );
}
```

### Error Handling

```typescript
import { useErrorHandling } from '@/hooks/useErrorHandling';

function WhiteboardOperations({ whiteboardId }: { whiteboardId: string }) {
  const { handleTRPCError, getError, clearError } = useErrorHandling();
  const { updateWhiteboard } = useWhiteboard();

  const handleUpdate = async (data: any) => {
    try {
      clearError('update-whiteboard');
      await updateWhiteboard(whiteboardId, data);
    } catch (error) {
      handleTRPCError(error, 'update-whiteboard', {
        showToast: true,
        logError: true
      });
    }
  };

  const error = getError('update-whiteboard');

  return (
    <div>
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error.message}</AlertDescription>
        </Alert>
      )}
      {/* Your UI components */}
    </div>
  );
}
```

### Optimistic Updates

```typescript
function QuickUpdateExample({ whiteboardId }: { whiteboardId: string }) {
  const utils = api.useUtils();
  
  const updateMutation = api.whiteboard.update.useMutation({
    // Optimistic update
    onMutate: async (newData) => {
      // Cancel outgoing refetches
      await utils.whiteboard.getById.cancel({ id: whiteboardId });

      // Snapshot the previous value
      const previousWhiteboard = utils.whiteboard.getById.getData({ id: whiteboardId });

      // Optimistically update to the new value
      utils.whiteboard.getById.setData({ id: whiteboardId }, (old) => 
        old ? { ...old, ...newData.data } : undefined
      );

      return { previousWhiteboard };
    },
    
    // Rollback on error
    onError: (err, newData, context) => {
      utils.whiteboard.getById.setData(
        { id: whiteboardId }, 
        context?.previousWhiteboard
      );
    },
    
    // Always refetch after error or success
    onSettled: () => {
      utils.whiteboard.getById.invalidate({ id: whiteboardId });
    },
  });

  return (
    <Button onClick={() => updateMutation.mutate({
      id: whiteboardId,
      data: { name: 'Updated Name' }
    })}>
      Quick Update
    </Button>
  );
}
```

## Performance Optimization

### Efficient Data Loading

```typescript
// Load only what you need
function WhiteboardSummary({ whiteboardId }: { whiteboardId: string }) {
  // Use getById for basic info (lighter query)
  const { data: whiteboard } = api.whiteboard.getById.useQuery({
    id: whiteboardId
  });

  return <h1>{whiteboard?.name}</h1>;
}

function WhiteboardDetails({ whiteboardId }: { whiteboardId: string }) {
  // Use getWithData only when you need all related data
  const { data: fullWhiteboard } = api.whiteboard.getWithData.useQuery({
    id: whiteboardId
  });

  return (
    <div>
      {/* Render full whiteboard with all data */}
    </div>
  );
}
```

### Batch Operations

```typescript
function SaveWhiteboardChanges({ whiteboardId }: { whiteboardId: string }) {
  const { saveWhiteboardData } = useWhiteboard();

  const handleSave = async (nodes: any[], edges: any[], viewport: any) => {
    // This uses bulkUpdate internally for efficiency
    await saveWhiteboardData(whiteboardId, nodes, edges, viewport);
  };

  return (
    <Button onClick={() => handleSave(nodes, edges, viewport)}>
      Save All Changes
    </Button>
  );
}
```

### Background Sync

```typescript
function AutoSaveWhiteboard({ whiteboardId }: { whiteboardId: string }) {
  const [hasChanges, setHasChanges] = useState(false);
  const { saveWhiteboardData } = useWhiteboard();

  // Auto-save every 30 seconds if there are changes
  useEffect(() => {
    if (!hasChanges) return;

    const timer = setTimeout(async () => {
      await saveWhiteboardData(whiteboardId, nodes, edges, viewport);
      setHasChanges(false);
    }, 30000);

    return () => clearTimeout(timer);
  }, [hasChanges, whiteboardId]);

  return (
    <div>
      {hasChanges && <Badge>Unsaved changes</Badge>}
      {/* Your whiteboard UI */}
    </div>
  );
}
```

## Testing

### Testing Components

```typescript
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { WhiteboardList } from '@/components/whiteboard/WhiteboardList';

function createTestWrapper() {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } }
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}

test('renders whiteboard list', () => {
  render(<WhiteboardList userId="test-user" />, {
    wrapper: createTestWrapper()
  });

  expect(screen.getByText('Whiteboards')).toBeInTheDocument();
});
```

### Testing Hooks

```typescript
import { renderHook, waitFor } from '@testing-library/react';
import { useWhiteboard } from '@/hooks/useWhiteboard';

test('creates whiteboard successfully', async () => {
  const { result } = renderHook(() => useWhiteboard(), {
    wrapper: createTestWrapper()
  });

  await waitFor(() => {
    expect(result.current.createWhiteboard).toBeDefined();
  });

  // Test the hook functionality
  const newWhiteboard = await result.current.createWhiteboard({
    name: 'Test Board'
  });

  expect(newWhiteboard.name).toBe('Test Board');
});
```

## Deployment

### Production Database Setup

1. **PostgreSQL Setup:**
```bash
# Update DATABASE_URL in production
DATABASE_URL="postgresql://username:password@host:port/database"

# Run migrations
pnpm db:deploy
```

2. **Environment Variables:**
```env
NODE_ENV=production
DATABASE_URL="your-production-database-url"
```

3. **Build and Deploy:**
```bash
pnpm build
pnpm start
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors:**
   - Check DATABASE_URL format
   - Ensure database server is running
   - Verify network connectivity

2. **Type Errors:**
   - Run `pnpm db:generate` after schema changes
   - Restart TypeScript server in your IDE

3. **Query Failures:**
   - Check network connectivity
   - Verify API endpoint configuration
   - Review error logs for details

4. **Performance Issues:**
   - Use React DevTools Profiler
   - Check TanStack Query DevTools
   - Monitor database query performance

### Debug Mode

Enable debug logging in development:

```typescript
// In your tRPC client configuration
const trpcClient = api.createClient({
  links: [
    loggerLink({
      enabled: (opts) => process.env.NODE_ENV === 'development'
    }),
    // ... other links
  ],
});
```

This comprehensive guide should help you effectively use and extend the full-stack whiteboard application!
