{"name": "nextjs-shadcn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0 --turbopack", "prebuild": "bun db:generate", "build": "next build", "1postbuild": "npx @better-auth/cli@latest migrate -y", "start": "next start", "lint": "bunx biome lint --write && bunx tsc --noEmit", "seo:check": "node scripts/seo-check.js", "seo:audit": "npm run seo:check && echo 'SEO检查完成'", "seo:assets": "node scripts/generate-seo-assets.js", "seo:monitor": "node scripts/seo-monitor.js", "seo:dashboard": "node scripts/seo-dashboard.js", "perf:audit": "node scripts/performance-audit.js", "format": "bunx biome format --write", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "db:init": "node scripts/init-db.js", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:migrate:reset": "prisma migrate reset", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:push": "prisma db push", "db:deploy": "prisma migrate deploy", "db:test": "tsx scripts/test-db-connection.ts", "env:check": "tsx scripts/check-env.ts"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@logtail/node": "^0.5.5", "@logtail/winston": "^0.5.5", "@prisma/client": "^6.10.1", "@radix-ui/themes": "^3.2.1", "@reactflow/background": "^11.3.14", "@reactflow/controls": "^11.2.14", "@reactflow/minimap": "^11.7.14", "@reactflow/node-resizer": "^2.2.14", "@reactflow/node-toolbar": "^1.3.14", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@trpc/client": "^11.4.3", "@trpc/next": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "@types/better-sqlite3": "^7.6.13", "@types/pg": "^8.15.4", "ai": "^4.3.16", "better-auth": "^1.2.12", "better-sqlite3": "^12.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.1.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "kysely": "^0.28.2", "lucide-react": "^0.475.0", "next": "^15.3.2", "pg": "^8.16.3", "prisma": "^6.10.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-use": "^17.6.0", "reactflow": "^11.11.4", "same-runtime": "^0.0.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.6", "superjson": "^2.2.2", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.5", "winston": "^3.17.0", "zod": "^3.25.75"}, "devDependencies": {"@better-auth/cli": "^1.2.12", "@biomejs/biome": "1.9.4", "@eslint/eslintrc": "^3.3.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jspdf": "^2.0.0", "@types/node": "^20.17.50", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.6.0", "@vitest/ui": "^3.2.4", "eslint": "^9.27.0", "eslint-config-next": "15.1.7", "jsdom": "^26.1.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "^5.8.3", "vitest": "^3.2.4"}}