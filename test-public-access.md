# Public Whiteboard Access Fix

## Problem
When users marked a whiteboard as "public" via the UI toggle, the whiteboard was still not accessible to anonymous users because the access control logic required additional flags to be set:

- `requiresAuth` needed to be `false` (was defaulting to `true`)
- `allowAnonymousView` needed to be `true` (was defaulting to `false`)
- `publicAccessLevel` needed to be set to `'view'` (was defaulting to `'none'`)

## Solution

### 1. Server-side Changes (`src/server/routers/whiteboard.ts`)

**Added RBAC fields to update mutation input:**
```typescript
// RBAC fields for public access
publicAccessLevel: z.string().optional(),
requiresAuth: z.boolean().optional(),
allowAnonymousView: z.boolean().optional(),
```

**Added automatic public access logic:**
```typescript
// Handle public access logic
const finalUpdateData = { ...updateData };

// If isPublic is being set to true, ensure proper public access settings
if (updateData.isPublic === true) {
  finalUpdateData.requiresAuth = finalUpdateData.requiresAuth ?? false;
  finalUpdateData.allowAnonymousView = finalUpdateData.allowAnonymousView ?? true;
  finalUpdateData.publicAccessLevel = finalUpdateData.publicAccessLevel ?? 'view';
}

// If isPublic is being set to false, reset to private settings
if (updateData.isPublic === false) {
  finalUpdateData.requiresAuth = true;
  finalUpdateData.allowAnonymousView = false;
  finalUpdateData.publicAccessLevel = 'none';
}
```

### 2. Client-side Changes (`src/components/whiteboard/VisibilityToggle.tsx`)

**Updated the toggle to send all required fields:**
```typescript
await updateWhiteboard.mutateAsync({
  id: whiteboardId,
  isPublic: newIsPublic,
  // Ensure proper public access settings for anonymous users
  requiresAuth: newIsPublic ? false : true,
  allowAnonymousView: newIsPublic ? true : false,
  publicAccessLevel: newIsPublic ? 'view' : 'none',
});
```

## How to Test

1. **Start the application:**
   ```bash
   npm run dev
   ```

2. **Create a whiteboard** (requires login)

3. **Toggle it to public** using the visibility toggle

4. **Copy the whiteboard URL** (e.g., `/whiteboard/[id]`)

5. **Open incognito/private browser window** (to simulate anonymous user)

6. **Visit the whiteboard URL** - it should now load without requiring login

7. **Verify the UI shows "View Only" mode** for anonymous users

## Expected Behavior

- **Public whiteboards**: Anonymous users can view but not edit
- **Private whiteboards**: Anonymous users get access denied
- **UI indicators**: Public whiteboards show appropriate visual indicators

## Database Changes

When a whiteboard is toggled to public, these fields are automatically updated:

```sql
UPDATE whiteboard SET 
  isPublic = true,
  requiresAuth = false,
  allowAnonymousView = true,
  publicAccessLevel = 'view'
WHERE id = '[whiteboard-id]';
```

This ensures the RBAC system properly grants VIEW permission to anonymous users.