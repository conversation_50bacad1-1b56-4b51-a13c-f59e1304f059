const { Pool } = require('pg');

const pool = new Pool({
  connectionString: "postgresql://i9438d:<EMAIL>/kun:main",
  ssl: false // Xata doesn't support SSL
});

async function setupDatabase() {
  try {
    console.log('Setting up better-auth database tables...');
    
    // Create users table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS "user" (
        "id" TEXT PRIMARY KEY,
        "email" TEXT UNIQUE NOT NULL,
        "emailVerified" BOOLEAN DEFAULT FALSE,
        "name" TEXT,
        "image" TEXT,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    // Create sessions table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS "session" (
        "id" TEXT PRIMARY KEY,
        "userId" TEXT NOT NULL,
        "expiresAt" TIMESTAMP NOT NULL,
        "token" TEXT UNIQUE NOT NULL,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE
      );
    `);
    
    // Create accounts table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS "account" (
        "id" TEXT PRIMARY KEY,
        "userId" TEXT NOT NULL,
        "accountId" TEXT NOT NULL,
        "providerId" TEXT NOT NULL,
        "accessToken" TEXT,
        "refreshToken" TEXT,
        "idToken" TEXT,
        "accessTokenExpiresAt" TIMESTAMP,
        "refreshTokenExpiresAt" TIMESTAMP,
        "scope" TEXT,
        "password" TEXT,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE
      );
    `);
    
    // Create verification table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS "verification" (
        "id" TEXT PRIMARY KEY,
        "identifier" TEXT NOT NULL,
        "value" TEXT NOT NULL,
        "expiresAt" TIMESTAMP NOT NULL,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    console.log('Database tables created successfully!');
    
    // Insert demo users
    await pool.query(`
      INSERT INTO "user" (id, email, "emailVerified", name) 
      VALUES 
        ('1', '<EMAIL>', true, 'Demo User'),
        ('2', '<EMAIL>', true, 'Whiteboard User')
      ON CONFLICT (email) DO NOTHING;
    `);
    
    // Insert demo accounts with passwords
    await pool.query(`
      INSERT INTO "account" (id, "userId", "accountId", "providerId", password) 
      VALUES 
        ('1', '1', '<EMAIL>', 'credential', 'demo123'),
        ('2', '2', '<EMAIL>', 'credential', 'password')
      ON CONFLICT DO NOTHING;
    `);
    
    console.log('Demo users created successfully!');
    
  } catch (error) {
    console.error('Error setting up database:', error);
  } finally {
    await pool.end();
  }
}

setupDatabase();
