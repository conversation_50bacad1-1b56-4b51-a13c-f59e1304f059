#!/usr/bin/env node

/**
 * SEO Assets Generator
 * Generates placeholder SEO assets for the Anchorboard Application
 */

const fs = require('fs');
const path = require('path');

class SEOAssetsGenerator {
  constructor() {
    this.publicDir = path.join(process.cwd(), 'public');
    this.assetsToCreate = [
      {
        name: 'og-image.png',
        width: 1200,
        height: 630,
        description: 'Open Graph image for social media sharing'
      },
      {
        name: 'twitter-image.png',
        width: 1200,
        height: 630,
        description: 'Twitter Card image'
      },
      {
        name: 'screenshot.png',
        width: 1920,
        height: 1080,
        description: 'Application screenshot for structured data'
      },
      {
        name: 'logo.png',
        width: 512,
        height: 512,
        description: 'Company logo'
      },
      {
        name: 'favicon.ico',
        width: 32,
        height: 32,
        description: 'Favicon'
      },
      {
        name: 'apple-touch-icon.png',
        width: 180,
        height: 180,
        description: 'Apple touch icon'
      }
    ];
  }

  // Create SVG placeholder for images
  createSVGPlaceholder(width, height, text, filename) {
    const svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#grad)"/>
  <text x="50%" y="40%" dominant-baseline="middle" text-anchor="middle" 
        font-family="Arial, sans-serif" font-size="${Math.min(width, height) / 15}" 
        font-weight="bold" fill="white">
    Anchorboard App
  </text>
  <text x="50%" y="55%" dominant-baseline="middle" text-anchor="middle" 
        font-family="Arial, sans-serif" font-size="${Math.min(width, height) / 25}" 
        fill="rgba(255,255,255,0.9)">
    AI-Powered Collaboration
  </text>
  <text x="50%" y="70%" dominant-baseline="middle" text-anchor="middle" 
        font-family="Arial, sans-serif" font-size="${Math.min(width, height) / 35}" 
        fill="rgba(255,255,255,0.7)">
    ${text}
  </text>
</svg>`;

    const filepath = path.join(this.publicDir, filename);
    fs.writeFileSync(filepath, svg);
    console.log(`✅ Created ${filename} (${width}x${height})`);
  }

  // Create favicon ICO placeholder
  createFaviconPlaceholder() {
    // Create a simple SVG favicon
    const svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <rect width="32" height="32" fill="#3B82F6"/>
  <text x="16" y="20" dominant-baseline="middle" text-anchor="middle" 
        font-family="Arial, sans-serif" font-size="18" 
        font-weight="bold" fill="white">W</text>
</svg>`;

    const filepath = path.join(this.publicDir, 'favicon.svg');
    fs.writeFileSync(filepath, svg);
    console.log(`✅ Created favicon.svg (32x32)`);
  }

  // Generate all SEO assets
  generateAssets() {
    console.log('🎨 Generating SEO assets...\n');

    // Ensure public directory exists
    if (!fs.existsSync(this.publicDir)) {
      fs.mkdirSync(this.publicDir, { recursive: true });
    }

    // Generate image placeholders
    this.assetsToCreate.forEach(asset => {
      if (asset.name === 'favicon.ico') {
        this.createFaviconPlaceholder();
      } else {
        this.createSVGPlaceholder(
          asset.width, 
          asset.height, 
          asset.description,
          asset.name.replace('.png', '.svg')
        );
      }
    });

    // Create manifest.json for PWA
    this.createManifest();

    // Create additional favicon sizes
    this.createAdditionalFavicons();

    console.log('\n🎉 All SEO assets generated successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Replace SVG placeholders with actual PNG images');
    console.log('2. Create proper favicon.ico file');
    console.log('3. Add favicon links to layout.tsx');
    console.log('4. Test images in social media validators');
  }

  // Create web app manifest
  createManifest() {
    const manifest = {
      name: "Anchorboard App - AI-Powered Collaboration",
      short_name: "Anchorboard App",
      description: "AI-powered collaborative whiteboard with countdown timers, goal tracking, and Shape Up hill charts",
      start_url: "/",
      display: "standalone",
      background_color: "#ffffff",
      theme_color: "#3B82F6",
      icons: [
        {
          src: "/android-chrome-192x192.png",
          sizes: "192x192",
          type: "image/png"
        },
        {
          src: "/android-chrome-512x512.png",
          sizes: "512x512",
          type: "image/png"
        }
      ]
    };

    const filepath = path.join(this.publicDir, 'manifest.json');
    fs.writeFileSync(filepath, JSON.stringify(manifest, null, 2));
    console.log('✅ Created manifest.json');
  }

  // Create additional favicon sizes
  createAdditionalFavicons() {
    const sizes = [
      { name: 'android-chrome-192x192.svg', size: 192 },
      { name: 'android-chrome-512x512.svg', size: 512 },
      { name: 'apple-touch-icon.svg', size: 180 },
      { name: 'favicon-16x16.svg', size: 16 },
      { name: 'favicon-32x32.svg', size: 32 }
    ];

    sizes.forEach(({ name, size }) => {
      const svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#grad)" rx="${size * 0.1}"/>
  <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" 
        font-family="Arial, sans-serif" font-size="${size * 0.6}" 
        font-weight="bold" fill="white">W</text>
</svg>`;

      const filepath = path.join(this.publicDir, name);
      fs.writeFileSync(filepath, svg);
      console.log(`✅ Created ${name} (${size}x${size})`);
    });
  }
}

// Run the generator
if (require.main === module) {
  const generator = new SEOAssetsGenerator();
  generator.generateAssets();
}

module.exports = SEOAssetsGenerator;
