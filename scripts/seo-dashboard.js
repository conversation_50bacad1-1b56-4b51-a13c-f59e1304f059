#!/usr/bin/env node

/**
 * SEO Dashboard Script
 * Comprehensive SEO health dashboard
 */

const SEOChecker = require('./seo-check.js');
const PerformanceAuditor = require('./performance-audit.js');
const fs = require('fs');
const path = require('path');

class SEODashboard {
  constructor() {
    this.results = {
      seo: null,
      performance: null,
      timestamp: new Date().toISOString(),
      recommendations: []
    };
  }

  // Run all SEO checks
  async runAllChecks() {
    console.log('🚀 Running comprehensive SEO dashboard...\n');
    
    // Run SEO checks
    console.log('1️⃣ Running SEO configuration checks...');
    const seoChecker = new SEOChecker();
    this.results.seo = await seoChecker.runAllChecks();
    
    console.log('\n2️⃣ Running performance audit...');
    const perfAuditor = new PerformanceAuditor();
    this.results.performance = await perfAuditor.runAudit();
    
    // Generate recommendations
    this.generateRecommendations();
    
    // Generate dashboard
    this.generateDashboard();
    
    // Save results
    this.saveResults();
  }

  // Generate actionable recommendations
  generateRecommendations() {
    console.log('\n3️⃣ Generating recommendations...');
    
    // SEO recommendations
    if (this.results.seo.score < 100) {
      if (this.results.seo.errors > 0) {
        this.results.recommendations.push({
          priority: 'HIGH',
          category: 'SEO',
          title: 'Fix SEO Errors',
          description: `${this.results.seo.errors} SEO errors need immediate attention`,
          action: 'Run npm run seo:check for details'
        });
      }
      
      if (this.results.seo.warnings > 0) {
        this.results.recommendations.push({
          priority: 'MEDIUM',
          category: 'SEO',
          title: 'Address SEO Warnings',
          description: `${this.results.seo.warnings} SEO warnings should be resolved`,
          action: 'Review and implement missing SEO features'
        });
      }
    }

    // Performance recommendations
    if (this.results.performance.score < 90) {
      this.results.recommendations.push({
        priority: 'HIGH',
        category: 'Performance',
        title: 'Optimize Performance',
        description: `Performance score is ${this.results.performance.score}%, should be 90%+`,
        action: 'Run npm run perf:audit for specific recommendations'
      });
    }

    // General recommendations
    this.results.recommendations.push({
      priority: 'LOW',
      category: 'Monitoring',
      title: 'Set up Regular Monitoring',
      description: 'Implement automated SEO monitoring for production',
      action: 'Schedule npm run seo:monitor to run daily'
    });

    this.results.recommendations.push({
      priority: 'MEDIUM',
      category: 'Content',
      title: 'Content Optimization',
      description: 'Regularly update and optimize content for target keywords',
      action: 'Review and update page content monthly'
    });
  }

  // Generate comprehensive dashboard
  generateDashboard() {
    console.log('\n📊 SEO Health Dashboard');
    console.log('='.repeat(60));
    console.log(`Generated: ${this.results.timestamp}`);
    console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log('');

    // Overall health score
    const overallScore = Math.round((this.results.seo.score + this.results.performance.score) / 2);
    console.log(`🎯 Overall SEO Health: ${overallScore}%`);
    
    if (overallScore >= 90) {
      console.log('🎉 Excellent! Your SEO is in great shape');
    } else if (overallScore >= 75) {
      console.log('👍 Good SEO health, some improvements possible');
    } else if (overallScore >= 60) {
      console.log('⚠️ SEO needs attention');
    } else {
      console.log('🚨 SEO requires immediate action');
    }
    console.log('');

    // Detailed scores
    console.log('📈 Detailed Scores:');
    console.log(`  SEO Configuration: ${this.results.seo.score}%`);
    console.log(`  Performance: ${this.results.performance.score}%`);
    console.log('');

    // Quick stats
    console.log('📊 Quick Stats:');
    console.log(`  ✅ SEO Checks Passed: ${this.results.seo.passed}`);
    console.log(`  ⚠️ SEO Warnings: ${this.results.seo.warnings}`);
    console.log(`  ❌ SEO Errors: ${this.results.seo.errors}`);
    console.log(`  ⚡ Performance Optimizations: ${this.results.performance.passed}`);
    console.log(`  💡 Performance Recommendations: ${this.results.performance.recommendations}`);
    console.log('');

    // Priority recommendations
    const highPriority = this.results.recommendations.filter(r => r.priority === 'HIGH');
    const mediumPriority = this.results.recommendations.filter(r => r.priority === 'MEDIUM');
    const lowPriority = this.results.recommendations.filter(r => r.priority === 'LOW');

    if (highPriority.length > 0) {
      console.log('🚨 High Priority Actions:');
      highPriority.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec.title}`);
        console.log(`     ${rec.description}`);
        console.log(`     Action: ${rec.action}`);
        console.log('');
      });
    }

    if (mediumPriority.length > 0) {
      console.log('⚠️ Medium Priority Actions:');
      mediumPriority.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec.title}`);
        console.log(`     ${rec.description}`);
        console.log(`     Action: ${rec.action}`);
        console.log('');
      });
    }

    if (lowPriority.length > 0) {
      console.log('💡 Low Priority Actions:');
      lowPriority.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec.title}`);
        console.log(`     ${rec.description}`);
        console.log(`     Action: ${rec.action}`);
        console.log('');
      });
    }

    // Next steps
    console.log('🎯 Next Steps:');
    console.log('1. Address high priority issues first');
    console.log('2. Run individual audits for detailed information:');
    console.log('   - npm run seo:check (SEO configuration)');
    console.log('   - npm run perf:audit (Performance)');
    console.log('   - npm run seo:monitor (Live monitoring)');
    console.log('3. Set up automated monitoring in production');
    console.log('4. Review and update content regularly');
    console.log('5. Monitor Core Web Vitals in Google Search Console');
    console.log('');

    // SEO checklist
    console.log('✅ SEO Checklist for Production:');
    console.log('□ Google Search Console setup');
    console.log('□ Google Analytics 4 configured');
    console.log('□ Bing Webmaster Tools setup');
    console.log('□ Social media meta tags tested');
    console.log('□ Core Web Vitals monitoring');
    console.log('□ Regular content updates scheduled');
    console.log('□ Competitor analysis planned');
    console.log('□ Local SEO optimized (if applicable)');
    console.log('');
  }

  // Save results to file
  saveResults() {
    const resultsDir = path.join(process.cwd(), 'seo-reports');
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }

    const filename = `seo-dashboard-${new Date().toISOString().split('T')[0]}.json`;
    const filepath = path.join(resultsDir, filename);
    
    fs.writeFileSync(filepath, JSON.stringify(this.results, null, 2));
    console.log(`📄 Results saved to: ${filepath}`);
    
    // Also save a latest.json for easy access
    const latestPath = path.join(resultsDir, 'latest.json');
    fs.writeFileSync(latestPath, JSON.stringify(this.results, null, 2));
    console.log(`📄 Latest results: ${latestPath}`);
  }

  // Generate HTML report
  generateHTMLReport() {
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Dashboard Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .score { font-size: 2em; font-weight: bold; }
        .high { color: #dc3545; }
        .medium { color: #ffc107; }
        .low { color: #28a745; }
        .card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>SEO Dashboard Report</h1>
    <p>Generated: ${this.results.timestamp}</p>
    
    <div class="card">
        <h2>Overall Health Score</h2>
        <div class="score">${Math.round((this.results.seo.score + this.results.performance.score) / 2)}%</div>
    </div>
    
    <div class="card">
        <h2>Detailed Scores</h2>
        <p>SEO Configuration: ${this.results.seo.score}%</p>
        <p>Performance: ${this.results.performance.score}%</p>
    </div>
    
    <div class="card">
        <h2>Recommendations</h2>
        ${this.results.recommendations.map(rec => `
            <div class="${rec.priority.toLowerCase()}">
                <h3>${rec.title}</h3>
                <p>${rec.description}</p>
                <p><strong>Action:</strong> ${rec.action}</p>
            </div>
        `).join('')}
    </div>
</body>
</html>`;

    const htmlPath = path.join(process.cwd(), 'seo-reports', 'dashboard.html');
    fs.writeFileSync(htmlPath, html);
    console.log(`📄 HTML report: ${htmlPath}`);
  }
}

// Run dashboard
if (require.main === module) {
  const dashboard = new SEODashboard();
  dashboard.runAllChecks().then(() => {
    console.log('✅ SEO Dashboard completed successfully!');
  }).catch(error => {
    console.error('❌ Dashboard failed:', error);
    process.exit(1);
  });
}

module.exports = SEODashboard;
