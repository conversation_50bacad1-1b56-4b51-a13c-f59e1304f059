#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to test public whiteboard access functionality
 * This script helps verify that the public access fix is working correctly
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testPublicAccess() {
  console.log('🔍 Testing Public Whiteboard Access...\n');

  try {
    // Find a public whiteboard
    const publicWhiteboard = await prisma.whiteboard.findFirst({
      where: {
        isPublic: true,
      },
      select: {
        id: true,
        title: true,
        isPublic: true,
        requiresAuth: true,
        allowAnonymousView: true,
        publicAccessLevel: true,
        userId: true,
      },
    });

    if (!publicWhiteboard) {
      console.log('❌ No public whiteboards found. Create one first by:');
      console.log('   1. Login to the app');
      console.log('   2. Create a whiteboard');
      console.log('   3. Toggle it to public using the visibility toggle');
      console.log('   4. Run this script again');
      return;
    }

    console.log('✅ Found public whiteboard:');
    console.log(`   ID: ${publicWhiteboard.id}`);
    console.log(`   Title: ${publicWhiteboard.title}`);
    console.log(`   URL: http://localhost:3000/whiteboard/${publicWhiteboard.id}`);
    console.log();

    // Check if the access control flags are set correctly
    console.log('🔐 Access Control Settings:');
    console.log(`   isPublic: ${publicWhiteboard.isPublic}`);
    console.log(`   requiresAuth: ${publicWhiteboard.requiresAuth}`);
    console.log(`   allowAnonymousView: ${publicWhiteboard.allowAnonymousView}`);
    console.log(`   publicAccessLevel: ${publicWhiteboard.publicAccessLevel}`);
    console.log();

    // Verify the settings are correct for public access
    const isConfiguredCorrectly = 
      publicWhiteboard.isPublic === true &&
      publicWhiteboard.requiresAuth === false &&
      publicWhiteboard.allowAnonymousView === true &&
      publicWhiteboard.publicAccessLevel === 'view';

    if (isConfiguredCorrectly) {
      console.log('✅ Public access is configured correctly!');
      console.log('🌐 Anonymous users should be able to view this whiteboard.');
      console.log();
      console.log('📝 To test:');
      console.log('   1. Open an incognito/private browser window');
      console.log(`   2. Visit: http://localhost:3000/whiteboard/${publicWhiteboard.id}`);
      console.log('   3. Verify you can see the whiteboard without logging in');
      console.log('   4. Verify the UI shows "View Only" mode');
    } else {
      console.log('❌ Public access is NOT configured correctly!');
      console.log('🔧 Expected settings for anonymous access:');
      console.log('   isPublic: true');
      console.log('   requiresAuth: false');
      console.log('   allowAnonymousView: true');
      console.log('   publicAccessLevel: "view"');
      console.log();
      console.log('💡 Try toggling the whiteboard visibility to fix the settings.');
    }

  } catch (error) {
    console.error('❌ Error testing public access:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testPublicAccess();