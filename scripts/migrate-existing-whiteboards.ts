#!/usr/bin/env tsx

/**
 * Migration script to update existing whiteboards with new RBAC fields
 * This script should be run after the database migration to ensure
 * existing whiteboards have proper default values for the new fields.
 */

import { PrismaClient, WhiteboardRole, WhiteboardPermission } from '../src/generated/prisma';

const prisma = new PrismaClient();

async function migrateExistingWhiteboards() {
  console.log('🚀 Starting migration of existing whiteboards...');

  try {
    // Get all existing whiteboards
    const whiteboards = await prisma.whiteboard.findMany({
      select: {
        id: true,
        userId: true,
        isPublic: true,
        title: true,
      },
    });

    console.log(`📊 Found ${whiteboards.length} whiteboards to migrate`);

    let migratedCount = 0;
    let errorCount = 0;

    for (const whiteboard of whiteboards) {
      try {
        console.log(`📝 Migrating whiteboard: ${whiteboard.title} (${whiteboard.id})`);

        // Update whiteboard with new RBAC fields
        await prisma.whiteboard.update({
          where: { id: whiteboard.id },
          data: {
            // Set default values for new RBAC fields
            publicAccessLevel: whiteboard.isPublic ? 'view' : 'none',
            requiresAuth: true,
            allowAnonymousView: whiteboard.isPublic,
          },
        });

        // Create owner access record for the whiteboard owner
        // This ensures the owner has explicit access in the new RBAC system
        await prisma.whiteboardUserAccess.upsert({
          where: {
            whiteboardId_userId: {
              whiteboardId: whiteboard.id,
              userId: whiteboard.userId,
            },
          },
          update: {
            role: WhiteboardRole.OWNER,
            permissions: [
              WhiteboardPermission.VIEW,
              WhiteboardPermission.EDIT,
              WhiteboardPermission.COMMENT,
              WhiteboardPermission.MANAGE_SETTINGS,
              WhiteboardPermission.MANAGE_ACCESS,
              WhiteboardPermission.DELETE,
              WhiteboardPermission.SHARE,
            ],
          },
          create: {
            whiteboardId: whiteboard.id,
            userId: whiteboard.userId,
            role: WhiteboardRole.OWNER,
            permissions: [
              WhiteboardPermission.VIEW,
              WhiteboardPermission.EDIT,
              WhiteboardPermission.COMMENT,
              WhiteboardPermission.MANAGE_SETTINGS,
              WhiteboardPermission.MANAGE_ACCESS,
              WhiteboardPermission.DELETE,
              WhiteboardPermission.SHARE,
            ],
          },
        });

        migratedCount++;
        console.log(`✅ Successfully migrated whiteboard: ${whiteboard.id}`);
      } catch (error) {
        errorCount++;
        console.error(`❌ Error migrating whiteboard ${whiteboard.id}:`, error);
      }
    }

    console.log('\n📈 Migration Summary:');
    console.log(`✅ Successfully migrated: ${migratedCount} whiteboards`);
    console.log(`❌ Errors: ${errorCount} whiteboards`);
    
    if (errorCount === 0) {
      console.log('🎉 All whiteboards migrated successfully!');
    } else {
      console.log('⚠️  Some whiteboards failed to migrate. Please check the errors above.');
    }

  } catch (error) {
    console.error('💥 Fatal error during migration:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Verification function to check migration results
async function verifyMigration() {
  console.log('\n🔍 Verifying migration results...');

  try {
    // Check that all whiteboards have the new RBAC fields
    // Since these fields have default values, they can't be null
    // We'll just verify the fields exist by checking a sample
    const sampleWhiteboard = await prisma.whiteboard.findFirst({
      select: {
        id: true,
        title: true,
        publicAccessLevel: true,
        requiresAuth: true,
        allowAnonymousView: true,
      },
    });

    if (sampleWhiteboard) {
      console.log('✅ RBAC fields are properly configured');
      console.log(`Sample whiteboard "${sampleWhiteboard.title}" has:`);
      console.log(`  - publicAccessLevel: ${sampleWhiteboard.publicAccessLevel}`);
      console.log(`  - requiresAuth: ${sampleWhiteboard.requiresAuth}`);
      console.log(`  - allowAnonymousView: ${sampleWhiteboard.allowAnonymousView}`);
    } else {
      console.log('⚠️  No whiteboards found to verify RBAC fields');
    }

    // Check that all whiteboards have owner access records
    const whiteboardsWithoutOwnerAccess = await prisma.whiteboard.findMany({
      where: {
        userAccess: {
          none: {
            role: WhiteboardRole.OWNER,
          },
        },
      },
      select: {
        id: true,
        title: true,
        userId: true,
      },
    });

    if (whiteboardsWithoutOwnerAccess.length > 0) {
      console.log(`⚠️  Found ${whiteboardsWithoutOwnerAccess.length} whiteboards without owner access records:`);
      whiteboardsWithoutOwnerAccess.forEach(wb => {
        console.log(`  - ${wb.title} (${wb.id}) - Owner: ${wb.userId}`);
      });
    } else {
      console.log('✅ All whiteboards have owner access records');
    }

    // Summary statistics
    const totalWhiteboards = await prisma.whiteboard.count();
    const totalUserAccess = await prisma.whiteboardUserAccess.count();
    const totalShareLinks = await prisma.whiteboardShareLink.count();
    const totalInvitations = await prisma.whiteboardInvitation.count();

    console.log('\n📊 RBAC System Statistics:');
    console.log(`📋 Total whiteboards: ${totalWhiteboards}`);
    console.log(`👥 Total user access records: ${totalUserAccess}`);
    console.log(`🔗 Total share links: ${totalShareLinks}`);
    console.log(`📧 Total invitations: ${totalInvitations}`);

  } catch (error) {
    console.error('💥 Error during verification:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  switch (command) {
    case 'migrate':
      await migrateExistingWhiteboards();
      break;
    case 'verify':
      await verifyMigration();
      break;
    case 'all':
    default:
      await migrateExistingWhiteboards();
      await verifyMigration();
      break;
  }
}

// Handle script execution
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Script execution failed:', error);
    process.exit(1);
  });
}

export { migrateExistingWhiteboards, verifyMigration };
