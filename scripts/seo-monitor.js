#!/usr/bin/env node

/**
 * SEO Monitoring Script
 * Comprehensive monitoring for SEO health and performance
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

class SEOMonitor {
  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://anchorboard.xyz';
    this.results = {
      accessibility: [],
      performance: [],
      seo: [],
      errors: []
    };
  }

  // Check sitemap accessibility
  async checkSitemap() {
    console.log('🗺️ Checking sitemap...');
    
    try {
      const sitemapUrl = `${this.baseUrl}/sitemap.xml`;
      const response = await this.fetchUrl(sitemapUrl);
      
      if (response.statusCode === 200) {
        this.results.seo.push('✅ Sitemap is accessible');
        
        // Parse sitemap content
        if (response.data.includes('<urlset')) {
          this.results.seo.push('✅ Sitemap has valid XML structure');
          
          // Count URLs
          const urlCount = (response.data.match(/<url>/g) || []).length;
          this.results.seo.push(`✅ Sitemap contains ${urlCount} URLs`);
        } else {
          this.results.errors.push('❌ Sitemap XML structure is invalid');
        }
      } else {
        this.results.errors.push(`❌ Sitemap not accessible (${response.statusCode})`);
      }
    } catch (error) {
      this.results.errors.push(`❌ Sitemap check failed: ${error.message}`);
    }
  }

  // Check robots.txt
  async checkRobots() {
    console.log('🤖 Checking robots.txt...');
    
    try {
      const robotsUrl = `${this.baseUrl}/robots.txt`;
      const response = await this.fetchUrl(robotsUrl);
      
      if (response.statusCode === 200) {
        this.results.seo.push('✅ Robots.txt is accessible');
        
        if (response.data.includes('User-agent:')) {
          this.results.seo.push('✅ Robots.txt has valid directives');
        }
        
        if (response.data.includes('Sitemap:')) {
          this.results.seo.push('✅ Robots.txt references sitemap');
        } else {
          this.results.errors.push('❌ Robots.txt missing sitemap reference');
        }
      } else {
        this.results.errors.push(`❌ Robots.txt not accessible (${response.statusCode})`);
      }
    } catch (error) {
      this.results.errors.push(`❌ Robots.txt check failed: ${error.message}`);
    }
  }

  // Check page response times
  async checkPagePerformance() {
    console.log('⚡ Checking page performance...');
    
    const pages = ['/', '/features', '/pricing', '/about', '/contact'];
    
    for (const page of pages) {
      try {
        const startTime = Date.now();
        const response = await this.fetchUrl(`${this.baseUrl}${page}`);
        const responseTime = Date.now() - startTime;
        
        if (response.statusCode === 200) {
          if (responseTime < 1000) {
            this.results.performance.push(`✅ ${page} loads in ${responseTime}ms`);
          } else if (responseTime < 3000) {
            this.results.performance.push(`⚠️ ${page} loads in ${responseTime}ms (could be faster)`);
          } else {
            this.results.errors.push(`❌ ${page} loads slowly (${responseTime}ms)`);
          }
        } else {
          this.results.errors.push(`❌ ${page} returns ${response.statusCode}`);
        }
      } catch (error) {
        this.results.errors.push(`❌ ${page} check failed: ${error.message}`);
      }
    }
  }

  // Check meta tags
  async checkMetaTags() {
    console.log('🏷️ Checking meta tags...');
    
    const pages = [
      { path: '/', name: 'Home' },
      { path: '/features', name: 'Features' },
      { path: '/pricing', name: 'Pricing' },
      { path: '/about', name: 'About' },
      { path: '/contact', name: 'Contact' }
    ];
    
    for (const page of pages) {
      try {
        const response = await this.fetchUrl(`${this.baseUrl}${page.path}`);
        
        if (response.statusCode === 200) {
          const html = response.data;
          
          // Check title
          const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
          if (titleMatch && titleMatch[1].length > 0) {
            this.results.seo.push(`✅ ${page.name} has title tag`);
            
            if (titleMatch[1].length <= 60) {
              this.results.seo.push(`✅ ${page.name} title length is optimal`);
            } else {
              this.results.errors.push(`❌ ${page.name} title too long (${titleMatch[1].length} chars)`);
            }
          } else {
            this.results.errors.push(`❌ ${page.name} missing title tag`);
          }
          
          // Check meta description
          const descMatch = html.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i);
          if (descMatch && descMatch[1].length > 0) {
            this.results.seo.push(`✅ ${page.name} has meta description`);
            
            if (descMatch[1].length <= 160) {
              this.results.seo.push(`✅ ${page.name} description length is optimal`);
            } else {
              this.results.errors.push(`❌ ${page.name} description too long (${descMatch[1].length} chars)`);
            }
          } else {
            this.results.errors.push(`❌ ${page.name} missing meta description`);
          }
          
          // Check Open Graph
          if (html.includes('property="og:title"')) {
            this.results.seo.push(`✅ ${page.name} has Open Graph tags`);
          } else {
            this.results.errors.push(`❌ ${page.name} missing Open Graph tags`);
          }
          
          // Check structured data
          if (html.includes('application/ld+json')) {
            this.results.seo.push(`✅ ${page.name} has structured data`);
          } else {
            this.results.errors.push(`❌ ${page.name} missing structured data`);
          }
        }
      } catch (error) {
        this.results.errors.push(`❌ ${page.name} meta check failed: ${error.message}`);
      }
    }
  }

  // Check SSL certificate
  async checkSSL() {
    console.log('🔒 Checking SSL certificate...');
    
    if (this.baseUrl.startsWith('https://')) {
      this.results.seo.push('✅ Site uses HTTPS');
      
      try {
        const response = await this.fetchUrl(this.baseUrl);
        if (response.statusCode === 200) {
          this.results.seo.push('✅ SSL certificate is valid');
        }
      } catch (error) {
        if (error.message.includes('certificate')) {
          this.results.errors.push('❌ SSL certificate issue detected');
        }
      }
    } else {
      this.results.errors.push('❌ Site not using HTTPS');
    }
  }

  // Utility function to fetch URLs
  fetchUrl(url) {
    return new Promise((resolve, reject) => {
      const request = https.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'SEO-Monitor/1.0'
        }
      }, (response) => {
        let data = '';
        
        response.on('data', (chunk) => {
          data += chunk;
        });
        
        response.on('end', () => {
          resolve({
            statusCode: response.statusCode,
            headers: response.headers,
            data: data
          });
        });
      });
      
      request.on('error', reject);
      request.on('timeout', () => {
        request.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  // Generate monitoring report
  generateReport() {
    console.log('\n📊 SEO Monitoring Report');
    console.log('='.repeat(50));
    console.log(`Report generated: ${new Date().toISOString()}`);
    console.log(`Base URL: ${this.baseUrl}\n`);
    
    const categories = [
      { name: 'SEO', items: this.results.seo, icon: '🔍' },
      { name: 'Performance', items: this.results.performance, icon: '⚡' },
      { name: 'Accessibility', items: this.results.accessibility, icon: '♿' }
    ];
    
    categories.forEach(category => {
      if (category.items.length > 0) {
        console.log(`${category.icon} ${category.name} (${category.items.length})`);
        category.items.forEach(item => console.log(`  ${item}`));
        console.log('');
      }
    });
    
    if (this.results.errors.length > 0) {
      console.log(`❌ Issues Found (${this.results.errors.length})`);
      this.results.errors.forEach(error => console.log(`  ${error}`));
      console.log('');
    }
    
    const totalChecks = Object.values(this.results).flat().length;
    const successfulChecks = this.results.seo.length + this.results.performance.length + this.results.accessibility.length;
    const healthScore = totalChecks > 0 ? Math.round((successfulChecks / totalChecks) * 100) : 0;
    
    console.log(`📈 SEO Health Score: ${healthScore}%`);
    
    if (healthScore >= 90) {
      console.log('🎉 Excellent SEO health!');
    } else if (healthScore >= 70) {
      console.log('👍 Good SEO health, minor issues to address');
    } else {
      console.log('⚠️ SEO health needs attention');
    }
    
    return {
      score: healthScore,
      errors: this.results.errors.length,
      total: totalChecks
    };
  }

  // Run all monitoring checks
  async runMonitoring() {
    console.log('🚀 Starting SEO monitoring...\n');
    
    await this.checkSitemap();
    await this.checkRobots();
    await this.checkSSL();
    await this.checkPagePerformance();
    await this.checkMetaTags();
    
    return this.generateReport();
  }
}

// Run monitoring
if (require.main === module) {
  const monitor = new SEOMonitor();
  monitor.runMonitoring().then(report => {
    process.exit(report.errors > 0 ? 1 : 0);
  }).catch(error => {
    console.error('Monitoring failed:', error);
    process.exit(1);
  });
}

module.exports = SEOMonitor;
