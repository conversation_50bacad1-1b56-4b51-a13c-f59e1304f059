#!/usr/bin/env node

/**
 * Sitemap Validation Script
 * Validates the sitemap.xml structure and content
 */

const https = require('https');
const http = require('http');
const { DOMParser } = require('xmldom');

class SitemapValidator {
  constructor(baseUrl = 'http://localhost:3000') {
    this.baseUrl = baseUrl;
    this.results = {
      valid: true,
      errors: [],
      warnings: [],
      info: []
    };
  }

  async validateSitemap() {
    console.log('🗺️ Validating sitemap...\n');
    
    try {
      const sitemapUrl = `${this.baseUrl}/sitemap.xml`;
      const response = await this.fetchUrl(sitemapUrl);
      
      if (response.statusCode !== 200) {
        this.addError(`Sitemap not accessible (${response.statusCode})`);
        return this.generateReport();
      }

      this.addInfo('✅ Sitemap is accessible');
      
      // Validate XML structure
      this.validateXMLStructure(response.data);
      
      // Validate sitemap content
      this.validateSitemapContent(response.data);
      
    } catch (error) {
      this.addError(`Sitemap validation failed: ${error.message}`);
    }
    
    return this.generateReport();
  }

  validateXMLStructure(xmlData) {
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(xmlData, 'text/xml');
      
      // Check for parsing errors
      const parserErrors = doc.getElementsByTagName('parsererror');
      if (parserErrors.length > 0) {
        this.addError('Invalid XML structure');
        return;
      }
      
      this.addInfo('✅ Valid XML structure');
      
      // Check root element
      const urlset = doc.getElementsByTagName('urlset')[0];
      if (!urlset) {
        this.addError('Missing <urlset> root element');
        return;
      }
      
      // Check namespace
      const xmlns = urlset.getAttribute('xmlns');
      if (xmlns !== 'http://www.sitemaps.org/schemas/sitemap/0.9') {
        this.addWarning('Incorrect or missing xmlns namespace');
      } else {
        this.addInfo('✅ Correct sitemap namespace');
      }
      
    } catch (error) {
      this.addError(`XML parsing failed: ${error.message}`);
    }
  }

  validateSitemapContent(xmlData) {
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(xmlData, 'text/xml');
      const urls = doc.getElementsByTagName('url');
      
      if (urls.length === 0) {
        this.addError('No URLs found in sitemap');
        return;
      }
      
      this.addInfo(`✅ Found ${urls.length} URLs in sitemap`);
      
      // Validate each URL
      for (let i = 0; i < urls.length; i++) {
        const url = urls[i];
        this.validateUrlEntry(url, i + 1);
      }
      
    } catch (error) {
      this.addError(`Content validation failed: ${error.message}`);
    }
  }

  validateUrlEntry(urlElement, index) {
    const loc = urlElement.getElementsByTagName('loc')[0];
    const lastmod = urlElement.getElementsByTagName('lastmod')[0];
    const changefreq = urlElement.getElementsByTagName('changefreq')[0];
    const priority = urlElement.getElementsByTagName('priority')[0];
    
    // Validate required <loc> element
    if (!loc || !loc.textContent) {
      this.addError(`URL ${index}: Missing or empty <loc> element`);
      return;
    }
    
    const urlValue = loc.textContent.trim();
    
    // Validate URL format
    try {
      new URL(urlValue);
      this.addInfo(`✅ URL ${index}: Valid URL format (${urlValue})`);
    } catch (error) {
      this.addError(`URL ${index}: Invalid URL format (${urlValue})`);
    }
    
    // Validate lastmod if present
    if (lastmod && lastmod.textContent) {
      const lastmodValue = lastmod.textContent.trim();
      if (isNaN(Date.parse(lastmodValue))) {
        this.addWarning(`URL ${index}: Invalid lastmod date format (${lastmodValue})`);
      }
    }
    
    // Validate changefreq if present
    if (changefreq && changefreq.textContent) {
      const validFreqs = ['always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never'];
      const freqValue = changefreq.textContent.trim();
      if (!validFreqs.includes(freqValue)) {
        this.addWarning(`URL ${index}: Invalid changefreq value (${freqValue})`);
      }
    }
    
    // Validate priority if present
    if (priority && priority.textContent) {
      const priorityValue = parseFloat(priority.textContent.trim());
      if (isNaN(priorityValue) || priorityValue < 0 || priorityValue > 1) {
        this.addWarning(`URL ${index}: Invalid priority value (${priority.textContent})`);
      }
    }
  }

  fetchUrl(url) {
    return new Promise((resolve, reject) => {
      const client = url.startsWith('https:') ? https : http;
      
      client.get(url, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data
          });
        });
      }).on('error', reject);
    });
  }

  addError(message) {
    this.results.errors.push(message);
    this.results.valid = false;
  }

  addWarning(message) {
    this.results.warnings.push(message);
  }

  addInfo(message) {
    this.results.info.push(message);
  }

  generateReport() {
    console.log('\n📊 Sitemap Validation Report');
    console.log('=' .repeat(50));
    
    if (this.results.info.length > 0) {
      console.log('\n✅ Success:');
      this.results.info.forEach(info => console.log(`  ${info}`));
    }
    
    if (this.results.warnings.length > 0) {
      console.log('\n⚠️ Warnings:');
      this.results.warnings.forEach(warning => console.log(`  ${warning}`));
    }
    
    if (this.results.errors.length > 0) {
      console.log('\n❌ Errors:');
      this.results.errors.forEach(error => console.log(`  ${error}`));
    }
    
    console.log('\n' + '=' .repeat(50));
    console.log(`Overall Status: ${this.results.valid ? '✅ VALID' : '❌ INVALID'}`);
    console.log(`Errors: ${this.results.errors.length}`);
    console.log(`Warnings: ${this.results.warnings.length}`);
    
    return this.results;
  }
}

// Run validation if script is executed directly
if (require.main === module) {
  const baseUrl = process.argv[2] || 'http://localhost:3000';
  const validator = new SitemapValidator(baseUrl);
  
  validator.validateSitemap().then(results => {
    process.exit(results.valid ? 0 : 1);
  }).catch(error => {
    console.error('Validation failed:', error);
    process.exit(1);
  });
}

module.exports = SitemapValidator;
