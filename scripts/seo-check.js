#!/usr/bin/env node

/**
 * SEO自动检查脚本
 * 用于验证网站的基本SEO配置
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

class SEOChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.passed = [];
  }

  // 检查必需文件
  checkRequiredFiles() {
    console.log('🔍 检查必需文件...');
    
    const requiredFiles = [
      'public/robots.txt',
      'src/app/sitemap.ts',
      'next.config.js'
    ];

    requiredFiles.forEach(file => {
      if (fs.existsSync(file)) {
        this.passed.push(`✅ ${file} 存在`);
      } else {
        this.errors.push(`❌ ${file} 缺失`);
      }
    });
  }

  // 检查robots.txt内容
  checkRobotsTxt() {
    console.log('🤖 检查robots.txt...');
    
    const robotsPath = 'public/robots.txt';
    if (fs.existsSync(robotsPath)) {
      const content = fs.readFileSync(robotsPath, 'utf8');
      
      if (content.includes('User-agent: *')) {
        this.passed.push('✅ robots.txt 包含User-agent');
      } else {
        this.warnings.push('⚠️ robots.txt 缺少User-agent');
      }
      
      if (content.includes('Sitemap:')) {
        this.passed.push('✅ robots.txt 包含Sitemap');
      } else {
        this.warnings.push('⚠️ robots.txt 缺少Sitemap引用');
      }
    }
  }

  // 检查Next.js配置
  checkNextConfig() {
    console.log('⚙️ 检查Next.js配置...');
    
    const configPath = 'next.config.js';
    if (fs.existsSync(configPath)) {
      const content = fs.readFileSync(configPath, 'utf8');
      
      // 检查图片优化
      if (content.includes('images:') && !content.includes('unoptimized: true')) {
        this.passed.push('✅ 图片优化已启用');
      } else {
        this.warnings.push('⚠️ 图片优化未启用');
      }
      
      // 检查压缩
      if (content.includes('compress: true')) {
        this.passed.push('✅ 压缩已启用');
      } else {
        this.warnings.push('⚠️ 压缩未启用');
      }
      
      // 检查安全头部
      if (content.includes('headers()')) {
        this.passed.push('✅ 安全头部已配置');
      } else {
        this.warnings.push('⚠️ 安全头部未配置');
      }
    }
  }

  // 检查页面元数据
  checkPageMetadata() {
    console.log('📄 检查页面元数据...');
    
    const layoutPath = 'src/app/layout.tsx';
    if (fs.existsSync(layoutPath)) {
      const content = fs.readFileSync(layoutPath, 'utf8');
      
      if (content.includes('export const metadata')) {
        this.passed.push('✅ 根布局包含metadata');
      } else {
        this.errors.push('❌ 根布局缺少metadata');
      }
      
      if (content.includes('openGraph:')) {
        this.passed.push('✅ Open Graph配置存在');
      } else {
        this.warnings.push('⚠️ Open Graph配置缺失');
      }
      
      if (content.includes('twitter:')) {
        this.passed.push('✅ Twitter Cards配置存在');
      } else {
        this.warnings.push('⚠️ Twitter Cards配置缺失');
      }
    }
  }

  // 检查结构化数据
  checkStructuredData() {
    console.log('🏗️ 检查结构化数据...');
    
    const layoutPath = 'src/app/layout.tsx';
    if (fs.existsSync(layoutPath)) {
      const content = fs.readFileSync(layoutPath, 'utf8');
      
      if (content.includes('application/ld+json')) {
        this.passed.push('✅ 结构化数据已添加');
      } else {
        this.warnings.push('⚠️ 结构化数据缺失');
      }
    }
  }

  // 检查性能优化
  checkPerformanceOptimizations() {
    console.log('⚡ 检查性能优化...');
    
    // 检查动态导入
    const excalidrawPath = 'src/components/whiteboard/ExcalidrawWrapper.tsx';
    if (fs.existsSync(excalidrawPath)) {
      const content = fs.readFileSync(excalidrawPath, 'utf8');
      
      if (content.includes('dynamic(')) {
        this.passed.push('✅ Excalidraw使用动态导入');
      } else {
        this.warnings.push('⚠️ Excalidraw未使用动态导入');
      }
    }
    
    // 检查性能监控
    if (fs.existsSync('src/components/performance/PerformanceMonitor.tsx')) {
      this.passed.push('✅ 性能监控组件存在');
    } else {
      this.warnings.push('⚠️ 性能监控组件缺失');
    }
  }

  // 检查环境变量
  checkEnvironmentVariables() {
    console.log('🌍 检查环境变量...');
    
    const envFiles = ['.env.local', '.env.example'];
    let hasEnvFile = false;
    
    envFiles.forEach(file => {
      if (fs.existsSync(file)) {
        hasEnvFile = true;
        const content = fs.readFileSync(file, 'utf8');
        
        if (content.includes('NEXT_PUBLIC_BASE_URL')) {
          this.passed.push('✅ BASE_URL环境变量已配置');
        } else {
          this.warnings.push('⚠️ BASE_URL环境变量缺失');
        }
      }
    });
    
    if (!hasEnvFile) {
      this.warnings.push('⚠️ 环境变量文件缺失');
    }
  }

  // 生成报告
  generateReport() {
    console.log('\n📊 SEO检查报告');
    console.log('='.repeat(50));
    
    console.log(`\n✅ 通过检查: ${this.passed.length}`);
    this.passed.forEach(item => console.log(`  ${item}`));
    
    console.log(`\n⚠️ 警告: ${this.warnings.length}`);
    this.warnings.forEach(item => console.log(`  ${item}`));
    
    console.log(`\n❌ 错误: ${this.errors.length}`);
    this.errors.forEach(item => console.log(`  ${item}`));
    
    const total = this.passed.length + this.warnings.length + this.errors.length;
    const score = Math.round((this.passed.length / total) * 100);
    
    console.log(`\n📈 SEO得分: ${score}%`);
    
    if (score >= 90) {
      console.log('🎉 优秀！SEO配置很完善');
    } else if (score >= 70) {
      console.log('👍 良好，还有改进空间');
    } else {
      console.log('⚠️ 需要改进SEO配置');
    }
    
    return {
      score,
      passed: this.passed.length,
      warnings: this.warnings.length,
      errors: this.errors.length
    };
  }

  // 运行所有检查
  async runAllChecks() {
    console.log('🚀 开始SEO检查...\n');
    
    this.checkRequiredFiles();
    this.checkRobotsTxt();
    this.checkNextConfig();
    this.checkPageMetadata();
    this.checkStructuredData();
    this.checkPerformanceOptimizations();
    this.checkEnvironmentVariables();
    
    return this.generateReport();
  }
}

// 运行检查
if (require.main === module) {
  const checker = new SEOChecker();
  checker.runAllChecks().then(report => {
    process.exit(report.errors > 0 ? 1 : 0);
  });
}

module.exports = SEOChecker;
