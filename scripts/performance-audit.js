#!/usr/bin/env node

/**
 * Performance Audit Script
 * Checks Core Web Vitals and performance metrics
 */

const fs = require('fs');
const path = require('path');

class PerformanceAuditor {
  constructor() {
    this.recommendations = [];
    this.warnings = [];
    this.passed = [];
  }

  // Check bundle size
  checkBundleSize() {
    console.log('📦 Checking bundle size...');
    
    const nextDir = '.next';
    if (!fs.existsSync(nextDir)) {
      this.warnings.push('⚠️ Next.js build not found. Run "npm run build" first.');
      return;
    }

    // Check for large bundles
    const staticDir = path.join(nextDir, 'static');
    if (fs.existsSync(staticDir)) {
      this.passed.push('✅ Static assets directory exists');
      
      // Check for code splitting
      const chunksDir = path.join(staticDir, 'chunks');
      if (fs.existsSync(chunksDir)) {
        this.passed.push('✅ Code splitting is enabled');
      } else {
        this.warnings.push('⚠️ Code splitting may not be optimized');
      }
    }
  }

  // Check image optimization
  checkImageOptimization() {
    console.log('🖼️ Checking image optimization...');
    
    const nextConfig = 'next.config.js';
    if (fs.existsSync(nextConfig)) {
      const content = fs.readFileSync(nextConfig, 'utf8');
      
      if (content.includes('images:') && !content.includes('unoptimized: true')) {
        this.passed.push('✅ Image optimization is enabled');
      } else {
        this.recommendations.push('💡 Enable Next.js image optimization');
      }

      if (content.includes('formats:')) {
        this.passed.push('✅ Modern image formats configured');
      } else {
        this.recommendations.push('💡 Configure WebP/AVIF image formats');
      }
    }
  }

  // Check font optimization
  checkFontOptimization() {
    console.log('🔤 Checking font optimization...');
    
    const layoutPath = 'src/app/layout.tsx';
    if (fs.existsSync(layoutPath)) {
      const content = fs.readFileSync(layoutPath, 'utf8');
      
      if (content.includes('next/font')) {
        this.passed.push('✅ Next.js font optimization is used');
      } else {
        this.recommendations.push('💡 Use Next.js font optimization');
      }

      if (content.includes('preload')) {
        this.passed.push('✅ Font preloading is configured');
      } else {
        this.recommendations.push('💡 Add font preloading for critical fonts');
      }
    }
  }

  // Check lazy loading
  checkLazyLoading() {
    console.log('⚡ Checking lazy loading...');
    
    // Check for dynamic imports
    const srcDir = 'src';
    let hasDynamicImports = false;
    
    const checkDirectory = (dir) => {
      if (!fs.existsSync(dir)) return;
      
      const files = fs.readdirSync(dir);
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          checkDirectory(filePath);
        } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
          const content = fs.readFileSync(filePath, 'utf8');
          if (content.includes('dynamic(') || content.includes('lazy(')) {
            hasDynamicImports = true;
          }
        }
      });
    };

    checkDirectory(srcDir);
    
    if (hasDynamicImports) {
      this.passed.push('✅ Dynamic imports are used for lazy loading');
    } else {
      this.recommendations.push('💡 Implement lazy loading for heavy components');
    }
  }

  // Check Core Web Vitals setup
  checkWebVitals() {
    console.log('📊 Checking Web Vitals monitoring...');
    
    const performanceDir = 'src/components/performance';
    if (fs.existsSync(performanceDir)) {
      this.passed.push('✅ Performance monitoring components exist');
    } else {
      this.recommendations.push('💡 Add Web Vitals monitoring');
    }

    // Check for analytics
    const layoutPath = 'src/app/layout.tsx';
    if (fs.existsSync(layoutPath)) {
      const content = fs.readFileSync(layoutPath, 'utf8');
      
      if (content.includes('gtag') || content.includes('analytics')) {
        this.passed.push('✅ Analytics tracking is configured');
      } else {
        this.recommendations.push('💡 Add analytics for performance tracking');
      }
    }
  }

  // Check caching strategy
  checkCaching() {
    console.log('🗄️ Checking caching strategy...');
    
    const nextConfig = 'next.config.js';
    if (fs.existsSync(nextConfig)) {
      const content = fs.readFileSync(nextConfig, 'utf8');
      
      if (content.includes('headers()')) {
        this.passed.push('✅ Custom headers are configured');
        
        if (content.includes('Cache-Control')) {
          this.passed.push('✅ Cache-Control headers are set');
        } else {
          this.recommendations.push('💡 Add Cache-Control headers for static assets');
        }
      } else {
        this.recommendations.push('💡 Configure caching headers');
      }
    }
  }

  // Check compression
  checkCompression() {
    console.log('🗜️ Checking compression...');
    
    const nextConfig = 'next.config.js';
    if (fs.existsSync(nextConfig)) {
      const content = fs.readFileSync(nextConfig, 'utf8');
      
      if (content.includes('compress: true')) {
        this.passed.push('✅ Compression is enabled');
      } else {
        this.recommendations.push('💡 Enable compression in Next.js config');
      }
    }
  }

  // Generate performance recommendations
  generateReport() {
    console.log('\n📈 Performance Audit Report');
    console.log('='.repeat(50));
    
    console.log(`\n✅ Optimizations in place: ${this.passed.length}`);
    this.passed.forEach(item => console.log(`  ${item}`));
    
    console.log(`\n⚠️ Warnings: ${this.warnings.length}`);
    this.warnings.forEach(item => console.log(`  ${item}`));
    
    console.log(`\n💡 Recommendations: ${this.recommendations.length}`);
    this.recommendations.forEach(item => console.log(`  ${item}`));
    
    const total = this.passed.length + this.warnings.length + this.recommendations.length;
    const score = total > 0 ? Math.round((this.passed.length / total) * 100) : 0;
    
    console.log(`\n⚡ Performance Score: ${score}%`);
    
    if (score >= 90) {
      console.log('🎉 Excellent! Your app is well optimized');
    } else if (score >= 70) {
      console.log('👍 Good performance, but there\'s room for improvement');
    } else {
      console.log('⚠️ Performance needs attention');
    }

    console.log('\n📝 Next Steps:');
    console.log('1. Run "npm run build" to check bundle sizes');
    console.log('2. Use Lighthouse to audit Core Web Vitals');
    console.log('3. Monitor real user metrics in production');
    console.log('4. Implement the recommendations above');
    
    return {
      score,
      passed: this.passed.length,
      warnings: this.warnings.length,
      recommendations: this.recommendations.length
    };
  }

  // Run all performance checks
  async runAudit() {
    console.log('🚀 Starting performance audit...\n');
    
    this.checkBundleSize();
    this.checkImageOptimization();
    this.checkFontOptimization();
    this.checkLazyLoading();
    this.checkWebVitals();
    this.checkCaching();
    this.checkCompression();
    
    return this.generateReport();
  }
}

// Run the audit
if (require.main === module) {
  const auditor = new PerformanceAuditor();
  auditor.runAudit().then(report => {
    process.exit(report.warnings > 5 ? 1 : 0);
  });
}

module.exports = PerformanceAuditor;
