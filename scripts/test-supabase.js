const { Pool } = require('pg');

async function testXataConnection() {
  const pool = new Pool({
    connectionString: "postgresql://i9438d:<EMAIL>/kun:main",
    ssl: false // Xata doesn't support SSL
  });

  try {
    console.log('Testing Xata connection...');
    const result = await pool.query('SELECT version();');
    console.log('✅ Connection successful!');
    console.log('PostgreSQL version:', result.rows[0].version);
    
    // Test creating a simple table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS test_table (
        id SERIAL PRIMARY KEY,
        name TEXT
      );
    `);
    console.log('✅ Table creation successful!');
    
    // Clean up
    await pool.query('DROP TABLE IF EXISTS test_table;');
    console.log('✅ Cleanup successful!');
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
  } finally {
    await pool.end();
  }
}

testXataConnection();
