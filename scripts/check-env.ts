import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables from .env.local
config({ path: resolve(process.cwd(), '.env.local') });

console.log('🔍 Checking environment variables...');
console.log('DATABASE_URL:', process.env.DATABASE_URL);
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '[HIDDEN]' : 'NOT SET');
console.log('DB_MAX_POOL_SIZE:', process.env.DB_MAX_POOL_SIZE);
console.log('DB_SCHEMA:', process.env.DB_SCHEMA);

// Construct the URL manually to verify format
const constructedUrl = `postgresql://${process.env.DB_USER}:${process.env.DB_PASSWORD}@${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_NAME}?schema=${process.env.DB_SCHEMA}&connection_limit=${process.env.DB_MAX_POOL_SIZE}`;
console.log('\n🔧 Constructed URL:', constructedUrl);

// Check if they match
console.log('\n✅ URLs match:', process.env.DATABASE_URL === constructedUrl);
