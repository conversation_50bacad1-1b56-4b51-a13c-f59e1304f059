import { config } from 'dotenv';
import { resolve } from 'path';
import { PrismaClient } from '../src/generated/prisma';

// Load environment variables from .env.local
config({ path: resolve(process.cwd(), '.env.local') });

async function testDatabaseConnection() {
  const prisma = new PrismaClient();

  try {
    console.log('🔍 Testing PostgreSQL database connection...');
    
    // Test basic connection
    await prisma.$connect();
    console.log('✅ Successfully connected to PostgreSQL database');

    // Test a simple query
    const result = await prisma.$queryRaw`SELECT version()`;
    console.log('📊 Database version:', result);

    // Test if we can query the database schema
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `;
    console.log('📋 Existing tables:', tables);

    // Test database write capability (if tables exist)
    try {
      const userCount = await prisma.user.count();
      console.log('👥 Current user count:', userCount);
    } catch (error) {
      console.log('ℹ️  Tables not yet created (this is expected before migration)');
    }

    console.log('🎉 Database connection test completed successfully!');
    
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      
      // Provide helpful debugging information
      if (error.message.includes('ENOTFOUND')) {
        console.error('💡 DNS resolution failed. Check the database host.');
      } else if (error.message.includes('ECONNREFUSED')) {
        console.error('💡 Connection refused. Check if the database server is running and the port is correct.');
      } else if (error.message.includes('authentication failed')) {
        console.error('💡 Authentication failed. Check username and password.');
      } else if (error.message.includes('database') && error.message.includes('does not exist')) {
        console.error('💡 Database does not exist. Check the database name.');
      }
    }
    
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testDatabaseConnection();
