import { Pool } from 'pg';

// Test the database connection with URL-encoded password
const password = 'xau_3zGlhrdvuRaS3SISiFNWn1TFAvSVX1RL4';
const encodedPassword = encodeURIComponent(password);

const databaseUrl = process.env.DATABASE_URL || 
  `postgresql://i9438d:${encodedPassword}@us-east-1.sql.xata.sh/kun:main`;

// Also try with explicit SSL parameter
const databaseUrlWithSSL = process.env.DATABASE_URL || 
  `postgresql://i9438d:${encodedPassword}@us-east-1.sql.xata.sh/kun:main?sslmode=require`;

console.log('Testing database connection...');
console.log('Database URL:', databaseUrl);

async function testConnection(url, label) {
  console.log(`\n--- Testing ${label} ---`);
  console.log('URL:', url);
  
  const pool = new Pool({
    connectionString: url,
    ssl: {
      rejectUnauthorized: false,
    },
    max: 1,
    connectionTimeoutMillis: 10000,
  });

  try {
    const client = await pool.connect();
    console.log('✅ Database connection successful!');
    
    // Test a simple query
    const result = await client.query('SELECT NOW()');
    console.log('✅ Query test successful:', result.rows[0]);
    
    client.release();
    await pool.end();
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    await pool.end();
    return false;
  }
}

// Test both URLs
(async () => {
  const success1 = await testConnection(databaseUrl, 'Standard URL');
  if (!success1) {
    await testConnection(databaseUrlWithSSL, 'URL with SSL parameter');
  }
})();
